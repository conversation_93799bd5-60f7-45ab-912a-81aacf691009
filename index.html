
<!DOCTYPE html>
<html lang="">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" href="/favicon.ico" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=3, user-scalable=no"
        />
        <title>
            天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具
        </title>
        <meta
            name="description"
            content="天远数据提供婚恋评估报告、司法涉诉查询、婚姻状态查询、判决书查询、失信人、司法涉诉、企业涉诉、车辆核验等多项服务，帮助您查询婚姻信息、名下车辆、涉诉风险等，提供全面的法律与金融风险防范工具。"
        />
        <meta
            name="keywords"
            content="婚恋评估, 司法涉诉查询, 判决书查询, 婚姻状态查询, 失信人, 司法涉诉查询, 企业涉诉查询, 名下车辆核验, 车辆核验, 婚姻报告, 法律风险, 信用风险, 银行卡黑名单, 手机身份证核验, 学历核验, 智能助手"
        />
        <meta name="author" content="天远数据" />
        <meta
            name="baidu-site-verification"
            content="4d551d55896a88badef8dcdb14cf874c"
        />
        <script>
            (function (w, d, s, q, i) {
                w[q] = w[q] || [];
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s);
                j.async = true;
                j.id = "beacon-aplus";
                j.src = "https://d.alicdn.com/alilog/mlog/aplus/" + i + ".js";
                f.parentNode.insertBefore(j, f);
            })(window, document, "script", "aplus_queue", "203467608");
            aplus_queue.push({
                action: "aplus.setMetaInfo",
                arguments: ["appKey", "67aeb61b8f232a05f11365e0"],
            });
            aplus_queue.push({
                action: "aplus.setMetaInfo",
                arguments: ["aplus-waiting", "MAN"],
            });
            aplus_queue.push({
                action: "aplus.setMetaInfo",
                arguments: ["DEBUG", false],
            });
            aplus_queue.push({
                action: "aplus.setMetaInfo",
                arguments: ["aplus-idtype", "uuid"], //取值参考见附表1
            });
        </script>
        <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
        <script>
            window.jWeixin = window.wx;
            delete window.wx;
        </script>
        <style>
            /* 基础样式 */
            html {
                font-size: 16px;
                -webkit-text-size-adjust: 100%;
                -moz-text-size-adjust: 100%;
                     text-size-adjust: 100%;
            }

            body {
                margin: 0;
                padding: 0;
                overflow-x: hidden;
                min-width: 320px;
            }

            /* 确保所有元素在缩放时保持相对位置 */
            * {
                box-sizing: border-box;
            }

            /* 防止水平滚动 */
            #app {
                width: 100%;
                max-width: 100vw;
                overflow-x: hidden;
            }

            /* 响应式字体大小 */
            @media screen and (max-width: 480px) {
                html {
                    font-size: 14px;
                }
            }

            @media screen and (min-width: 481px) and (max-width: 768px) {
                html {
                    font-size: 15px;
                }
            }

            /* 加载页面样式 */
            #app-loading {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                background-color: #fff;
                z-index: 9999;
                font-family: Arial, sans-serif;
                color: #666;
            }

            /* 加载动画 */
            .loading-spinner {
                width: 50px;
                height: 50px;
                border: 5px solid #ccc;
                border-top: 5px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 16px;
                /* 与文字的间距 */
            }

            /* 文字样式 */
            .loading-text {
                font-size: 16px;
                color: #666;
            }

            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }


        </style>
      <script type="module" crossorigin src="/assets/index-B4d74vWj.js"></script>
      <link rel="stylesheet" crossorigin href="/assets/index-ptDfl3Jv.css">
    </head>

    <body>
        <div id="app-loading">
            <div class="loading-spinner"></div>
            <div class="loading-text">加载中</div>
        </div>
        <div id="app"></div>
        



        
    </body>
</html>
