/**
 * 格式化日期
 * @param {string} dateStr - 日期字符串
 * @returns {string} 格式化后的日期
 */
export const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  
  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) return dateStr
    
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return dateStr
  }
}

/**
 * 格式化金额
 * @param {number|string} amount - 金额
 * @returns {string} 格式化后的金额
 */
export const formatAmount = (amount) => {
  if (!amount || amount === 0) return '-'
  
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  if (isNaN(num)) return '-'
  
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(num)
}

/**
 * 获取案件类型颜色
 * @param {string} caseType - 案件类型
 * @returns {string} 颜色类名
 */
export const getCaseTypeColor = (caseType) => {
  if (!caseType) return 'text-gray-500'

  const type = caseType.toLowerCase()

  if (type.includes('刑事') || type.includes('犯罪')) {
    return 'text-red-600'
  } else if (type.includes('民事') || type.includes('合同') || type.includes('债务') || type.includes('借款')) {
    return 'text-orange-600'
  } else if (type.includes('行政')) {
    return 'text-blue-600'
  } else {
    return 'text-gray-600'
  }
}

/**
 * 获取诉讼地位颜色
 * @param {string} position - 诉讼地位
 * @returns {string} 颜色类名
 */
export const getPositionColor = (position) => {
  if (!position) return 'text-gray-500'
  
  if (position.includes('被告') || position.includes('被执行人') || position.includes('被申请人')) {
    return 'text-red-600'
  } else if (position.includes('原告') || position.includes('申请人')) {
    return 'text-green-600'
  } else if (position.includes('第三人')) {
    return 'text-blue-600'
  } else {
    return 'text-gray-600'
  }
}

/**
 * 获取胜诉估计颜色
 * @param {string} victory - 胜诉估计
 * @returns {string} 颜色类名
 */
export const getVictoryColor = (victory) => {
  if (!victory) return 'text-gray-500'
  
  if (victory.includes('胜') || victory.includes('有利')) {
    return 'text-green-600'
  } else if (victory.includes('败') || victory.includes('不利')) {
    return 'text-red-600'
  } else {
    return 'text-orange-600'
  }
}

/**
 * 截取文本
 * @param {string} text - 原文本
 * @param {number} maxLength - 最大长度
 * @returns {string} 截取后的文本
 */
export const truncateText = (text, maxLength = 50) => {
  if (!text) return '-'
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

/**
 * 格式化案由树形结构
 * @param {string} caseTree - 案由树形结构（逗号分隔）
 * @returns {string} 格式化后的案由
 */
export const formatCaseTree = (caseTree) => {
  if (!caseTree) return '-'

  // 将逗号分隔的字符串转换为面包屑导航格式
  const parts = caseTree.split(',').filter(part => part.trim())
  if (parts.length <= 1) return caseTree

  return parts.join(' > ')
}

/**
 * 获取案由简称
 * @param {string} caseTree - 案由树形结构
 * @returns {string} 案由简称
 */
export const getCaseShortName = (caseTree) => {
  if (!caseTree) return '-'

  const parts = caseTree.split(',').filter(part => part.trim())
  // 返回最后一级案由作为简称
  return parts[parts.length - 1] || caseTree
}

/**
 * 获取风险等级
 * @param {Array} casesList - 案件列表
 * @returns {Object} 风险等级信息
 */
export const getRiskLevel = (casesList) => {
  if (!casesList || casesList.length === 0) {
    return {
      level: 'safe',
      label: '无风险',
      color: 'green',
      count: 0
    }
  }
  
  const count = casesList.length
  let highRiskCount = 0
  let mediumRiskCount = 0
  
  casesList.forEach(item => {
    // 根据案件类型和诉讼地位判断风险等级
    const caseType = item.n_ajlx || ''
    const position = item.n_ssdw || ''
    
    if (caseType.includes('刑事') || position.includes('被执行人')) {
      highRiskCount++
    } else if (position.includes('被告') || caseType.includes('债务')) {
      mediumRiskCount++
    }
  })
  
  if (highRiskCount > 0) {
    return {
      level: 'high',
      label: '高风险',
      color: 'red',
      count: count,
      highRiskCount
    }
  } else if (mediumRiskCount > 0) {
    return {
      level: 'medium',
      label: '中风险',
      color: 'orange',
      count: count,
      mediumRiskCount
    }
  } else if (count > 0) {
    return {
      level: 'low',
      label: '低风险',
      color: 'blue',
      count: count
    }
  }
  
  return {
    level: 'safe',
    label: '无风险',
    color: 'green',
    count: 0
  }
}
