import{_ as c,d as s,c as l,a as t,q as a}from"./index-B4d74vWj.js";const x={class:"card"},u={key:0,class:"bg-green-100 text-green-700 p-4 rounded-lg"},p={key:1,class:"bg-red-100 text-red-700 p-4 rounded-lg"},m={key:2,class:"bg-yellow-100 text-yellow-700 p-4 rounded-lg"},f={key:3,class:"bg-blue-100 text-blue-700 p-4 rounded-lg"},b={__name:"CG03HZ01",props:{data:{type:Object,required:!0},params:{type:Object,required:!0}},setup(d){return(y,e)=>{var o,r,n,i;return s(),l("div",x,[((o=d.data)==null?void 0:o.filterType)==="0"?(s(),l("div",u,e[0]||(e[0]=[t("h3",{class:"text-xl font-semibold"},"安全号码",-1),t("p",{class:"text-sm"},"该手机号码为安全号码，没有发现任何风险。",-1)]))):a("",!0),((r=d.data)==null?void 0:r.filterType)==="1"?(s(),l("div",p,e[1]||(e[1]=[t("h3",{class:"text-xl font-semibold"},"高危",-1),t("p",{class:"text-sm"},"该手机号码存在较高风险，请谨慎处理。",-1)]))):a("",!0),((n=d.data)==null?void 0:n.filterType)==="2"?(s(),l("div",m,e[2]||(e[2]=[t("h3",{class:"text-xl font-semibold"},"中危",-1),t("p",{class:"text-sm"},"该手机号码存在一定风险，请留意相关信息。",-1)]))):a("",!0),((i=d.data)==null?void 0:i.filterType)==="3"?(s(),l("div",f,e[3]||(e[3]=[t("h3",{class:"text-xl font-semibold"},"低危",-1),t("p",{class:"text-sm"},"该手机号码风险较低，但仍需关注。",-1)]))):a("",!0)])}}},_=c(b,[["__scopeId","data-v-cf57e1a9"]]);export{_ as default};
