import{ar as de,as as X,ad as ue,at as Ne,r as C,au as Oe,av as He,aw as Le,o as Ee,ax as fe,B as W,C as Z,a9 as ve,a1 as he,e as I,ay as be,a2 as me,az as De,N as $,ab as ge,ah as J,Y as A,ag as O,aA as we,aB as Me,g,U as z,E as ye,ac as H,G as p,aC as q,f as We,a4 as ee,aD as Ze,aE as Fe,aF as Ue,D as M,V as ie,an as Q,aq as Ve,aG as Ke,aH as oe,aI as Ye,aJ as je,aK as qe,ak as Ge,al as Xe,a3 as Je,aL as Qe,aM as pe,aN as et,w as tt,aO as nt,n as at,aP as lt,aQ as it,a7 as ot,a6 as se,aR as st}from"./index-B4d74vWj.js";import{u as xe}from"./use-id-BHlr6Txk.js";import{T as rt}from"./use-tab-status-Co3jcf_0.js";import{a as ct,S as dt}from"./index-Z9L7R3xo.js";function ut(e,a,o){let l,r=0;const t=e.scrollLeft,s=o===0?1:Math.round(o*1e3/16);let c=t;function h(){de(l)}function m(){c+=(a-t)/s,e.scrollLeft=c,++r<s&&(l=X(m))}return m(),h}function ft(e,a,o,l){let r,t=ue(e);const s=t<a,c=o===0?1:Math.round(o*1e3/16),h=(a-t)/c;function m(){de(r)}function y(){t+=h,(s&&t>a||!s&&t<a)&&(t=a),Ne(e,t),s&&t<a||!s&&t>a?r=X(y):l&&(r=X(l))}return y(),m}function vt(){const e=C([]),a=[];return Oe(()=>{e.value=[]}),[e,l=>(a[l]||(a[l]=r=>{e.value[l]=r}),a[l])]}function Te(e,a){if(!He||!window.IntersectionObserver)return;const o=new IntersectionObserver(t=>{a(t[0].intersectionRatio>0)},{root:document.body}),l=()=>{e.value&&o.observe(e.value)},r=()=>{e.value&&o.unobserve(e.value)};Le(r),Ee(r),fe(l)}const[ht,bt]=W("sticky"),mt={zIndex:z,position:ye("top"),container:Object,offsetTop:H(0),offsetBottom:H(0)};var gt=Z({name:ht,props:mt,emits:["scroll","change"],setup(e,{emit:a,slots:o}){const l=C(),r=ve(l),t=he({fixed:!1,width:0,height:0,transform:0}),s=C(!1),c=I(()=>be(e.position==="top"?e.offsetTop:e.offsetBottom)),h=I(()=>{if(s.value)return;const{fixed:f,height:S,width:u}=t;if(f)return{width:`${u}px`,height:`${S}px`}}),m=I(()=>{if(!t.fixed||s.value)return;const f=me(De(e.zIndex),{width:`${t.width}px`,height:`${t.height}px`,[e.position]:`${c.value}px`});return t.transform&&(f.transform=`translate3d(0, ${t.transform}px, 0)`),f}),y=f=>a("scroll",{scrollTop:f,isFixed:t.fixed}),k=()=>{if(!l.value||J(l))return;const{container:f,position:S}=e,u=O(l),T=ue(window);if(t.width=u.width,t.height=u.height,S==="top")if(f){const d=O(f),B=d.bottom-c.value-t.height;t.fixed=c.value>u.top&&d.bottom>0,t.transform=B<0?B:0}else t.fixed=c.value>u.top;else{const{clientHeight:d}=document.documentElement;if(f){const B=O(f),b=d-B.top-c.value-t.height;t.fixed=d-c.value<u.bottom&&d>B.top,t.transform=b<0?-b:0}else t.fixed=d-c.value<u.bottom}y(T)};return $(()=>t.fixed,f=>a("change",f)),ge("scroll",k,{target:r,passive:!0}),Te(l,k),$([we,Me],()=>{!l.value||J(l)||!t.fixed||(s.value=!0,A(()=>{const f=O(l);t.width=f.width,t.height=f.height,s.value=!1}))}),()=>{var f;return g("div",{ref:l,style:h.value},[g("div",{class:bt({fixed:t.fixed&&!s.value}),style:m.value},[(f=o.default)==null?void 0:f.call(o)])])}}});const wt=p(gt),[yt,re]=W("tabs");var xt=Z({name:yt,props:{count:q(Number),inited:Boolean,animated:Boolean,duration:q(z),swipeable:Boolean,lazyRender:Boolean,currentIndex:q(Number)},emits:["change"],setup(e,{emit:a,slots:o}){const l=C(),r=c=>a("change",c),t=()=>{var c;const h=(c=o.default)==null?void 0:c.call(o);return e.animated||e.swipeable?g(ct,{ref:l,loop:!1,class:re("track"),duration:+e.duration*1e3,touchable:e.swipeable,lazyRender:e.lazyRender,showIndicators:!1,onChange:r},{default:()=>[h]}):h},s=c=>{const h=l.value;h&&h.state.active!==c&&h.swipeTo(c,{immediate:!e.inited})};return $(()=>e.currentIndex,s),We(()=>{s(e.currentIndex)}),ee({swipeRef:l}),()=>g("div",{class:re("content",{animated:e.animated||e.swipeable})},[t()])}});const[Se,j]=W("tabs"),Tt={type:ye("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:H(0),duration:H(.3),animated:Boolean,ellipsis:M,swipeable:Boolean,scrollspy:Boolean,offsetTop:H(0),background:String,lazyRender:M,showHeader:M,lineWidth:z,lineHeight:z,beforeChange:Function,swipeThreshold:H(5),titleActiveColor:String,titleInactiveColor:String},Ce=Symbol(Se);var St=Z({name:Se,props:Tt,emits:["change","scroll","rendered","clickTab","update:active"],setup(e,{emit:a,slots:o}){let l,r,t,s,c;const h=C(),m=C(),y=C(),k=C(),f=xe(),S=ve(h),[u,T]=vt(),{children:d,linkChildren:B}=Ze(Ce),b=he({inited:!1,position:"",lineStyle:{},currentIndex:-1}),L=I(()=>d.length>+e.swipeThreshold||!e.ellipsis||e.shrink),F=I(()=>({borderColor:e.color,background:e.background})),E=(n,i)=>{var v;return(v=n.name)!=null?v:i},U=I(()=>{const n=d[b.currentIndex];if(n)return E(n,b.currentIndex)}),N=I(()=>be(e.offsetTop)),te=I(()=>e.sticky?N.value+l:0),D=n=>{const i=m.value,v=u.value;if(!L.value||!i||!v||!v[b.currentIndex])return;const x=v[b.currentIndex].$el,w=x.offsetLeft-(i.offsetWidth-x.offsetWidth)/2;s&&s(),s=ut(i,w,n?0:+e.duration)},_=()=>{const n=b.inited;A(()=>{const i=u.value;if(!i||!i[b.currentIndex]||e.type!=="line"||J(h.value))return;const v=i[b.currentIndex].$el,{lineWidth:x,lineHeight:w}=e,R=v.offsetLeft+v.offsetWidth/2,P={width:ie(x),backgroundColor:e.color,transform:`translateX(${R}px) translateX(-50%)`};if(n&&(P.transitionDuration=`${e.duration}s`),Q(w)){const Y=ie(w);P.height=Y,P.borderRadius=Y}b.lineStyle=P})},Re=n=>{const i=n<b.currentIndex?-1:1;for(;n>=0&&n<d.length;){if(!d[n].disabled)return n;n+=i}},V=(n,i)=>{const v=Re(n);if(!Q(v))return;const x=d[v],w=E(x,v),R=b.currentIndex!==null;b.currentIndex!==v&&(b.currentIndex=v,i||D(),_()),w!==e.active&&(a("update:active",w),R&&a("change",w,x.title)),t&&!e.scrollspy&&Ke(Math.ceil(oe(h.value)-N.value))},K=(n,i)=>{const v=d.find((w,R)=>E(w,R)===n),x=v?d.indexOf(v):0;V(x,i)},ne=(n=!1)=>{if(e.scrollspy){const i=d[b.currentIndex].$el;if(i&&S.value){const v=oe(i,S.value)-te.value;r=!0,c&&c(),c=ft(S.value,v,n?0:+e.duration,()=>{r=!1})}}},Ie=(n,i,v)=>{const{title:x,disabled:w}=d[i],R=E(d[i],i);w||(Ye(e.beforeChange,{args:[R],done:()=>{V(i),ne()}}),je(n)),a("clickTab",{name:R,title:x,event:v,disabled:w})},ke=n=>{t=n.isFixed,a("scroll",n)},Be=n=>{A(()=>{K(n),ne(!0)})},$e=()=>{for(let n=0;n<d.length;n++){const{top:i}=O(d[n].$el);if(i>te.value)return n===0?0:n-1}return d.length-1},_e=()=>{if(e.scrollspy&&!r){const n=$e();V(n)}},Pe=()=>{if(e.type==="line"&&d.length)return g("div",{class:j("line"),style:b.lineStyle},null)},ae=()=>{var n,i,v;const{type:x,border:w,sticky:R}=e,P=[g("div",{ref:R?void 0:y,class:[j("wrap"),{[Ve]:x==="line"&&w}]},[g("div",{ref:m,role:"tablist",class:j("nav",[x,{shrink:e.shrink,complete:L.value}]),style:F.value,"aria-orientation":"horizontal"},[(n=o["nav-left"])==null?void 0:n.call(o),d.map(Y=>Y.renderTitle(Ie)),Pe(),(i=o["nav-right"])==null?void 0:i.call(o)])]),(v=o["nav-bottom"])==null?void 0:v.call(o)];return R?g("div",{ref:y},[P]):P},le=()=>{_(),A(()=>{var n,i;D(!0),(i=(n=k.value)==null?void 0:n.swipeRef.value)==null||i.resize()})};$(()=>[e.color,e.duration,e.lineWidth,e.lineHeight],_),$(we,le),$(()=>e.active,n=>{n!==U.value&&K(n)}),$(()=>d.length,()=>{b.inited&&(K(e.active),_(),A(()=>{D(!0)}))});const Ae=()=>{K(e.active,!0),A(()=>{b.inited=!0,y.value&&(l=O(y.value).height),D(!0)})},ze=(n,i)=>a("rendered",n,i);return ee({resize:le,scrollTo:Be}),Fe(_),Ue(_),fe(Ae),Te(h,_),ge("scroll",_e,{target:S,passive:!0}),B({id:f,props:e,setLine:_,scrollable:L,onRendered:ze,currentName:U,setTitleRefs:T,scrollIntoView:D}),()=>g("div",{ref:h,class:j([e.type])},[e.showHeader?e.sticky?g(wt,{container:h.value,offsetTop:N.value,onScroll:ke},{default:()=>[ae()]}):ae():null,g(xt,{ref:k,count:d.length,inited:b.inited,animated:e.animated,duration:e.duration,swipeable:e.swipeable,lazyRender:e.lazyRender,currentIndex:b.currentIndex,onChange:V},{default:()=>{var n;return[(n=o.default)==null?void 0:n.call(o)]}})])}});const[Ct,ce]=W("tab"),Rt=Z({name:Ct,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:z,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:M},setup(e,{slots:a}){const o=I(()=>{const r={},{type:t,color:s,disabled:c,isActive:h,activeColor:m,inactiveColor:y}=e;s&&t==="card"&&(r.borderColor=s,c||(h?r.backgroundColor=s:r.color=s));const f=h?m:y;return f&&(r.color=f),r}),l=()=>{const r=g("span",{class:ce("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||Q(e.badge)&&e.badge!==""?g(qe,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[r]}):r};return()=>g("div",{id:e.id,role:"tab",class:[ce([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:o.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[It,G]=W("tab"),kt=me({},Ge,{dot:Boolean,name:z,badge:z,title:String,disabled:Boolean,titleClass:Xe,titleStyle:[String,Object],showZeroBadge:M});var Bt=Z({name:It,props:kt,setup(e,{slots:a}){const o=xe(),l=C(!1),r=st(),{parent:t,index:s}=Je(Ce);if(!t)return;const c=()=>{var u;return(u=e.name)!=null?u:s.value},h=()=>{l.value=!0,t.props.lazyRender&&A(()=>{t.onRendered(c(),e.title)})},m=I(()=>{const u=c()===t.currentName.value;return u&&!l.value&&h(),u}),y=C(""),k=C("");Qe(()=>{const{titleClass:u,titleStyle:T}=e;y.value=u?at(u):"",k.value=T&&typeof T!="string"?lt(it(T)):T});const f=u=>g(Rt,ot({key:o,id:`${t.id}-${s.value}`,ref:t.setTitleRefs(s.value),style:k.value,class:y.value,isActive:m.value,controls:o,scrollable:t.scrollable.value,activeColor:t.props.titleActiveColor,inactiveColor:t.props.titleInactiveColor,onClick:T=>u(r.proxy,s.value,T)},se(t.props,["type","color","shrink"]),se(e,["dot","badge","title","disabled","showZeroBadge"])),{title:a.title}),S=C(!m.value);return $(m,u=>{u?S.value=!1:pe(()=>{S.value=!0})}),$(()=>e.title,()=>{t.setLine(),t.scrollIntoView()}),et(rt,m),ee({id:o,renderTitle:f}),()=>{var u;const T=`${t.id}-${s.value}`,{animated:d,swipeable:B,scrollspy:b,lazyRender:L}=t.props;if(!a.default&&!d)return;const F=b||m.value;if(d||B)return g(dt,{id:o,role:"tabpanel",class:G("panel-wrapper",{inactive:S.value}),tabindex:m.value?0:-1,"aria-hidden":!m.value,"aria-labelledby":T,"data-allow-mismatch":"attribute"},{default:()=>{var N;return[g("div",{class:G("panel")},[(N=a.default)==null?void 0:N.call(a)])]}});const U=l.value||b||!L?(u=a.default)==null?void 0:u.call(a):null;return tt(g("div",{id:o,role:"tabpanel",class:G("panel"),tabindex:F?0:-1,"aria-labelledby":T,"data-allow-mismatch":"attribute"},[U]),[[nt,F]])}}});const zt=p(Bt),Nt=p(St);export{Nt as T,zt as a,vt as u};
