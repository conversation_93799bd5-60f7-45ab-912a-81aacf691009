<template>
  <div class="case-detail">
    <!-- 案件基本信息 -->
    <div class="flex items-start justify-between mb-3">
      <div class="flex-1">
        <div class="flex items-center mb-2">
          <span class="text-base font-semibold text-gray-800 mr-2">
            {{ caseData.c_ah || '案号未知' }}
          </span>
          <span 
            v-if="caseData.n_ajlx"
            :class="['text-xs px-2 py-1 rounded-full font-medium', getCaseTypeClass()]"
          >
            {{ caseData.n_ajlx }}
          </span>
        </div>
        
        <div class="text-sm text-gray-600 space-y-1">
          <div v-if="caseData.n_jbfy">
            <span class="text-gray-500">审理法院：</span>
            <span>{{ caseData.n_jbfy }}</span>
            <span v-if="caseData.n_jbfy_cj" class="ml-1 text-xs text-gray-400">
              ({{ caseData.n_jbfy_cj }})
            </span>
          </div>
          
          <div v-if="caseData.c_ssdy">
            <span class="text-gray-500">所属地域：</span>
            <span>{{ caseData.c_ssdy }}</span>
          </div>
        </div>
      </div>
      
      <!-- 诉讼地位 -->
      <div v-if="caseData.n_ssdw" class="ml-4">
        <span 
          :class="['text-xs px-2 py-1 rounded-full font-medium', getPositionClass()]"
        >
          {{ caseData.n_ssdw }}
        </span>
      </div>
    </div>
    
    <!-- 案由信息 -->
    <div class="space-y-3 mb-3">
      <div v-if="caseData.n_laay">
        <div class="text-xs text-gray-500 mb-1">立案案由</div>
        <div class="text-sm text-gray-800">{{ caseData.n_laay }}</div>
        <div v-if="caseData.n_laay_tag" class="text-xs text-blue-600 mt-1">
          标签：{{ caseData.n_laay_tag }}
        </div>
        <div v-if="caseData.n_laay_tree" class="text-xs text-gray-500 mt-1">
          分类：{{ formatCaseTree(caseData.n_laay_tree) }}
        </div>
      </div>

      <div v-if="caseData.n_jaay && caseData.n_jaay !== caseData.n_laay">
        <div class="text-xs text-gray-500 mb-1">结案案由</div>
        <div class="text-sm text-gray-800">{{ caseData.n_jaay }}</div>
        <div v-if="caseData.n_jaay_tag" class="text-xs text-blue-600 mt-1">
          标签：{{ caseData.n_jaay_tag }}
        </div>
        <div v-if="caseData.n_jaay_tree" class="text-xs text-gray-500 mt-1">
          分类：{{ formatCaseTree(caseData.n_jaay_tree) }}
        </div>
      </div>
    </div>
    
    <!-- 时间和金额信息 -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3 text-sm">
      <div v-if="caseData.d_larq">
        <div class="text-xs text-gray-500 mb-1">立案时间</div>
        <div class="text-gray-800">{{ formatDate(caseData.d_larq) }}</div>
      </div>
      
      <div v-if="caseData.d_jarq">
        <div class="text-xs text-gray-500 mb-1">结案时间</div>
        <div class="text-gray-800">{{ formatDate(caseData.d_jarq) }}</div>
      </div>
      
      <div v-if="caseData.n_jabdje">
        <div class="text-xs text-gray-500 mb-1">标的金额</div>
        <div class="text-gray-800 font-medium">{{ formatAmount(caseData.n_jabdje) }}</div>
      </div>
      
      <div v-if="caseData.n_jafs">
        <div class="text-xs text-gray-500 mb-1">结案方式</div>
        <div class="text-gray-800">{{ caseData.n_jafs }}</div>
      </div>
    </div>
    
    <!-- 审理信息 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3 text-sm">
      <div v-if="caseData.n_slcx">
        <div class="text-xs text-gray-500 mb-1">审理程序</div>
        <div class="text-gray-800">{{ caseData.n_slcx }}</div>
      </div>
      
      <div v-if="caseData.n_ajjzjd">
        <div class="text-xs text-gray-500 mb-1">案件阶段</div>
        <div class="text-gray-800">{{ caseData.n_ajjzjd }}</div>
      </div>
      
      <div v-if="caseData.n_pj_victory">
        <div class="text-xs text-gray-500 mb-1">胜诉估计</div>
        <div :class="['font-medium', getVictoryClass()]">{{ caseData.n_pj_victory }}</div>
      </div>
    </div>
    
    <!-- 当事人和判决结果 -->
    <div v-if="caseData.c_gkws_dsr || caseData.c_gkws_pjjg" class="space-y-2">
      <div v-if="caseData.c_gkws_dsr" class="text-sm">
        <span class="text-gray-500">当事人：</span>
        <span class="text-gray-800">{{ truncateText(caseData.c_gkws_dsr, 100) }}</span>
      </div>
      
      <div v-if="caseData.c_gkws_pjjg" class="text-sm">
        <span class="text-gray-500">判决结果：</span>
        <span class="text-gray-800">{{ truncateText(caseData.c_gkws_pjjg, 150) }}</span>
      </div>
    </div>
    
    <!-- 审理方式信息 -->
    <div v-if="caseData.c_slfsxx" class="mt-3 pt-3 border-t border-gray-100">
      <div class="text-xs text-gray-500 mb-1">审理方式</div>
      <div class="text-sm text-gray-600">{{ caseData.c_slfsxx }}</div>
    </div>
  </div>
</template>

<script setup>
import {
  formatDate,
  formatAmount,
  getCaseTypeColor,
  getPositionColor,
  getVictoryColor,
  truncateText,
  formatCaseTree
} from '@/utils'

const props = defineProps({
  caseData: {
    type: Object,
    required: true
  }
})

const getCaseTypeClass = () => {
  const color = getCaseTypeColor(props.caseData.n_ajlx)
  if (color.includes('red')) {
    return 'bg-red-100 text-red-800'
  } else if (color.includes('orange')) {
    return 'bg-orange-100 text-orange-800'
  } else if (color.includes('blue')) {
    return 'bg-blue-100 text-blue-800'
  } else {
    return 'bg-gray-100 text-gray-800'
  }
}

const getPositionClass = () => {
  const color = getPositionColor(props.caseData.n_ssdw)
  if (color.includes('red')) {
    return 'bg-red-100 text-red-800'
  } else if (color.includes('green')) {
    return 'bg-green-100 text-green-800'
  } else if (color.includes('blue')) {
    return 'bg-blue-100 text-blue-800'
  } else {
    return 'bg-gray-100 text-gray-800'
  }
}

const getVictoryClass = () => {
  return getVictoryColor(props.caseData.n_pj_victory)
}
</script>

<style scoped>
.case-detail {
  @apply bg-white;
}
</style>
