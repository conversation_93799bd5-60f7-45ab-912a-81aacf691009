import{_ as Ft,r as y,N as ut,e as pt,d as C,c as k,F as wt,b as Rt,n as ct,p as m,t as f,a as l,aQ as Ht,f as Yt,Y as Qt,X as Jt,h as yt,q as ht,S as Kt,g as F,O as Lt,A as mt}from"./index-B4d74vWj.js";import{a as _t,i as Et}from"./BaseReport-ClT0Rj5U.js";import{L as It}from"./LTable-jtVDM3iJ.js";/* empty css              *//* empty css              */import"./index-DsKI3WGL.js";import"./use-id-BHlr6Txk.js";import"./use-tab-status-Co3jcf_0.js";import"./index-Z9L7R3xo.js";const Zt={class:"relative flex"},Tt=["onClick"],el={__name:"LButtonGroup",props:{type:{type:String,default:"purple-pink"},options:{type:Array,required:!0},modelValue:{type:String,default:""}},emits:["update:modelValue"],setup(dt,{emit:gt}){const A=dt,O=gt,z=y(A.modelValue);ut(()=>A.modelValue,S=>{z.value=S});const R=pt(()=>{switch(A.type){case"blue-green":return"bg-gradient-to-r from-blue-400 via-green-500 to-teal-500";case"orange-yellow":return"bg-gradient-to-r from-orange-400 via-yellow-500 to-yellow-600";case"red-purple":return"bg-gradient-to-r from-red-500 via-purple-500 to-purple-600";default:return"bg-gradient-to-r from-purple-400 via-pink-500 to-red-500"}}),D=pt(()=>{const S=A.options.findIndex(r=>r.value===z.value);return{width:`${100/A.options.length}%`,transform:`translateX(${S*100}%)`}});function K(S){z.value=S.value,O("update:modelValue",S.value)}return(S,p)=>(C(),k("div",Zt,[(C(!0),k(wt,null,Rt(dt.options,(r,N)=>(C(),k("div",{key:N,class:ct(["flex-1 shrink-0 cursor-pointer py-2 text-center text-size-sm font-bold transition-transform duration-200 ease-in-out",{"text-gray-900":m(z)===r.value,"text-gray-500":m(z)!==r.value}]),onClick:L=>K(r)},f(r.label),11,Tt))),128)),l("div",{class:ct(["absolute bottom-0 h-[3px] rounded transition-all duration-300",m(R)]),style:Ht(m(D))},null,6)]))}},tl=Ft(el,[["__scopeId","data-v-e42a02d4"]]),ll={class:"card"},ol={class:"flex flex-col gap-y-6"},nl={class:"p-6 bg-white rounded-lg shadow-sm border border-gray-100 relative overflow-hidden"},sl={class:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 relative z-10"},al={class:"flex flex-wrap gap-6 w-full"},rl={key:0,class:"flex-1 flex rounded-md shadow-sm relative"},il={key:0,class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},ul={key:1,class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},dl={key:0,class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},ml={key:1,class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},pl={class:"bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-4 relative overflow-hidden"},cl={class:"grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 relative z-10"},gl={class:"bg-white rounded-lg p-3 md:p-5 shadow-sm border border-gray-100 transform transition-transform duration-300 hover:shadow-md hover:-translate-y-1"},vl={class:"flex items-start"},xl={class:"text-lg md:text-2xl font-bold mt-1 text-gray-800"},bl={class:"bg-white rounded-lg p-3 md:p-5 shadow-sm border border-gray-100 transform transition-transform duration-300 hover:shadow-md hover:-translate-y-1"},fl={class:"flex items-start"},yl={class:"text-lg md:text-2xl font-bold mt-1 text-gray-800"},hl={class:"bg-white rounded-lg p-3 md:p-5 shadow-sm border border-gray-100 transform transition-transform duration-300 hover:shadow-md hover:-translate-y-1"},_l={class:"flex items-start"},wl={class:"text-lg md:text-2xl font-bold mt-1 text-gray-800"},Cl={class:"bg-white rounded-lg p-3 md:p-5 shadow-sm border border-gray-100 transform transition-transform duration-300 hover:shadow-md hover:-translate-y-1"},kl={class:"flex items-start"},Al={class:"text-lg md:text-2xl font-bold mt-1 text-gray-800"},Ol={class:"grid grid-cols-1 lg:grid-cols-2 gap-4"},$l={class:"relative"},Dl={class:"mb-2 flex justify-between items-center"},Sl={class:"overflow-x-auto"},Ml={class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm font-medium whitespace-nowrap"},zl={class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm text-center"},Nl={class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm text-center"},Vl={class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm text-center"},jl={class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm text-center"},Bl={class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm text-center"},Kl={class:"mt-4 bg-blue-50 p-3 rounded-lg text-sm"},Ll={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2"},El={class:"mb-2"},Il={class:"overflow-x-auto"},Fl={class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm font-medium whitespace-nowrap"},Rl={class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm text-center"},Hl={class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm text-center"},Wl={class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm text-center"},Gl={class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm text-center"},Pl={class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm text-center"},ql={key:1,class:"flex items-center justify-center h-60 bg-gray-50 rounded-lg"},Ul={__name:"CBankLoanApplication",props:{data:{type:Object,required:!0}},setup(dt){const gt=dt,{data:A}=gt,O=y(null),z=y(null),R=y(null),D=y(null),K=y({}),S=y({color:["#4B96FF","#3CA272","#EE6666","#FAC858","#73C0DE","#B280E9","#FF8463"],idColor:"#4B96FF",cellColor:"#3CA272"}),p=y({id:[],cell:[]}),r=y({id:[],cell:[]}),N=y({id:{totalApplyCount:[],totalOrgCount:[]},cell:{totalApplyCount:[],totalOrgCount:[]}});y([{label:"近7日",value:0},{label:"近1月",value:1},{label:"近3月",value:2},{label:"近6月",value:3},{label:"近1年",value:4}]),y([{label:"身份证匹配数据",value:"id"},{label:"手机号匹配数据",value:"cell"}]);const L=y(0);y(0);const b=y("id"),$=y("apply"),Ct=y([{label:"申请次数",value:"apply"},{label:"申请机构数",value:"org"}]),vt={d7:"近7日",m1:"近1月",m3:"近3月",m6:"近6月",m12:"近1年"},M={bank:"银行借贷",mc:"小额贷款",cf:"消费分期",ca:"现金分期",rel:"信用卡相关",af:"汽车金融",other:"其他借贷"}``,kt={bank:[{code:"bank",name:"银行借贷机构"}],mc:[{code:"nbank_mc",name:"小贷机构"},{code:"nbank_nsloan",name:"持牌网络小贷"},{code:"nbank_sloan",name:"持牌小贷机构"},{code:"pdl",name:"线上小额现金贷"}],cf:[{code:"nbank_cf",name:"消费类分期机构"},{code:"coon",name:"线上消费分期"},{code:"cooff",name:"线下消费分期"}],ca:[{code:"nbank_ca",name:"现金类分期机构"},{code:"caon",name:"线上现金分期"},{code:"caoff",name:"线下现金分期"},{code:"nbank_com",name:"代偿类分期机构"}],rel:[{code:"rel",name:"信用卡（类信用卡）"}],af:[{code:"af",name:"汽车金融"},{code:"nbank_autofin",name:"持牌汽车金融机构"}],other:[{code:"nbank_p2p",name:"改制机构"},{code:"nbank_cons",name:"持牌消费金融机构"},{code:"nbank_finlea",name:"持牌融资租赁机构"},{code:"nbank_oth",name:"其他非银借贷类型申请"},{code:"nbank_else",name:"其他非银类型申请"},{code:"oth",name:"其他银行借贷类型申请"},{code:"else",name:"其他银行类型申请"}]},Wt={week:[{code:"bank_week",name:"周末银行"},{code:"nbank_week",name:"周末非银"}],night:[{code:"bank_night",name:"夜间银行"},{code:"nbank_night",name:"夜间非银"}]};function xt(){var t;if(!z.value)return;O.value||(O.value=Et(z.value));const e={color:S.value.color,tooltip:{trigger:"axis",axisPointer:{type:"shadow",shadowStyle:{color:"rgba(0,0,0,0.05)"}},textStyle:{fontSize:12},backgroundColor:"rgba(50,50,50,0.9)",borderRadius:4,shadowColor:"rgba(0,0,0,0.3)",shadowBlur:10},grid:{left:"3%",right:"4%",bottom:"10%",containLabel:!0},xAxis:{type:"category",data:K.value.categories||[],axisLabel:{interval:0,rotate:0,fontSize:12,color:"#666"},axisLine:{lineStyle:{color:"#ddd"}}},yAxis:{type:"value",name:"数量",nameTextStyle:{color:"#666",fontSize:12},splitLine:{lineStyle:{type:"dashed",color:"#eee"}}},legend:{data:((t=K.value.series)==null?void 0:t.map(i=>i.name))||[],bottom:"0%",textStyle:{fontSize:12},selectedMode:!0},series:(K.value.series||[]).map(i=>({name:i.name,type:"bar",data:i.data||[],barMaxWidth:50,barGap:"30%",label:{show:!0,position:"top",formatter:"{c}",fontSize:12,fontWeight:"bold"},itemStyle:{borderRadius:[3,3,0,0],shadowColor:"rgba(0,0,0,0.2)",shadowBlur:8,shadowOffsetX:2,shadowOffsetY:2}}))};O.value.setOption(e)}function At(){var c,g;if(!R.value)return;D.value||(D.value=Et(R.value));const e=((g=(c=bt.value)==null?void 0:c[b.value])==null?void 0:g[$.value])||[];if(!e||e.length===0){const d={title:{text:"暂无数据",left:"center",top:"center"},series:[{type:"pie",radius:["40%","70%"],data:[]}]};D.value.setOption(d);return}const t=e.map(d=>({name:d.label,value:d.value})),i={color:S.value.color,tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:0,top:"center",data:t.map(d=>d.name)},series:[{name:$.value==="apply"?"申请次数":"申请机构数",type:"pie",radius:["35%","60%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"14",fontWeight:"bold"}},labelLine:{show:!1},data:t,center:["32%","50%"],itemStyle:{borderRadius:8,borderWidth:2,borderColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.2)"},zlevel:1}]};D.value.setOption(i)}const bt=pt(()=>{const e={id:{apply:[],org:[]},cell:{apply:[],org:[]}};return Object.keys(M).forEach(t=>{var x,v,o,n,u,h,_,w,V,j,B,I,H,W,G,P,q,U,X,Y,Q,J,Z,T,ee,te,le,oe,ne,se,ae,re,ie,ue,de,me,pe,ce,ge,ve,xe,be,fe,ye,he,_e,we,Ce,ke,Ae,Oe,$e,De,Se,Me,ze,Ne,Ve,je,Be,Ke,Le,Ee,Ie,Fe,Re,He,We,Ge,Pe,qe,Ue,Xe,Ye,Qe,Je,Ze,Te,et,tt,lt,ot,nt,st,at,rt,it,a,Dt,St,Mt,zt,Nt,Vt,jt,Bt;const i={label:M[t],d7:((n=(o=(v=(x=p.value)==null?void 0:x.id)==null?void 0:v[0])==null?void 0:o.find(s=>s.name===t))==null?void 0:n.totalApplyCount)||0,m1:((w=(_=(h=(u=p.value)==null?void 0:u.id)==null?void 0:h[1])==null?void 0:_.find(s=>s.name===t))==null?void 0:w.totalApplyCount)||0,m3:((I=(B=(j=(V=p.value)==null?void 0:V.id)==null?void 0:j[2])==null?void 0:B.find(s=>s.name===t))==null?void 0:I.totalApplyCount)||0,m6:((P=(G=(W=(H=p.value)==null?void 0:H.id)==null?void 0:W[3])==null?void 0:G.find(s=>s.name===t))==null?void 0:P.totalApplyCount)||0,m12:((Y=(X=(U=(q=p.value)==null?void 0:q.id)==null?void 0:U[4])==null?void 0:X.find(s=>s.name===t))==null?void 0:Y.totalApplyCount)||0,value:((T=(Z=(J=(Q=p.value)==null?void 0:Q.id)==null?void 0:J[L.value])==null?void 0:Z.find(s=>s.name===t))==null?void 0:T.totalApplyCount)||0};e.id.apply.push(i);const c={label:M[t],d7:((oe=(le=(te=(ee=p.value)==null?void 0:ee.id)==null?void 0:te[0])==null?void 0:le.find(s=>s.name===t))==null?void 0:oe.totalOrgCount)||0,m1:((re=(ae=(se=(ne=p.value)==null?void 0:ne.id)==null?void 0:se[1])==null?void 0:ae.find(s=>s.name===t))==null?void 0:re.totalOrgCount)||0,m3:((me=(de=(ue=(ie=p.value)==null?void 0:ie.id)==null?void 0:ue[2])==null?void 0:de.find(s=>s.name===t))==null?void 0:me.totalOrgCount)||0,m6:((ve=(ge=(ce=(pe=p.value)==null?void 0:pe.id)==null?void 0:ce[3])==null?void 0:ge.find(s=>s.name===t))==null?void 0:ve.totalOrgCount)||0,m12:((ye=(fe=(be=(xe=p.value)==null?void 0:xe.id)==null?void 0:be[4])==null?void 0:fe.find(s=>s.name===t))==null?void 0:ye.totalOrgCount)||0,value:((Ce=(we=(_e=(he=p.value)==null?void 0:he.id)==null?void 0:_e[L.value])==null?void 0:we.find(s=>s.name===t))==null?void 0:Ce.totalOrgCount)||0};e.id.org.push(c);const g={label:M[t],d7:(($e=(Oe=(Ae=(ke=p.value)==null?void 0:ke.cell)==null?void 0:Ae[0])==null?void 0:Oe.find(s=>s.name===t))==null?void 0:$e.totalApplyCount)||0,m1:((ze=(Me=(Se=(De=p.value)==null?void 0:De.cell)==null?void 0:Se[1])==null?void 0:Me.find(s=>s.name===t))==null?void 0:ze.totalApplyCount)||0,m3:((Be=(je=(Ve=(Ne=p.value)==null?void 0:Ne.cell)==null?void 0:Ve[2])==null?void 0:je.find(s=>s.name===t))==null?void 0:Be.totalApplyCount)||0,m6:((Ie=(Ee=(Le=(Ke=p.value)==null?void 0:Ke.cell)==null?void 0:Le[3])==null?void 0:Ee.find(s=>s.name===t))==null?void 0:Ie.totalApplyCount)||0,m12:((We=(He=(Re=(Fe=p.value)==null?void 0:Fe.cell)==null?void 0:Re[4])==null?void 0:He.find(s=>s.name===t))==null?void 0:We.totalApplyCount)||0,value:((Ue=(qe=(Pe=(Ge=p.value)==null?void 0:Ge.cell)==null?void 0:Pe[L.value])==null?void 0:qe.find(s=>s.name===t))==null?void 0:Ue.totalApplyCount)||0};e.cell.apply.push(g);const d={label:M[t],d7:((Je=(Qe=(Ye=(Xe=p.value)==null?void 0:Xe.cell)==null?void 0:Ye[0])==null?void 0:Qe.find(s=>s.name===t))==null?void 0:Je.totalOrgCount)||0,m1:((tt=(et=(Te=(Ze=p.value)==null?void 0:Ze.cell)==null?void 0:Te[1])==null?void 0:et.find(s=>s.name===t))==null?void 0:tt.totalOrgCount)||0,m3:((st=(nt=(ot=(lt=p.value)==null?void 0:lt.cell)==null?void 0:ot[2])==null?void 0:nt.find(s=>s.name===t))==null?void 0:st.totalOrgCount)||0,m6:((a=(it=(rt=(at=p.value)==null?void 0:at.cell)==null?void 0:rt[3])==null?void 0:it.find(s=>s.name===t))==null?void 0:a.totalOrgCount)||0,m12:((zt=(Mt=(St=(Dt=p.value)==null?void 0:Dt.cell)==null?void 0:St[4])==null?void 0:Mt.find(s=>s.name===t))==null?void 0:zt.totalOrgCount)||0,value:((Bt=(jt=(Vt=(Nt=p.value)==null?void 0:Nt.cell)==null?void 0:Vt[L.value])==null?void 0:jt.find(s=>s.name===t))==null?void 0:Bt.totalOrgCount)||0};e.cell.org.push(d)}),e}),Gt=pt(()=>{var i,c,g,d,x,v;const e={id:{apply:[],org:[]},cell:{apply:[],org:[]}},t={week:"周末申请",night:"夜间申请"};if(Object.keys(t).forEach(o=>{var w,V,j,B,I,H,W,G,P,q,U,X,Y,Q,J,Z,T,ee,te,le,oe,ne,se,ae,re,ie,ue,de,me,pe,ce,ge,ve,xe,be,fe,ye,he,_e,we,Ce,ke,Ae,Oe,$e,De,Se,Me,ze,Ne,Ve,je,Be,Ke,Le,Ee,Ie,Fe,Re,He,We,Ge,Pe,qe,Ue,Xe,Ye,Qe,Je,Ze,Te,et,tt,lt,ot,nt,st,at,rt,it;const n={label:t[o],d7:((B=(j=(V=(w=r.value)==null?void 0:w.id)==null?void 0:V[0])==null?void 0:j.find(a=>a.name===o))==null?void 0:B.totalApplyCount)||0,m1:((G=(W=(H=(I=r.value)==null?void 0:I.id)==null?void 0:H[1])==null?void 0:W.find(a=>a.name===o))==null?void 0:G.totalApplyCount)||0,m3:((X=(U=(q=(P=r.value)==null?void 0:P.id)==null?void 0:q[2])==null?void 0:U.find(a=>a.name===o))==null?void 0:X.totalApplyCount)||0,m6:((Z=(J=(Q=(Y=r.value)==null?void 0:Y.id)==null?void 0:Q[3])==null?void 0:J.find(a=>a.name===o))==null?void 0:Z.totalApplyCount)||0,m12:((le=(te=(ee=(T=r.value)==null?void 0:T.id)==null?void 0:ee[4])==null?void 0:te.find(a=>a.name===o))==null?void 0:le.totalApplyCount)||0};e.id.apply.push(n);const u={label:t[o],d7:((ae=(se=(ne=(oe=r.value)==null?void 0:oe.id)==null?void 0:ne[0])==null?void 0:se.find(a=>a.name===o))==null?void 0:ae.totalOrgCount)||0,m1:((de=(ue=(ie=(re=r.value)==null?void 0:re.id)==null?void 0:ie[1])==null?void 0:ue.find(a=>a.name===o))==null?void 0:de.totalOrgCount)||0,m3:((ge=(ce=(pe=(me=r.value)==null?void 0:me.id)==null?void 0:pe[2])==null?void 0:ce.find(a=>a.name===o))==null?void 0:ge.totalOrgCount)||0,m6:((fe=(be=(xe=(ve=r.value)==null?void 0:ve.id)==null?void 0:xe[3])==null?void 0:be.find(a=>a.name===o))==null?void 0:fe.totalOrgCount)||0,m12:((we=(_e=(he=(ye=r.value)==null?void 0:ye.id)==null?void 0:he[4])==null?void 0:_e.find(a=>a.name===o))==null?void 0:we.totalOrgCount)||0};e.id.org.push(u);const h={label:t[o],d7:((Oe=(Ae=(ke=(Ce=r.value)==null?void 0:Ce.cell)==null?void 0:ke[0])==null?void 0:Ae.find(a=>a.name===o))==null?void 0:Oe.totalApplyCount)||0,m1:((Me=(Se=(De=($e=r.value)==null?void 0:$e.cell)==null?void 0:De[1])==null?void 0:Se.find(a=>a.name===o))==null?void 0:Me.totalApplyCount)||0,m3:((je=(Ve=(Ne=(ze=r.value)==null?void 0:ze.cell)==null?void 0:Ne[2])==null?void 0:Ve.find(a=>a.name===o))==null?void 0:je.totalApplyCount)||0,m6:((Ee=(Le=(Ke=(Be=r.value)==null?void 0:Be.cell)==null?void 0:Ke[3])==null?void 0:Le.find(a=>a.name===o))==null?void 0:Ee.totalApplyCount)||0,m12:((He=(Re=(Fe=(Ie=r.value)==null?void 0:Ie.cell)==null?void 0:Fe[4])==null?void 0:Re.find(a=>a.name===o))==null?void 0:He.totalApplyCount)||0};e.cell.apply.push(h);const _={label:t[o],d7:((qe=(Pe=(Ge=(We=r.value)==null?void 0:We.cell)==null?void 0:Ge[0])==null?void 0:Pe.find(a=>a.name===o))==null?void 0:qe.totalOrgCount)||0,m1:((Qe=(Ye=(Xe=(Ue=r.value)==null?void 0:Ue.cell)==null?void 0:Xe[1])==null?void 0:Ye.find(a=>a.name===o))==null?void 0:Qe.totalOrgCount)||0,m3:((et=(Te=(Ze=(Je=r.value)==null?void 0:Je.cell)==null?void 0:Ze[2])==null?void 0:Te.find(a=>a.name===o))==null?void 0:et.totalOrgCount)||0,m6:((nt=(ot=(lt=(tt=r.value)==null?void 0:tt.cell)==null?void 0:lt[3])==null?void 0:ot.find(a=>a.name===o))==null?void 0:nt.totalOrgCount)||0,m12:((it=(rt=(at=(st=r.value)==null?void 0:st.cell)==null?void 0:at[4])==null?void 0:rt.find(a=>a.name===o))==null?void 0:it.totalOrgCount)||0};e.cell.org.push(_)}),(g=(c=(i=r.value)==null?void 0:i.id)==null?void 0:c[4])!=null&&g.some(o=>o.name==="first_apply")){const o=r.value.id[4].find(u=>u.name==="first_apply"),n=r.value.id[4].find(u=>u.name==="last_apply");e.id.apply.push({label:"最早非银申请",m12:(o==null?void 0:o.totalApplyCount)||"-",specialDisplay:!0}),e.id.apply.push({label:"最近非银申请",m12:(n==null?void 0:n.totalApplyCount)||"-",specialDisplay:!0}),e.id.org.push({label:"最早非银申请",m12:"-",specialDisplay:!0}),e.id.org.push({label:"最近非银申请",m12:"-",specialDisplay:!0})}if((v=(x=(d=r.value)==null?void 0:d.cell)==null?void 0:x[4])!=null&&v.some(o=>o.name==="first_apply")){const o=r.value.cell[4].find(u=>u.name==="first_apply"),n=r.value.cell[4].find(u=>u.name==="last_apply");e.cell.apply.push({label:"最早非银申请",m12:(o==null?void 0:o.totalApplyCount)||"-",specialDisplay:!0}),e.cell.apply.push({label:"最近非银申请",m12:(n==null?void 0:n.totalApplyCount)||"-",specialDisplay:!0}),e.cell.org.push({label:"最早非银申请",m12:"-",specialDisplay:!0}),e.cell.org.push({label:"最近非银申请",m12:"-",specialDisplay:!0})}return e});function Ot(){O.value&&xt(),D.value&&At()}function Pt(e){const t=Object.keys(vt),i={id:{totalApplyCount:[],totalOrgCount:[]},cell:{totalApplyCount:[],totalOrgCount:[]}},c=["bank","nbank"];for(const g of t){let d=0,x=0,v=0,o=0;c.forEach(n=>{const u=`als_${g}_id_${n}_allnum`,h=`als_${g}_id_${n}_orgnum`;(e==null?void 0:e[u])!==void 0&&(e==null?void 0:e[h])!==void 0&&(d+=Number((e==null?void 0:e[u])||0),x+=Number((e==null?void 0:e[h])||0));const _=`als_${g}_cell_${n}_allnum`,w=`als_${g}_cell_${n}_orgnum`;(e==null?void 0:e[_])!==void 0&&(e==null?void 0:e[w])!==void 0&&(v+=Number((e==null?void 0:e[_])||0),o+=Number((e==null?void 0:e[w])||0))}),i.id.totalApplyCount.push(d),i.id.totalOrgCount.push(x),i.cell.totalApplyCount.push(v),i.cell.totalOrgCount.push(o)}return i}function qt(e){const t=Object.keys(vt),i={id:[],cell:[]};for(const c of t){const g=[],d=[];Object.keys(M).forEach(x=>{const v=kt[x];let o=0,n=0,u=0,h=0;for(const _ of v){const w=`als_${c}_id_${_.code}_allnum`,V=`als_${c}_id_${_.code}_orgnum`;o+=Number((e==null?void 0:e[w])||0),n+=Number((e==null?void 0:e[V])||0);const j=`als_${c}_cell_${_.code}_allnum`,B=`als_${c}_cell_${_.code}_orgnum`;u+=Number((e==null?void 0:e[j])||0),h+=Number((e==null?void 0:e[B])||0)}g.push({label:M[x],name:x,totalApplyCount:o,totalOrgCount:n}),d.push({label:M[x],name:x,totalApplyCount:u,totalOrgCount:h})}),i.id.push(g),i.cell.push(d)}return i}function Ut(e){const t=Object.keys(vt),i={week:"周末申请",night:"夜间申请 (晚8点至次日早8点)"},c={id:[],cell:[]};for(const g of t){const d=[],x=[];if(Object.keys(i).forEach(v=>{const o=Wt[v];let n=0,u=0,h=0,_=0;for(const w of o){const V=`als_${g}_id_${w.code}_allnum`,j=`als_${g}_id_${w.code}_orgnum`;n+=Number((e==null?void 0:e[V])||0),u+=Number((e==null?void 0:e[j])||0);const B=`als_${g}_cell_${w.code}_allnum`,I=`als_${g}_cell_${w.code}_orgnum`;h+=Number((e==null?void 0:e[B])||0),_+=Number((e==null?void 0:e[I])||0)}d.push({label:i[v],name:v,totalApplyCount:n,totalOrgCount:u}),x.push({label:i[v],name:v,totalApplyCount:h,totalOrgCount:_})}),g==="m12"){const v="als_fst_id_nbank_inteday",o="als_fst_cell_nbank_inteday",n="als_lst_id_nbank_inteday",u="als_lst_cell_nbank_inteday";e!=null&&e[v]&&d.push({label:"最早非银借贷申请",name:"first_apply",totalApplyCount:`${e[v]}天前`,totalOrgCount:"-"}),e!=null&&e[n]&&d.push({label:"最近非银借贷申请",name:"last_apply",totalApplyCount:`${e[n]}天前`,totalOrgCount:"-"}),e!=null&&e[o]&&x.push({label:"最早非银借贷申请",name:"first_apply",totalApplyCount:`${e[o]}天前`,totalOrgCount:"-"}),e!=null&&e[u]&&x.push({label:"最近非银借贷申请",name:"last_apply",totalApplyCount:`${e[u]}天前`,totalOrgCount:"-"})}c.id.push(d),c.cell.push(x)}return c}function Xt(e){const t={id:{maxApply:0,maxOrg:0,avgMonthlyApply:0},cell:{maxApply:0,maxOrg:0,avgMonthlyApply:0}};return e!=null&&e.als_m12_id_max_monnum&&(t.id.maxApply=Number(e.als_m12_id_max_monnum)),e!=null&&e.als_m12_cell_max_monnum&&(t.cell.maxApply=Number(e.als_m12_cell_max_monnum)),e!=null&&e.als_m12_id_avg_monnum&&(t.id.avgMonthlyApply=Number(e.als_m12_id_avg_monnum)),e!=null&&e.als_m12_cell_avg_monnum&&(t.cell.avgMonthlyApply=Number(e.als_m12_cell_avg_monnum)),e!=null&&e.als_m12_id_nbank_tot_mons&&(t.id.totalMonths=Number(e.als_m12_id_nbank_tot_mons)),e!=null&&e.als_m12_cell_nbank_tot_mons&&(t.cell.totalMonths=Number(e.als_m12_cell_nbank_tot_mons)),t}function E(e){var t,i,c,g,d,x;return e==="id"?((c=(i=(t=N.value)==null?void 0:t.id)==null?void 0:i.totalApplyCount)==null?void 0:c.some(v=>v>0))||!1:e==="cell"&&((x=(d=(g=N.value)==null?void 0:g.cell)==null?void 0:d.totalApplyCount)==null?void 0:x.some(v=>v>0))||!1}ut(L,()=>{bt.value[b.value][$.value].forEach(e=>{const t=["d7","m1","m3","m6","m12"][L.value];e.value=e[t]}),Ot()}),ut(b,()=>{ft(),xt()}),ut($,Ot);function $t(){O.value&&O.value.resize(),D.value&&D.value.resize()}Yt(()=>{N.value=Pt(A),p.value=qt(A),r.value=Ut(A),Xt(A),E("id")?b.value="id":E("cell")&&(b.value="cell"),ft(),Qt(()=>{xt(),At()}),window.addEventListener("resize",$t)});function ft(){var e,t,i,c;K.value={categories:["近7日","近1月","近3月","近6月","近1年"],series:[{name:"申请次数",data:((t=(e=N.value)==null?void 0:e[b.value])==null?void 0:t.totalApplyCount)||[]},{name:"申请的机构数",data:((c=(i=N.value)==null?void 0:i[b.value])==null?void 0:c.totalOrgCount)||[]}]}}return Jt(()=>{window.removeEventListener("resize",$t),O.value&&O.value.dispose(),D.value&&D.value.dispose()}),ut(b,e=>{ft(),O.value&&O.value.setOption({xAxis:{data:K.value.categories},series:K.value.series.map(t=>({name:t.name,data:t.data}))})}),(e,t)=>{var c,g,d,x,v,o;const i=tl;return C(),k("div",ll,[l("div",ol,[l("div",nl,[t[11]||(t[11]=l("div",{class:"absolute top-0 right-0 w-32 h-32 bg-blue-50 rounded-full -mr-8 -mt-8 opacity-60"},null,-1)),t[12]||(t[12]=l("div",{class:"absolute bottom-0 left-0 w-20 h-20 bg-green-50 rounded-full -ml-10 -mb-10 opacity-50"},null,-1)),l("div",sl,[t[10]||(t[10]=l("div",{class:"space-y-2"},[l("h2",{class:"text-xl font-semibold text-gray-800 flex items-center"},"借贷申请分析报告"),l("p",{class:"text-sm text-gray-600 ml-6"},"本报告统计借贷申请记录和机构情况，帮助评估信贷风险")],-1)),l("div",al,[E("id")||E("cell")?(C(),k("div",rl,[E("id")?(C(),k("button",{key:0,type:"button",class:ct(["flex-1 py-2 px-4 text-sm font-medium rounded-l-md border transition-all duration-200 flex items-center flex-shrink-0",[m(b)==="id"?"bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-500 shadow-md":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"]]),onClick:t[0]||(t[0]=n=>b.value="id")},[m(b)==="id"?(C(),k("svg",il,t[4]||(t[4]=[l("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):(C(),k("svg",ul,t[5]||(t[5]=[l("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2"},null,-1)]))),t[6]||(t[6]=yt(" 身份证匹配 "))],2)):ht("",!0),E("cell")?(C(),k("button",{key:1,type:"button",class:ct(["flex-1 py-2 px-4 text-sm font-medium rounded-r-md border transition-all duration-200 flex items-center flex-shrink-0",[m(b)==="cell"?"bg-gradient-to-r from-green-500 to-green-600 text-white border-green-500 shadow-md":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"]]),onClick:t[1]||(t[1]=n=>b.value="cell")},[m(b)==="cell"?(C(),k("svg",dl,t[7]||(t[7]=[l("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):(C(),k("svg",ml,t[8]||(t[8]=[l("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"},null,-1)]))),t[9]||(t[9]=yt(" 手机号匹配 "))],2)):ht("",!0)])):ht("",!0)])]),t[13]||(t[13]=Kt('<div class="mt-4 bg-blue-50 p-3 rounded-lg text-xs text-gray-700" data-v-ba2226e1><p class="font-medium text-blue-800 mb-1" data-v-ba2226e1>数据类型说明：</p><div class="grid grid-cols-1 md:grid-cols-2 gap-2" data-v-ba2226e1><div class="flex items-start" data-v-ba2226e1><span class="inline-block w-2 h-2 mt-1 mr-2 rounded-full flex-shrink-0 bg-blue-500" data-v-ba2226e1></span><span data-v-ba2226e1><strong data-v-ba2226e1>身份证匹配：</strong>通过身份证号码匹配获取的借贷申请记录，能够反映与身份证关联的所有金融机构申请行为</span></div><div class="flex items-start" data-v-ba2226e1><span class="inline-block w-2 h-2 mt-1 mr-2 rounded-full flex-shrink-0 bg-green-500" data-v-ba2226e1></span><span data-v-ba2226e1><strong data-v-ba2226e1>手机号匹配：</strong>通过手机号码匹配获取的借贷申请记录，能够反映与手机号关联的所有金融机构申请行为</span></div></div></div>',1))]),E(m(b))?(C(),k(wt,{key:0},[l("div",pl,[t[22]||(t[22]=l("div",{class:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-green-400 opacity-70"},null,-1)),t[23]||(t[23]=l("div",{class:"absolute bottom-4 right-4 opacity-10"},[l("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-32 w-32",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[l("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),l("div",cl,[l("div",gl,[l("div",vl,[t[15]||(t[15]=l("div",{class:"mr-2 md:mr-3 bg-blue-100 rounded-lg p-1 md:p-2 text-blue-600"},[l("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 md:h-6 md:w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[l("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})])],-1)),l("div",null,[t[14]||(t[14]=l("div",{class:"text-xs md:text-sm text-gray-500"},"近1年总申请次数",-1)),l("div",xl,f(((c=m(N)[m(b)])==null?void 0:c.totalApplyCount[4])||0),1)])])]),l("div",bl,[l("div",fl,[t[17]||(t[17]=l("div",{class:"mr-2 md:mr-3 bg-green-100 rounded-lg p-1 md:p-2 text-green-600"},[l("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 md:h-6 md:w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[l("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])],-1)),l("div",null,[t[16]||(t[16]=l("div",{class:"text-xs md:text-sm text-gray-500"},"近1年总申请机构数",-1)),l("div",yl,f(((g=m(N)[m(b)])==null?void 0:g.totalOrgCount[4])||0),1)])])]),l("div",hl,[l("div",_l,[t[19]||(t[19]=l("div",{class:"mr-2 md:mr-3 bg-yellow-100 rounded-lg p-1 md:p-2 text-yellow-600"},[l("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 md:h-6 md:w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[l("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),l("div",null,[t[18]||(t[18]=l("div",{class:"text-xs md:text-sm text-gray-500"},"单月最高申请次数",-1)),l("div",wl,f(m(A)[`als_m12_${m(b)}_max_monnum`]||0),1)])])]),l("div",Cl,[l("div",kl,[t[21]||(t[21]=l("div",{class:"mr-2 md:mr-3 bg-purple-100 rounded-lg p-1 md:p-2 text-purple-600"},[l("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 md:h-6 md:w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[l("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"})])],-1)),l("div",null,[t[20]||(t[20]=l("div",{class:"text-xs md:text-sm text-gray-500"},"月均申请次数",-1)),l("div",Al,f(m(A)[`als_m12_${m(b)}_avg_monnum`]||0),1)])])])])]),l("div",null,[F(_t,{title:"借贷申请趋势",type:"blue-green"}),l("div",{ref_key:"chartRef",ref:z,class:"chart-container"},null,512)]),l("div",null,[F(_t,{title:"借贷类型分析",type:"blue-green"}),l("div",Ol,[l("div",$l,[l("div",{ref_key:"orgPieChartRef",ref:R,class:"chart-container-small"},null,512)]),l("div",null,[l("div",Dl,[F(i,{modelValue:m($),"onUpdate:modelValue":t[2]||(t[2]=n=>Lt($)?$.value=n:null),options:m(Ct),type:"blue-green"},null,8,["modelValue","options"])]),l("div",Sl,[F(It,{data:((x=(d=m(bt))==null?void 0:d[m(b)])==null?void 0:x[m($)])||[],type:"blue-green",class:"w-full whitespace-nowrap"},{header:mt(()=>t[24]||(t[24]=[l("th",{class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm w-[25%]"},"借贷类别",-1),l("th",{class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm w-[15%]"},"近7日",-1),l("th",{class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm w-[15%]"},"近1月",-1),l("th",{class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm w-[15%]"},"近3月",-1),l("th",{class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm w-[15%]"},"近6月",-1),l("th",{class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm w-[15%]"},"近1年",-1)])),default:mt(({row:n})=>[l("td",Ml,f(n.label),1),l("td",zl,f(n.d7),1),l("td",Nl,f(n.m1),1),l("td",Vl,f(n.m3),1),l("td",jl,f(n.m6),1),l("td",Bl,f(n.m12),1)]),_:1},8,["data"])])])]),l("div",Kl,[t[25]||(t[25]=l("p",{class:"font-medium text-blue-800 mb-1"},"借贷类型说明：",-1)),l("div",Ll,[(C(),k(wt,null,Rt(M,(n,u)=>l("div",{key:u,class:"flex items-start"},[l("span",{class:"inline-block w-3 h-3 mt-1 mr-2 rounded-full flex-shrink-0",style:Ht({backgroundColor:m(S).color[Object.keys(M).indexOf(u)]})},null,4),l("span",null,[l("strong",null,f(n)+"：",1),yt(f(u==="bank"?"包含各类银行贷款":kt[u].map(h=>h.name).join("、")),1)])])),64))])])]),l("div",null,[F(_t,{title:"特殊时段申请记录",type:"blue-green"}),t[27]||(t[27]=l("div",{class:"text-xs text-gray-500 my-2"}," 此表格展示在周末或夜间时段的借贷申请情况，这类时段的高频申请可能需要额外关注 ",-1)),l("div",El,[F(i,{modelValue:m($),"onUpdate:modelValue":t[3]||(t[3]=n=>Lt($)?$.value=n:null),options:m(Ct),type:"blue-green"},null,8,["modelValue","options"])]),l("div",Il,[F(It,{data:((o=(v=m(Gt))==null?void 0:v[m(b)])==null?void 0:o[m($)])||[],type:"blue-green",class:"w-full whitespace-nowrap"},{header:mt(()=>t[26]||(t[26]=[l("th",{class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm w-[25%]"},"时段类型",-1),l("th",{class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm w-[15%]"},"近7日",-1),l("th",{class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm w-[15%]"},"近1月",-1),l("th",{class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm w-[15%]"},"近3月",-1),l("th",{class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm w-[15%]"},"近6月",-1),l("th",{class:"border px-2 py-1 text-xs lg:px-3 lg:py-2 lg:text-sm w-[15%]"},"近1年",-1)])),default:mt(({row:n})=>[l("td",Fl,f(n.label),1),l("td",Rl,f(n.specialDisplay?"-":n.d7),1),l("td",Hl,f(n.specialDisplay?"-":n.m1),1),l("td",Wl,f(n.specialDisplay?"-":n.m3),1),l("td",Gl,f(n.specialDisplay?"-":n.m6),1),l("td",Pl,f(n.m12),1)]),_:1},8,["data"])])])],64)):(C(),k("div",ql,t[28]||(t[28]=[Kt('<div class="text-center" data-v-ba2226e1><div class="text-gray-400 text-4xl mb-2" data-v-ba2226e1><svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-ba2226e1><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" data-v-ba2226e1></path></svg></div><div class="text-gray-600 text-xl font-medium" data-v-ba2226e1>暂无借贷申请数据</div><div class="text-gray-500 mt-2" data-v-ba2226e1>当前没有匹配到任何借贷申请记录</div></div>',1)])))])])}}},oo=Ft(Ul,[["__scopeId","data-v-ba2226e1"]]);export{oo as default};
