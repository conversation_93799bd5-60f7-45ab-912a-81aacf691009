import{a as L,i as W,b as w}from"./BaseReport-ClT0Rj5U.js";import{L as X}from"./LTable-jtVDM3iJ.js";import{_ as rt,r as b,N as J,e as v,f as nt,X as at,d as y,c as g,a as o,p as P,n as D,h as K,q as Q,S as st,g as _,A as R,t as s}from"./index-B4d74vWj.js";/* empty css              *//* empty css              */import"./index-DsKI3WGL.js";import"./use-id-BHlr6Txk.js";import"./use-tab-status-Co3jcf_0.js";import"./index-Z9L7R3xo.js";const lt={class:"card"},it={class:"flex flex-col gap-y-4"},dt={class:"p-6 bg-white rounded-lg shadow-sm border border-gray-100 relative overflow-hidden mb-4"},ut={class:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 relative z-10"},ct={key:0,class:"flex flex-wrap gap-6 w-full md:w-auto"},mt={class:"flex-1 md:flex-none flex rounded-md shadow-sm relative"},pt={key:0,class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},bt={key:1,class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},xt={key:0,class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},vt={key:1,class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},yt={key:0,class:"mt-4 bg-blue-50 p-3 rounded-lg text-xs text-gray-700"},gt={class:"overflow-x-auto"},ft={class:"border px-1 py-2 text-xs"},ht={class:"border px-1 py-2 text-xs text-center"},wt={class:"border px-1 py-2 text-xs text-center"},_t={class:"border px-1 py-2 text-xs text-center"},Ct={class:"overflow-x-auto"},kt={class:"border px-1 py-2 text-xs"},$t={class:"border px-1 py-2 text-xs text-center"},At={class:"border px-1 py-2 text-xs text-center"},St={class:"border px-1 py-2 text-xs text-center"},Lt={class:"border px-1 py-2 text-xs text-center"},Rt={class:"border px-1 py-2 text-xs text-center"},Bt={class:"summary-container bg-blue-50 p-4 rounded-md"},Nt={class:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4"},Mt={class:"info-card p-3 bg-white rounded shadow-sm"},zt={class:"text-lg font-semibold"},Wt={class:"info-card p-3 bg-white rounded shadow-sm"},Dt={class:"text-lg font-semibold"},Kt={class:"info-card p-3 bg-white rounded shadow-sm"},Ot={class:"text-lg font-semibold"},Tt={class:"info-card p-3 bg-white rounded shadow-sm"},Vt={class:"text-lg font-semibold"},jt={class:"risk-assessment p-4 bg-white rounded-md"},Ft={class:"text-lg font-bold mb-2"},It={class:"text-gray-700"},Yt={__name:"CBankLoanBehavior",props:{data:{type:Object,required:!0},mode:{type:String,default:"full",validator:B=>["full","idOnly"].includes(B)}},setup(B){const O=B,{data:a,mode:T}=O,i=b("id");J(()=>O.mode,r=>{r==="idOnly"&&(i.value="id")},{immediate:!0});const N=b(null),V=b(null),j=b(null),C=b(null),k=b(null),$=b(null);b([]),b([]),v(()=>{const r=`tl_${i.value}`,t=a[`${r}_eletail_lasttime`];return t?new Date(t):new Date});function M(r){const t=r.replace("m","");return t==="12"?"近1年":`近${t}月`}function Z(r){return r==="t0"?"1年内":`近${r.replace("t","")}月`}function f(r){return 3e3*((Number(r)||1)-1)}function F(r){const t=Number(r)||1,e=3e3,n=e*(t-1),l=e*t;return`${n}元 - ${l}元`}const I=v(()=>{const r=["m1","m3","m6","m9","m12"],t=`tl_${i.value}`;return r.map((e,n)=>{const l=`${t}_${e}_nbank_passlendamt`,u=f(a[l]);return console.log(l,u),{month:M(e),amount:u,displayAmount:m(u),level:a[l]||"0",levelRange:F(a[l])}}).reverse()}),Y=v(()=>{const r=["m1","m3","m6","m9","m12"],t=`tl_${i.value}`;return r.map((e,n)=>{const l=`${t}_${e}_nbank_reamt`,u=f(a[l]);return{month:M(e),amount:u,displayAmount:m(u),level:a[l]||"0",levelRange:F(a[l])}}).reverse()}),tt=v(()=>{const r=["m1","m3","m6","m9","m12"],t=`tl_${i.value}`;return r.map((e,n)=>{const l=`${t}_${e}_nbank_passorg`,u=`${t}_${e}_nbank_passnum`,h=`${t}_${e}_nbank_passlendamt`,c=`${t}_${e}_nbank_reamt`,p=Number(a[l]||0),z=Number(a[u]||0),x=f(a[h]),A=f(a[c]);let S=0;return x>0&&(S=(A/x*100).toFixed(2)),{month:M(e),orgCount:p,loanCount:z,borrowAmount:m(x),repayAmount:m(A),ratio:`${S}%`}}).reverse()}),E=v(()=>{const r=["t0"],t=`tl_${i.value}`;return r.map((e,n)=>{const l=`${t}_${e}_nbank_org`,u=`${t}_${e}_nbank_num`,h=`${t}_${e}_nbank_lendamt`,c=`${t}_${e}_nbank_reamt`,p=Number(a[l]||0),z=Number(a[u]||0),x=f(a[h]),A=f(a[c]);let S=0;return x>0&&(S=(A/x*100).toFixed(2)),{month:Z(e),orgCount:p,loanCount:z,borrowAmount:m(x),repayAmount:m(A),ratio:`${S}%`}}).reverse()});v(()=>{const r=`tl_${i.value}`;return{time:a[`${r}_eletail_lasttime`]||"--",type:et(a[`${r}_eletail_lasttype`]),count:Number(a[`${r}_eletail_num`]||0),orgCount:Number(a[`${r}_eletail_org`]||0)}});function et(r){return{a:"传统银行",b:"网络零售银行",c:"持牌网络小贷",d:"持牌小贷",e:"持牌消费金融",f:"持牌融资租赁",g:"持牌汽车金融",h:"其他"}[r]||"未知"}function m(r){return r.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}const d=v(()=>{`${i.value}`;const r=E.value[0],t=(r.loanCount/12).toFixed(1),e=Number(r.borrowAmount.replace(/,/g,"")),n=(e/12).toFixed(0),l=Number(r.repayAmount.replace(/,/g,"")),u=(l/12).toFixed(0),h=e>0?(l/e*100).toFixed(1):0;let c="低",p="借贷行为健康，借贷金额合理";return r.orgCount>5?(c="高",p="多头借贷风险较高，借贷机构过多"):r.orgCount>3&&(c="中",p="存在多头借贷风险，借贷机构较多"),t>3&&(c=c==="低"?"中":"高",p+="，月均申请次数较多"),h<50&&(c=c==="低"?"中":"高",p+="，还款比例较低"),{totalApplications:r.loanCount,totalOrgs:r.orgCount,totalAmount:m(e),avgMonthlyApplications:t,avgMonthlyAmount:m(n),avgMonthlyRepay:m(u),repayRatio:`${h}%`,riskLevel:c,riskDesc:p}});function q(){if(!N.value)return;C.value||(C.value=W(N.value));const r=I.value,t={title:{text:"月度审批额度(元)",left:"center",textStyle:{fontWeight:"bold",fontSize:16}},tooltip:{trigger:"axis",formatter:function(e){const n=e[0].data;return`${e[0].name}<br/>${e[0].seriesName}: ${n.displayAmount}<br/>等级: ${n.level} (${n.levelRange})`},backgroundColor:"rgba(255, 255, 255, 0.8)",borderColor:"#5470C6",borderWidth:1,textStyle:{color:"#333"},shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.2)"},grid:{left:"5%",right:"5%",bottom:"0%",containLabel:!0},xAxis:{type:"category",data:r.map(e=>e.month),axisLabel:{interval:0,rotate:45,fontWeight:"bold",margin:15},axisLine:{lineStyle:{color:"#999"}}},yAxis:{type:"value",name:"金额(元)",nameTextStyle:{fontWeight:"bold"},splitLine:{lineStyle:{type:"dashed",opacity:.6}}},series:[{name:"审批额度",type:"bar",data:r.map(e=>({value:e.amount,displayAmount:e.displayAmount,level:e.level,levelRange:e.levelRange})),itemStyle:{color:new w(0,0,0,1,[{offset:0,color:"#83bff6"},{offset:.5,color:"#5470C6"},{offset:1,color:"#4662a4"}]),borderRadius:[5,5,0,0]},emphasis:{itemStyle:{color:new w(0,0,0,1,[{offset:0,color:"#5470C6"},{offset:.7,color:"#4662a4"},{offset:1,color:"#3c5390"}])}},barWidth:"60%",showBackground:!0,backgroundStyle:{color:"rgba(180, 180, 180, 0.1)"}}],animation:!0};C.value.setOption(t)}function H(){if(!V.value)return;k.value||(k.value=W(V.value));const r=Y.value,t={title:{text:"月度应还金额(元)",left:"center",textStyle:{fontWeight:"bold",fontSize:16}},tooltip:{trigger:"axis",formatter:function(e){const n=e[0].data;return`${e[0].name}<br/>${e[0].seriesName}: ${n.displayAmount}<br/>等级: ${n.level} (${n.levelRange})`},backgroundColor:"rgba(255, 255, 255, 0.8)",borderColor:"#91CC75",borderWidth:1,textStyle:{color:"#333"},shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.2)"},grid:{left:"5%",right:"5%",bottom:"15%",containLabel:!0},xAxis:{type:"category",data:r.map(e=>e.month),axisLabel:{interval:0,rotate:45,fontWeight:"bold",margin:15},axisLine:{lineStyle:{color:"#999"}}},yAxis:{type:"value",name:"金额(元)",nameTextStyle:{fontWeight:"bold"},splitLine:{lineStyle:{type:"dashed",opacity:.6}}},series:[{name:"应还金额",type:"bar",data:r.map(e=>({value:e.amount,displayAmount:e.displayAmount,level:e.level,levelRange:e.levelRange})),itemStyle:{color:new w(0,0,0,1,[{offset:0,color:"#b8e986"},{offset:.5,color:"#91CC75"},{offset:1,color:"#7cb362"}]),borderRadius:[5,5,0,0]},emphasis:{itemStyle:{color:new w(0,0,0,1,[{offset:0,color:"#91CC75"},{offset:.7,color:"#7cb362"},{offset:1,color:"#6a9c53"}])}},barWidth:"60%",showBackground:!0,backgroundStyle:{color:"rgba(180, 180, 180, 0.1)"}}],animation:!0};k.value.setOption(t)}function G(){if(!j.value)return;$.value||($.value=W(j.value));const r=I.value,t=Y.value,e={title:{text:"审批额度与应还金额趋势对比",left:"center",textStyle:{fontWeight:"bold",fontSize:16}},tooltip:{trigger:"axis",backgroundColor:"rgba(255, 255, 255, 0.8)",borderColor:"#ccc",borderWidth:1,textStyle:{color:"#333"},shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.2)"},legend:{data:["审批额度","应还金额"],top:30,textStyle:{fontWeight:"bold"}},grid:{left:"5%",right:"5%",bottom:"15%",containLabel:!0},xAxis:{type:"category",data:r.map(n=>n.month),axisLabel:{interval:0,rotate:45,fontWeight:"bold",margin:15},axisLine:{lineStyle:{color:"#999"}}},yAxis:{type:"value",name:"金额(元)",nameTextStyle:{fontWeight:"bold"},splitLine:{lineStyle:{type:"dashed",opacity:.6}}},series:[{name:"审批额度",type:"line",data:r.map(n=>n.amount),smooth:!0,symbol:"emptyCircle",symbolSize:8,lineStyle:{width:3,shadowColor:"rgba(0, 0, 0, 0.3)",shadowBlur:10,shadowOffsetY:8},itemStyle:{color:"#5470C6",borderWidth:2},areaStyle:{color:new w(0,0,0,1,[{offset:0,color:"rgba(84, 112, 198, 0.5)"},{offset:1,color:"rgba(84, 112, 198, 0.1)"}])}},{name:"应还金额",type:"line",data:t.map(n=>n.amount),smooth:!0,symbol:"emptyCircle",symbolSize:8,lineStyle:{width:3,shadowColor:"rgba(0, 0, 0, 0.3)",shadowBlur:10,shadowOffsetY:8},itemStyle:{color:"#91CC75",borderWidth:2},areaStyle:{color:new w(0,0,0,1,[{offset:0,color:"rgba(145, 204, 117, 0.5)"},{offset:1,color:"rgba(145, 204, 117, 0.1)"}])}}],animation:!0};$.value.setOption(e)}J(i,()=>{q(),H(),G()});function ot(){q(),H(),G()}function U(){C.value&&C.value.resize(),k.value&&k.value.resize(),$.value&&$.value.resize()}return nt(()=>{ot(),window.addEventListener("resize",U)}),at(()=>{window.removeEventListener("resize",U)}),(r,t)=>(y(),g("div",lt,[o("div",it,[o("div",dt,[t[10]||(t[10]=o("div",{class:"absolute top-0 right-0 w-32 h-32 bg-blue-50 rounded-full -mr-8 -mt-8 opacity-60"},null,-1)),t[11]||(t[11]=o("div",{class:"absolute bottom-0 left-0 w-20 h-20 bg-green-50 rounded-full -ml-10 -mb-10 opacity-50"},null,-1)),o("div",ut,[t[8]||(t[8]=o("div",{class:"space-y-2"},[o("h2",{class:"text-xl font-semibold text-gray-800 flex items-center"},"借贷行为分析报告"),o("p",{class:"text-sm text-gray-600 ml-6"},"本报告统计审批额度与应还情况，帮助评估信贷风险")],-1)),P(T)==="full"?(y(),g("div",ct,[o("div",mt,[o("button",{type:"button",class:D(["flex-1 py-2 px-4 text-sm font-medium rounded-l-md border transition-all duration-200 flex items-center",[i.value==="id"?"bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-500 shadow-md":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"]]),onClick:t[0]||(t[0]=e=>i.value="id")},[i.value==="id"?(y(),g("svg",pt,t[2]||(t[2]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):(y(),g("svg",bt,t[3]||(t[3]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2"},null,-1)]))),t[4]||(t[4]=K(" 身份证数据 "))],2),o("button",{type:"button",class:D(["flex-1 py-2 px-4 text-sm font-medium rounded-r-md border transition-all duration-200 flex items-center",[i.value==="cell"?"bg-gradient-to-r from-green-500 to-green-600 text-white border-green-500 shadow-md":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"]]),onClick:t[1]||(t[1]=e=>i.value="cell")},[i.value==="cell"?(y(),g("svg",xt,t[5]||(t[5]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):(y(),g("svg",vt,t[6]||(t[6]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"},null,-1)]))),t[7]||(t[7]=K(" 手机号数据 "))],2)])])):Q("",!0)]),P(T)==="full"?(y(),g("div",yt,t[9]||(t[9]=[st('<p class="font-medium text-blue-800 mb-1" data-v-44e08373>数据类型说明：</p><div class="grid grid-cols-1 md:grid-cols-2 gap-2" data-v-44e08373><div class="flex items-start" data-v-44e08373><span class="inline-block w-2 h-2 mt-1 mr-2 rounded-full flex-shrink-0 bg-blue-500" data-v-44e08373></span><span data-v-44e08373><strong data-v-44e08373>身份证数据：</strong>通过身份证号码匹配获取的借贷记录，反映与身份证关联的所有借贷行为</span></div><div class="flex items-start" data-v-44e08373><span class="inline-block w-2 h-2 mt-1 mr-2 rounded-full flex-shrink-0 bg-green-500" data-v-44e08373></span><span data-v-44e08373><strong data-v-44e08373>手机号数据：</strong>通过手机号码匹配获取的借贷记录，反映与手机号关联的所有借贷行为</span></div></div>',2)]))):Q("",!0)]),_(L,{title:"近期审批额度",type:"blue-green"}),o("div",{ref_key:"borrowChartRef",ref:N,class:"chart-container"},null,512),_(L,{title:"近期通过借贷审批情况",type:"blue-green"}),o("div",gt,[_(X,{data:tt.value,type:"blue-green"},{header:R(()=>t[12]||(t[12]=[o("th",{class:"border px-1 py-2 text-xs min-w-[25%]"},"时间",-1),o("th",{class:"border px-1 py-2 text-xs min-w-[15%]"},"借贷机构数",-1),o("th",{class:"border px-1 py-2 text-xs min-w-[15%]"},"借贷次数",-1),o("th",{class:"border px-1 py-2 text-xs min-w-[15%]"},"审批额度",-1)])),default:R(({row:e})=>[o("td",ft,s(e.month),1),o("td",ht,s(e.orgCount),1),o("td",wt,s(e.loanCount),1),o("td",_t,s(e.borrowAmount),1)]),_:1},8,["data"])]),_(L,{title:"近1年借贷情况",type:"blue-green"}),o("div",Ct,[_(X,{data:E.value,type:"blue-green"},{header:R(()=>t[13]||(t[13]=[o("th",{class:"border px-1 py-2 text-xs min-w-[25%]"},"时间",-1),o("th",{class:"border px-1 py-2 text-xs min-w-[15%]"},"借贷机构数",-1),o("th",{class:"border px-1 py-2 text-xs min-w-[15%]"},"借贷次数",-1),o("th",{class:"border px-1 py-2 text-xs min-w-[15%]"},"审批额度",-1),o("th",{class:"border px-1 py-2 text-xs min-w-[15%]"},"应还金额",-1),o("th",{class:"border px-1 py-2 text-xs min-w-[15%]"},"审批与应还比例",-1)])),default:R(({row:e})=>[o("td",kt,s(e.month),1),o("td",$t,s(e.orgCount),1),o("td",At,s(e.loanCount),1),o("td",St,s(e.borrowAmount),1),o("td",Lt,s(e.repayAmount),1),o("td",Rt,s(e.ratio),1)]),_:1},8,["data"])]),_(L,{title:"借贷行为总结分析",type:"blue-green"}),o("div",Bt,[t[19]||(t[19]=o("div",{class:"text-xs text-gray-500 mb-2"},"数据时间范围: 近1年",-1)),o("div",Nt,[o("div",Mt,[t[14]||(t[14]=o("div",{class:"text-sm text-gray-500"},"总申请次数",-1)),o("div",zt,s(d.value.totalApplications)+"次",1)]),o("div",Wt,[t[15]||(t[15]=o("div",{class:"text-sm text-gray-500"},"借贷机构数",-1)),o("div",Dt,s(d.value.totalOrgs)+"家",1)]),o("div",Kt,[t[16]||(t[16]=o("div",{class:"text-sm text-gray-500"},"总审批额度",-1)),o("div",Ot,s(d.value.totalAmount)+"元",1)]),o("div",Tt,[t[17]||(t[17]=o("div",{class:"text-sm text-gray-500"},"月均申请次数",-1)),o("div",Vt,s(d.value.avgMonthlyApplications)+"次",1)])]),o("div",jt,[o("div",Ft,[t[18]||(t[18]=K(" 风险评估: ")),o("span",{class:D({"text-red-500":d.value.riskLevel==="高","text-yellow-500":d.value.riskLevel==="中","text-green-500":d.value.riskLevel==="低"})},s(d.value.riskLevel)+"风险",3)]),o("div",It,[o("p",null," · 月均审批额度: "+s(d.value.avgMonthlyAmount)+"元 ",1),o("p",null," · 月均应还金额: "+s(d.value.avgMonthlyRepay)+"元 ",1),o("p",null,"· 还款比例: "+s(d.value.repayRatio),1),o("p",null,"· "+s(d.value.riskDesc),1)])])])])]))}},Zt=rt(Yt,[["__scopeId","data-v-44e08373"]]);export{Zt as default};
