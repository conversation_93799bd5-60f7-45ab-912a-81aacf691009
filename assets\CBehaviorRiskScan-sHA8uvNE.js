import{_ as x,d,c as n,a as e,n as i,p as f,t as c,aQ as b,F as y,b as h,S as I}from"./index-B4d74vWj.js";const p={class:"card main-card"},w={key:0,class:"py-4 text-center text-gray-500"},L={key:1,class:"risk-content"},C={class:"flex items-center"},S={class:"mt-1 text-gray-700"},B={class:"grid-container"},R={class:"grid-left"},z={class:"risk-section hover-lift"},N={class:"section-content"},D={class:"risk-level-indicator"},E={class:"indicator-bar"},F={class:"risk-section hover-lift"},M={class:"section-content"},T={class:"risk-level-indicator"},$={class:"indicator-bar"},j={class:"grid-right"},A={class:"risk-section hover-lift"},O={class:"section-content"},V={key:0,class:"flex flex-col md:flex-row gap-3"},q={class:"risk-level-indicator flex-1"},G={class:"indicator-bar"},Q={class:"group-size flex-1"},H={class:"mt-2 flex items-center"},J={key:1,class:"text-center py-2 text-gray-500"},K={class:"risk-section hover-lift"},P={class:"section-content"},U={key:0,class:"grid grid-cols-1 gap-3"},W={class:"flex-1"},X={class:"font-medium text-sm"},Y={class:"flex items-center mt-2"},Z={class:"progress-container"},ee={key:1,class:"text-center py-2 text-gray-500"},te={__name:"CBehaviorRiskScan",props:{data:{type:Object,default:()=>({})}},setup(s){const l=s,g=(t,a)=>{if(a==="black_gray_level")return{"":"无风险",1:"低风险",2:"中等风险",3:"高风险",4:"极高风险"}[t]||"未知风险";if(a==="telefraud_level")return{0:"无风险",1:"极低风险",2:"低风险",3:"中低风险",4:"中等风险",5:"高风险",6:"极高风险"}[t]||"未知风险";if(a==="frg_list_level")return t>="3"&&t<="5"?"低风险团伙":t>="6"&&t<="7"?"中风险团伙":t>="8"&&t<="10"?"高风险团伙":"无风险";if(a==="risk_level")return{A:"无风险",F:"低风险",C:"中风险",D:"中风险",B:"高风险",E:"高风险"}[t]||"未知风险";if(a==="gaming"){const r=parseInt(t);return r===0?"无风险":r>0&&r<=20?"极低风险":r>20&&r<=40?"低风险":r>40&&r<=60?"中等风险":r>60&&r<=80?"高风险":r>80?"极高风险":"未知风险"}return"未知风险"},v=(t,a)=>{if(a==="black_gray_level")return t===""||t==="1"?"bg-gradient-to-r from-emerald-400 to-teal-500":t==="2"?"bg-gradient-to-r from-amber-400 to-yellow-500":t==="3"?"bg-gradient-to-r from-orange-400 to-amber-600":t==="4"?"bg-gradient-to-r from-rose-400 to-red-500":"bg-gradient-to-r from-gray-400 to-gray-500";if(a==="telefraud_level")return t==="0"?"bg-gradient-to-r from-emerald-400 to-teal-500":t==="1"||t==="2"?"bg-gradient-to-r from-teal-300 to-green-400":t==="3"||t==="4"?"bg-gradient-to-r from-amber-400 to-yellow-500":t==="5"?"bg-gradient-to-r from-orange-400 to-amber-600":t==="6"?"bg-gradient-to-r from-rose-400 to-red-500":"bg-gradient-to-r from-gray-400 to-gray-500";if(a==="frg_list_level")return t>="3"&&t<="5"?"bg-gradient-to-r from-emerald-400 to-teal-500":t>="6"&&t<="7"?"bg-gradient-to-r from-amber-400 to-yellow-500":t>="8"&&t<="10"?"bg-gradient-to-r from-rose-400 to-red-500":"bg-gradient-to-r from-gray-400 to-gray-500";if(a==="risk_level")return t==="A"?"bg-gradient-to-r from-emerald-400 to-teal-500":t==="F"?"bg-gradient-to-r from-amber-400 to-yellow-500":t==="C"||t==="D"?"bg-gradient-to-r from-orange-400 to-amber-600":t==="B"||t==="E"?"bg-gradient-to-r from-rose-400 to-red-500":"bg-gradient-to-r from-gray-400 to-gray-500";if(a==="gaming"){const r=parseInt(t);return r===0?"bg-gradient-to-r from-emerald-400 to-teal-500":r>0&&r<=20?"bg-gradient-to-r from-teal-300 to-green-400":r>20&&r<=40?"bg-gradient-to-r from-green-400 to-green-500":r>40&&r<=60?"bg-gradient-to-r from-amber-400 to-yellow-500":r>60&&r<=80?"bg-gradient-to-r from-orange-400 to-amber-600":r>80?"bg-gradient-to-r from-rose-400 to-red-500":"bg-gradient-to-r from-gray-400 to-gray-500"}return"bg-gradient-to-r from-gray-400 to-gray-500"},m=t=>({110:"疑似欺诈",130:"疑似赌博庄家",150:"疑似赌博玩家",170:"疑似涉赌跑分"})[t]||"未知类型",_=t=>({a:"小规模(少于50人)",b:"中等规模(50-100人)",c:"大规模(100-500人)",d:"超大规模(500人以上)"})[t]||"未知规模",k=t=>{switch(t){case"110":return"fa-exclamation-triangle";case"130":return"fa-dice";case"150":return"fa-gamepad";case"170":return"fa-money-bill-wave";default:return"fa-question-circle"}},o=(()=>{if(!l.data)return{text:"无法评估风险",level:"low",color:"text-gray-500"};let t=0,a=0;return l.data.black_gray_level&&parseInt(l.data.black_gray_level)>2?t++:l.data.black_gray_level&&parseInt(l.data.black_gray_level)===2&&a++,l.data.telefraud_level&&parseInt(l.data.telefraud_level)>4?t++:l.data.telefraud_level&&parseInt(l.data.telefraud_level)>2&&a++,l.data.fraud_group&&l.data.fraud_group.frg_list_level&&parseInt(l.data.fraud_group.frg_list_level)>7?t++:l.data.fraud_group&&l.data.fraud_group.frg_list_level&&parseInt(l.data.fraud_group.frg_list_level)>5&&a++,l.data.risk_level&&l.data.risk_level.risk_level&&(["B","E"].includes(l.data.risk_level.risk_level)?t++:["C","D"].includes(l.data.risk_level.risk_level)?a++:l.data.risk_level.risk_level),l.data.anti_fraud_gaming&&l.data.anti_fraud_gaming.forEach(r=>{const u=parseInt(r.riskLevel);u>60?t++:u>40&&a++}),t>0?{text:"该用户存在较高风险行为，建议进行进一步核实和监控",level:"high",color:"text-red-500"}:a>0?{text:"该用户存在一定风险行为，建议提高警惕",level:"medium",color:"text-yellow-500"}:{text:"该用户行为正常，风险较低",level:"low",color:"text-green-500"}})();return(t,a)=>(d(),n("div",p,[!s.data||Object.keys(s.data).length===0?(d(),n("div",w," 暂无风险行为扫描数据 ")):(d(),n("div",L,[e("div",{class:i(["summary-card",{"border-red-500 glow-red":f(o).level==="high","border-yellow-500 glow-yellow":f(o).level==="medium","border-green-500 glow-green":f(o).level==="low"}])},[e("div",C,[e("div",{class:i(["summary-icon",f(o).color])},[e("i",{class:i(["fas",f(o).level==="high"?"fa-exclamation-triangle":f(o).level==="medium"?"fa-exclamation-circle":"fa-check-circle"])},null,2)],2),e("div",{class:i(["font-bold text-lg",f(o).color])},"风险评估总结",2)]),e("div",S,c(f(o).text),1)],2),e("div",B,[e("div",R,[e("div",z,[a[2]||(a[2]=e("div",{class:"section-title flex items-center"},[e("div",{class:"title-icon bg-indigo-100 text-indigo-600"},[e("i",{class:"fas fa-user-secret"})]),e("span",null,"黑灰产等级")],-1)),e("div",N,[e("div",D,[a[0]||(a[0]=e("div",{class:"indicator-label"},"风险等级",-1)),e("div",E,[e("div",{class:i(["indicator-value",v(s.data.black_gray_level||"","black_gray_level")]),style:b({width:s.data.black_gray_level?`${Math.min(parseInt(s.data.black_gray_level)*25,100)}%`:"0%"})},null,6)]),e("div",{class:i(["indicator-text",{"text-green-500":(s.data.black_gray_level||"")===""||(s.data.black_gray_level||"")==="1","text-yellow-500":(s.data.black_gray_level||"")==="2","text-orange-500":(s.data.black_gray_level||"")==="3","text-red-500":(s.data.black_gray_level||"")==="4"}])},c(g(s.data.black_gray_level||"","black_gray_level")),3)]),a[1]||(a[1]=e("div",{class:"description"},"黑灰产等级评估用户是否参与非法活动，等级越高风险越大",-1))])]),e("div",F,[a[5]||(a[5]=e("div",{class:"section-title flex items-center"},[e("div",{class:"title-icon bg-red-100 text-red-600"},[e("i",{class:"fas fa-phone-slash"})]),e("span",null,"电诈风险预警")],-1)),e("div",M,[e("div",T,[a[3]||(a[3]=e("div",{class:"indicator-label"},"风险等级",-1)),e("div",$,[e("div",{class:i(["indicator-value",v(s.data.telefraud_level||"0","telefraud_level")]),style:b({width:`${Math.min(parseInt(s.data.telefraud_level||"0")*16.6,100)}%`})},null,6)]),e("div",{class:i(["indicator-text",{"text-green-500":(s.data.telefraud_level||"0")==="0"||(s.data.telefraud_level||"0")==="1"||(s.data.telefraud_level||"0")==="2","text-yellow-500":(s.data.telefraud_level||"0")==="3"||(s.data.telefraud_level||"0")==="4","text-orange-500":(s.data.telefraud_level||"0")==="5","text-red-500":(s.data.telefraud_level||"0")==="6"}])},c(g(s.data.telefraud_level||"0","telefraud_level")),3)]),a[4]||(a[4]=e("div",{class:"description"},"电诈风险预警评估用户是否涉及电信诈骗活动，值越大风险越高",-1))])])]),e("div",j,[e("div",A,[a[10]||(a[10]=e("div",{class:"section-title flex items-center"},[e("div",{class:"title-icon bg-amber-100 text-amber-600"},[e("i",{class:"fas fa-users-slash"})]),e("span",null,"团伙欺诈排查")],-1)),e("div",O,[s.data.fraud_group?(d(),n("div",V,[e("div",q,[a[6]||(a[6]=e("div",{class:"indicator-label"},"团伙风险等级",-1)),e("div",G,[e("div",{class:i(["indicator-value",v(s.data.fraud_group.frg_list_level||"3","frg_list_level")]),style:b({width:`${Math.min((parseInt(s.data.fraud_group.frg_list_level||"3")-2)*12.5,100)}%`})},null,6)]),e("div",{class:i(["indicator-text",{"text-green-500":parseInt(s.data.fraud_group.frg_list_level||"3")<=5,"text-yellow-500":parseInt(s.data.fraud_group.frg_list_level||"3")>=6&&parseInt(s.data.fraud_group.frg_list_level||"3")<=7,"text-red-500":parseInt(s.data.fraud_group.frg_list_level||"3")>=8}])},c(g(s.data.fraud_group.frg_list_level||"3","frg_list_level")),3)]),e("div",Q,[a[8]||(a[8]=e("div",{class:"font-medium text-gray-700"},"团伙规模",-1)),e("div",H,[a[7]||(a[7]=e("i",{class:"fas fa-users text-blue-500 mr-2 text-xl"},null,-1)),e("span",null,c(_(s.data.fraud_group.frg_group_num||"a")),1)])])])):(d(),n("div",J,"暂无团伙欺诈数据")),a[9]||(a[9]=e("div",{class:"description mt-1"},"团伙欺诈排查评估用户是否属于欺诈团伙及团伙规模大小",-1))])]),e("div",K,[a[12]||(a[12]=e("div",{class:"section-title flex items-center"},[e("div",{class:"title-icon bg-purple-100 text-purple-600"},[e("i",{class:"fas fa-dice-slash"})]),e("span",null,"反诈反赌核验")],-1)),e("div",P,[s.data.anti_fraud_gaming&&s.data.anti_fraud_gaming.length>0?(d(),n("div",U,[(d(!0),n(y,null,h(s.data.anti_fraud_gaming,(r,u)=>(d(),n("div",{key:u,class:i(["gaming-item",parseInt(r.riskLevel)===0?"border-green-500":parseInt(r.riskLevel)<4?"border-green-400":parseInt(r.riskLevel)<7?"border-yellow-500":"border-red-500"])},[e("div",{class:i(["gaming-icon",parseInt(r.riskLevel)===0||parseInt(r.riskLevel)<4?"bg-green-100 text-green-500":parseInt(r.riskLevel)<7?"bg-yellow-100 text-yellow-600":"bg-red-100 text-red-500"])},[e("i",{class:i(["fas",k(r.riskType)])},null,2)],2),e("div",W,[e("div",X,c(m(r.riskType)),1),e("div",Y,[e("div",Z,[e("div",{class:i(["progress-bar",v(r.riskLevel,"gaming")]),style:b({width:`${Math.min(parseInt(r.riskLevel),100)}%`})},null,6)]),e("span",{class:i(["risk-level-text",{"text-green-500":parseInt(r.riskLevel)<=20,"text-green-600":parseInt(r.riskLevel)>20&&parseInt(r.riskLevel)<=40,"text-yellow-500":parseInt(r.riskLevel)>40&&parseInt(r.riskLevel)<=60,"text-orange-500":parseInt(r.riskLevel)>60&&parseInt(r.riskLevel)<=80,"text-red-500":parseInt(r.riskLevel)>80}])},c(g(r.riskLevel,"gaming")),3)])])],2))),128))])):(d(),n("div",ee,"暂无反诈反赌核验数据")),a[11]||(a[11]=e("div",{class:"description mt-1"},"反诈反赌核验评估用户是否有涉及诈骗或赌博活动的风险",-1))])]),a[13]||(a[13]=I('<div class="security-tips hover-lift" data-v-b16ed4af><div class="flex items-center" data-v-b16ed4af><div class="title-icon bg-blue-100 text-blue-600 mr-2" data-v-b16ed4af><i class="fas fa-lightbulb" data-v-b16ed4af></i></div><div class="font-bold text-blue-700" data-v-b16ed4af>安全建议</div></div><div class="tip-list" data-v-b16ed4af><div class="tip-item" data-v-b16ed4af><i class="fas fa-check-circle text-green-500 mr-1" data-v-b16ed4af></i><span data-v-b16ed4af>定期更新密码，使用复杂且不易猜测的密码</span></div><div class="tip-item" data-v-b16ed4af><i class="fas fa-check-circle text-green-500 mr-1" data-v-b16ed4af></i><span data-v-b16ed4af>开启双因素认证，提高账户安全性</span></div><div class="tip-item" data-v-b16ed4af><i class="fas fa-check-circle text-green-500 mr-1" data-v-b16ed4af></i><span data-v-b16ed4af>不点击来源不明的链接或下载不明文件</span></div><div class="tip-item" data-v-b16ed4af><i class="fas fa-check-circle text-green-500 mr-1" data-v-b16ed4af></i><span data-v-b16ed4af>不向陌生人透露个人敏感信息</span></div></div></div>',1))])])]))]))}},se=x(te,[["__scopeId","data-v-b16ed4af"]]);export{se as default};
