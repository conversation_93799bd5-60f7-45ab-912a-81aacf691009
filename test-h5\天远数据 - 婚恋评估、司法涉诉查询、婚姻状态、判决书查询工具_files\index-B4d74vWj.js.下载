const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Ai-YqploJsL.js","assets/ai_qinggan-Bw8hwQ_E.js","assets/Ai-BRj3sBn2.css","assets/Agent-CkNdLRoi.js","assets/Agent-CM5FkOsM.css","assets/Me-Dl0_HAnC.js","assets/VipBanner-CI4cmnf6.js","assets/vip_banner-DfHz1HNI.js","assets/Me-CleddfaY.css","assets/HistoryQuery-1iwapoY5.js","assets/index-DlGn0-WC.js","assets/use-tab-status-Co3jcf_0.js","assets/index-BzrbYvYM.css","assets/index-CzKg-Dew.js","assets/index-B4wmwnXg.css","assets/HistoryQuery-DVvdQFwo.css","assets/Help-D3_uXjp4.js","assets/index-Nl1y0CQy.js","assets/index-p_Y1ee2a.css","assets/index-DsKI3WGL.js","assets/use-id-BHlr6Txk.js","assets/index-Z9L7R3xo.js","assets/Help-bTqkGusM.css","assets/index-CBO6F5n_.css","assets/index-vk3zi5_R.css","assets/HelpDetail-D1SW-URi.js","assets/HelpDetail-9ip7KCCC.css","assets/HelpGuide-CQ7UBUyy.js","assets/HelpGuide-DsUouO9D.css","assets/Promote-DfHzGVfL.js","assets/QRcode-DjHVxBY0.js","assets/index-D8bU27Fu.js","assets/index-Bhs70iyM.css","assets/QRcode-pn1yPPwo.css","assets/index-Cz7m3neB.js","assets/index-CXt_2gjZ.css","assets/index-BzP3aEIB.js","assets/index-lzJ4ipIx.css","assets/Promote-tn0RQdqM.css","assets/Withdraw-DLB0_riQ.js","assets/Withdraw-wjgmuIPt.css","assets/Service-BAdH7Lfo.js","assets/Service-CsL98bFM.css","assets/Complaint-CVQGXIDi.js","assets/Complaint-C2aLvJBh.css","assets/Report-eLfJCQYI.js","assets/BaseReport-ClT0Rj5U.js","assets/BaseReport-APQ-y0C-.css","assets/Report-BaDAqP6S.css","assets/Example-DNOHy4qv.js","assets/AgentServiceAgreement-D0oVWSj9.js","assets/AgentServiceAgreement-CK_UWYQO.css","assets/Inquire-9DoGhFZB.js","assets/crypto-BwK018Q7.js","assets/Payment-Hq3a0NQ1.js","assets/index-DkWlWzWU.js","assets/index-D_wequ2-.css","assets/Payment-DwmHfgDF.css","assets/CarNumberInput-kufAefGG.js","assets/CarNumberInput-Bx_DUZrh.css","assets/Inquire-3Gq_0j3g.css","assets/PaymentResult-BIo51pxg.js","assets/PaymentResult-DrYIfc6u.css","assets/AgentPromoteDetails-DaVxxXhZ.js","assets/AgentPromoteDetails-CEyNFcdp.css","assets/AgentRewardsDetails-Ce1uKtNU.js","assets/AgentRewardsDetails-D4kKF5Pa.css","assets/Invitation-BqOb762L.js","assets/invitation-CQR0Q72G.js","assets/AgentVip-xsQMH3ix.js","assets/vip_bg-Ca3VoCiy.js","assets/AgentVipApply-B035qoW2.js","assets/AgentVipApply-Cj3dLW2J.css","assets/AgentVipConfig-Cwd5W1mV.js","assets/AgentVipConfig-CzfwOD_L.css","assets/WithdrawDetails-DqfIjLfk.js","assets/WithdrawDetails-PSqGX6hE.css","assets/InvitationAgentApply-BRDP4xVU.js","assets/invitation_agent_apply-4Z0EtpdC.js","assets/InvitationAgentApply-DNRPwVP-.css","assets/SubordinateList-Zky1_PwQ.js","assets/index-DCfa1bSZ.js","assets/index-D-A-V2Q_.css","assets/SubordinateList-DrZZbXQs.css","assets/SubordinateDetail-B2wITVyQ.js","assets/SubordinateDetail-Dh0S68LT.css","assets/Login-DrsmQHFZ.js","assets/Login-1gxS1Ox6.css","assets/PromotionInquire-xtNF1XAp.js","assets/PromotionInquire-Bxss2S8Y.css","assets/ReportShare-vY0DFMhW.js","assets/ReportShare-C2bDx6Bg.css","assets/NotFound-Dy1NN-TG.js","assets/NotFound-B_t_AsNp.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))o(r);new MutationObserver(r=>{for(const s of r)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&o(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const s={};return r.integrity&&(s.integrity=r.integrity),r.referrerPolicy&&(s.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?s.credentials="include":r.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function o(r){if(r.ep)return;r.ep=!0;const s=n(r);fetch(r.href,s)}})();/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Jr(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const se={},fn=[],mt=()=>{},Mc=()=>!1,Bo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Qr=e=>e.startsWith("onUpdate:"),we=Object.assign,Xr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},$c=Object.prototype.hasOwnProperty,ie=(e,t)=>$c.call(e,t),K=Array.isArray,dn=e=>no(e)==="[object Map]",Fo=e=>no(e)==="[object Set]",ks=e=>no(e)==="[object Date]",Q=e=>typeof e=="function",pe=e=>typeof e=="string",st=e=>typeof e=="symbol",ue=e=>e!==null&&typeof e=="object",sl=e=>(ue(e)||Q(e))&&Q(e.then)&&Q(e.catch),il=Object.prototype.toString,no=e=>il.call(e),Bc=e=>no(e).slice(8,-1),ll=e=>no(e)==="[object Object]",Zr=e=>pe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ln=Jr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),No=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Fc=/-(\w)/g,Je=No(e=>e.replace(Fc,(t,n)=>n?n.toUpperCase():"")),Nc=/\B([A-Z])/g,_t=No(e=>e.replace(Nc,"-$1").toLowerCase()),Vo=No(e=>e.charAt(0).toUpperCase()+e.slice(1)),sr=No(e=>e?`on${Vo(e)}`:""),He=(e,t)=>!Object.is(e,t),_o=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},al=(e,t,n,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:n})},Tr=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Vc=e=>{const t=pe(e)?Number(e):NaN;return isNaN(t)?e:t};let Ds;const jo=()=>Ds||(Ds=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ho(e){if(K(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=pe(o)?zc(o):Ho(o);if(r)for(const s in r)t[s]=r[s]}return t}else if(pe(e)||ue(e))return e}const jc=/;(?![^(]*\))/g,Hc=/:([^]+)/,Uc=/\/\*[^]*?\*\//g;function zc(e){const t={};return e.replace(Uc,"").split(jc).forEach(n=>{if(n){const o=n.split(Hc);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function Q0(e){if(!e)return"";if(pe(e))return e;let t="";for(const n in e){const o=e[n];if(pe(o)||typeof o=="number"){const r=n.startsWith("--")?n:_t(n);t+=`${r}:${o};`}}return t}function Pt(e){let t="";if(pe(e))t=e;else if(K(e))for(let n=0;n<e.length;n++){const o=Pt(e[n]);o&&(t+=o+" ")}else if(ue(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const qc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Kc=Jr(qc);function cl(e){return!!e||e===""}function Wc(e,t){if(e.length!==t.length)return!1;let n=!0;for(let o=0;n&&o<e.length;o++)n=Uo(e[o],t[o]);return n}function Uo(e,t){if(e===t)return!0;let n=ks(e),o=ks(t);if(n||o)return n&&o?e.getTime()===t.getTime():!1;if(n=st(e),o=st(t),n||o)return e===t;if(n=K(e),o=K(t),n||o)return n&&o?Wc(e,t):!1;if(n=ue(e),o=ue(t),n||o){if(!n||!o)return!1;const r=Object.keys(e).length,s=Object.keys(t).length;if(r!==s)return!1;for(const i in e){const l=e.hasOwnProperty(i),a=t.hasOwnProperty(i);if(l&&!a||!l&&a||!Uo(e[i],t[i]))return!1}}return String(e)===String(t)}function ul(e,t){return e.findIndex(n=>Uo(n,t))}const fl=e=>!!(e&&e.__v_isRef===!0),zo=e=>pe(e)?e:e==null?"":K(e)||ue(e)&&(e.toString===il||!Q(e.toString))?fl(e)?zo(e.value):JSON.stringify(e,dl,2):String(e),dl=(e,t)=>fl(t)?dl(e,t.value):dn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[o,r],s)=>(n[ir(o,s)+" =>"]=r,n),{})}:Fo(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>ir(n))}:st(t)?ir(t):ue(t)&&!K(t)&&!ll(t)?String(t):t,ir=(e,t="")=>{var n;return st(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Be;class hl{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Be,!t&&Be&&(this.index=(Be.scopes||(Be.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Be;try{return Be=this,t()}finally{Be=n}}}on(){Be=this}off(){Be=this.parent}stop(t){if(this._active){this._active=!1;let n,o;for(n=0,o=this.effects.length;n<o;n++)this.effects[n].stop();for(this.effects.length=0,n=0,o=this.cleanups.length;n<o;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,o=this.scopes.length;n<o;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function pl(e){return new hl(e)}function es(){return Be}function ml(e,t=!1){Be&&Be.cleanups.push(e)}let de;const lr=new WeakSet;class gl{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Be&&Be.active&&Be.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,lr.has(this)&&(lr.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||_l(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Ls(this),yl(this);const t=de,n=rt;de=this,rt=!0;try{return this.fn()}finally{bl(this),de=t,rt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)os(t);this.deps=this.depsTail=void 0,Ls(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?lr.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Pr(this)&&this.run()}get dirty(){return Pr(this)}}let vl=0,Mn,$n;function _l(e,t=!1){if(e.flags|=8,t){e.next=$n,$n=e;return}e.next=Mn,Mn=e}function ts(){vl++}function ns(){if(--vl>0)return;if($n){let t=$n;for($n=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Mn;){let t=Mn;for(Mn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(o){e||(e=o)}t=n}}if(e)throw e}function yl(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function bl(e){let t,n=e.depsTail,o=n;for(;o;){const r=o.prevDep;o.version===-1?(o===n&&(n=r),os(o),Gc(o)):t=o,o.dep.activeLink=o.prevActiveLink,o.prevActiveLink=void 0,o=r}e.deps=t,e.depsTail=n}function Pr(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(wl(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function wl(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Kn))return;e.globalVersion=Kn;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Pr(e)){e.flags&=-3;return}const n=de,o=rt;de=e,rt=!0;try{yl(e);const r=e.fn(e._value);(t.version===0||He(r,e._value))&&(e._value=r,t.version++)}catch(r){throw t.version++,r}finally{de=n,rt=o,bl(e),e.flags&=-3}}function os(e,t=!1){const{dep:n,prevSub:o,nextSub:r}=e;if(o&&(o.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=o,e.nextSub=void 0),n.subs===e&&(n.subs=o,!o&&n.computed)){n.computed.flags&=-5;for(let s=n.computed.deps;s;s=s.nextDep)os(s,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Gc(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let rt=!0;const xl=[];function Ht(){xl.push(rt),rt=!1}function Ut(){const e=xl.pop();rt=e===void 0?!0:e}function Ls(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=de;de=void 0;try{t()}finally{de=n}}}let Kn=0;class Yc{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class qo{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!de||!rt||de===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==de)n=this.activeLink=new Yc(de,this),de.deps?(n.prevDep=de.depsTail,de.depsTail.nextDep=n,de.depsTail=n):de.deps=de.depsTail=n,El(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const o=n.nextDep;o.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=o),n.prevDep=de.depsTail,n.nextDep=void 0,de.depsTail.nextDep=n,de.depsTail=n,de.deps===n&&(de.deps=o)}return n}trigger(t){this.version++,Kn++,this.notify(t)}notify(t){ts();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{ns()}}}function El(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let o=t.deps;o;o=o.nextDep)El(o)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const So=new WeakMap,Qt=Symbol(""),Or=Symbol(""),Wn=Symbol("");function Oe(e,t,n){if(rt&&de){let o=So.get(e);o||So.set(e,o=new Map);let r=o.get(n);r||(o.set(n,r=new qo),r.map=o,r.key=n),r.track()}}function At(e,t,n,o,r,s){const i=So.get(e);if(!i){Kn++;return}const l=a=>{a&&a.trigger()};if(ts(),t==="clear")i.forEach(l);else{const a=K(e),f=a&&Zr(n);if(a&&n==="length"){const c=Number(o);i.forEach((u,d)=>{(d==="length"||d===Wn||!st(d)&&d>=c)&&l(u)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),f&&l(i.get(Wn)),t){case"add":a?f&&l(i.get("length")):(l(i.get(Qt)),dn(e)&&l(i.get(Or)));break;case"delete":a||(l(i.get(Qt)),dn(e)&&l(i.get(Or)));break;case"set":dn(e)&&l(i.get(Qt));break}}ns()}function Jc(e,t){const n=So.get(e);return n&&n.get(t)}function nn(e){const t=re(e);return t===e?t:(Oe(t,"iterate",Wn),Ze(e)?t:t.map(Ie))}function Ko(e){return Oe(e=re(e),"iterate",Wn),e}const Qc={__proto__:null,[Symbol.iterator](){return ar(this,Symbol.iterator,Ie)},concat(...e){return nn(this).concat(...e.map(t=>K(t)?nn(t):t))},entries(){return ar(this,"entries",e=>(e[1]=Ie(e[1]),e))},every(e,t){return wt(this,"every",e,t,void 0,arguments)},filter(e,t){return wt(this,"filter",e,t,n=>n.map(Ie),arguments)},find(e,t){return wt(this,"find",e,t,Ie,arguments)},findIndex(e,t){return wt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return wt(this,"findLast",e,t,Ie,arguments)},findLastIndex(e,t){return wt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return wt(this,"forEach",e,t,void 0,arguments)},includes(...e){return cr(this,"includes",e)},indexOf(...e){return cr(this,"indexOf",e)},join(e){return nn(this).join(e)},lastIndexOf(...e){return cr(this,"lastIndexOf",e)},map(e,t){return wt(this,"map",e,t,void 0,arguments)},pop(){return Tn(this,"pop")},push(...e){return Tn(this,"push",e)},reduce(e,...t){return Ms(this,"reduce",e,t)},reduceRight(e,...t){return Ms(this,"reduceRight",e,t)},shift(){return Tn(this,"shift")},some(e,t){return wt(this,"some",e,t,void 0,arguments)},splice(...e){return Tn(this,"splice",e)},toReversed(){return nn(this).toReversed()},toSorted(e){return nn(this).toSorted(e)},toSpliced(...e){return nn(this).toSpliced(...e)},unshift(...e){return Tn(this,"unshift",e)},values(){return ar(this,"values",Ie)}};function ar(e,t,n){const o=Ko(e),r=o[t]();return o!==e&&!Ze(e)&&(r._next=r.next,r.next=()=>{const s=r._next();return s.value&&(s.value=n(s.value)),s}),r}const Xc=Array.prototype;function wt(e,t,n,o,r,s){const i=Ko(e),l=i!==e&&!Ze(e),a=i[t];if(a!==Xc[t]){const u=a.apply(e,s);return l?Ie(u):u}let f=n;i!==e&&(l?f=function(u,d){return n.call(this,Ie(u),d,e)}:n.length>2&&(f=function(u,d){return n.call(this,u,d,e)}));const c=a.call(i,f,o);return l&&r?r(c):c}function Ms(e,t,n,o){const r=Ko(e);let s=n;return r!==e&&(Ze(e)?n.length>3&&(s=function(i,l,a){return n.call(this,i,l,a,e)}):s=function(i,l,a){return n.call(this,i,Ie(l),a,e)}),r[t](s,...o)}function cr(e,t,n){const o=re(e);Oe(o,"iterate",Wn);const r=o[t](...n);return(r===-1||r===!1)&&is(n[0])?(n[0]=re(n[0]),o[t](...n)):r}function Tn(e,t,n=[]){Ht(),ts();const o=re(e)[t].apply(e,n);return ns(),Ut(),o}const Zc=Jr("__proto__,__v_isRef,__isVue"),Sl=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(st));function eu(e){st(e)||(e=String(e));const t=re(this);return Oe(t,"has",e),t.hasOwnProperty(e)}class Cl{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,o){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,s=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return s;if(n==="__v_raw")return o===(r?s?uu:Ol:s?Pl:Tl).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(o)?t:void 0;const i=K(t);if(!r){let a;if(i&&(a=Qc[n]))return a;if(n==="hasOwnProperty")return eu}const l=Reflect.get(t,n,he(t)?t:o);return(st(n)?Sl.has(n):Zc(n))||(r||Oe(t,"get",n),s)?l:he(l)?i&&Zr(n)?l:l.value:ue(l)?r?vn(l):gt(l):l}}class Al extends Cl{constructor(t=!1){super(!1,t)}set(t,n,o,r){let s=t[n];if(!this._isShallow){const a=Zt(s);if(!Ze(o)&&!Zt(o)&&(s=re(s),o=re(o)),!K(t)&&he(s)&&!he(o))return a?!1:(s.value=o,!0)}const i=K(t)&&Zr(n)?Number(n)<t.length:ie(t,n),l=Reflect.set(t,n,o,he(t)?t:r);return t===re(r)&&(i?He(o,s)&&At(t,"set",n,o):At(t,"add",n,o)),l}deleteProperty(t,n){const o=ie(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&o&&At(t,"delete",n,void 0),r}has(t,n){const o=Reflect.has(t,n);return(!st(n)||!Sl.has(n))&&Oe(t,"has",n),o}ownKeys(t){return Oe(t,"iterate",K(t)?"length":Qt),Reflect.ownKeys(t)}}class tu extends Cl{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const nu=new Al,ou=new tu,ru=new Al(!0);const Ir=e=>e,uo=e=>Reflect.getPrototypeOf(e);function su(e,t,n){return function(...o){const r=this.__v_raw,s=re(r),i=dn(s),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,f=r[e](...o),c=n?Ir:t?Rr:Ie;return!t&&Oe(s,"iterate",a?Or:Qt),{next(){const{value:u,done:d}=f.next();return d?{value:u,done:d}:{value:l?[c(u[0]),c(u[1])]:c(u),done:d}},[Symbol.iterator](){return this}}}}function fo(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function iu(e,t){const n={get(r){const s=this.__v_raw,i=re(s),l=re(r);e||(He(r,l)&&Oe(i,"get",r),Oe(i,"get",l));const{has:a}=uo(i),f=t?Ir:e?Rr:Ie;if(a.call(i,r))return f(s.get(r));if(a.call(i,l))return f(s.get(l));s!==i&&s.get(r)},get size(){const r=this.__v_raw;return!e&&Oe(re(r),"iterate",Qt),Reflect.get(r,"size",r)},has(r){const s=this.__v_raw,i=re(s),l=re(r);return e||(He(r,l)&&Oe(i,"has",r),Oe(i,"has",l)),r===l?s.has(r):s.has(r)||s.has(l)},forEach(r,s){const i=this,l=i.__v_raw,a=re(l),f=t?Ir:e?Rr:Ie;return!e&&Oe(a,"iterate",Qt),l.forEach((c,u)=>r.call(s,f(c),f(u),i))}};return we(n,e?{add:fo("add"),set:fo("set"),delete:fo("delete"),clear:fo("clear")}:{add(r){!t&&!Ze(r)&&!Zt(r)&&(r=re(r));const s=re(this);return uo(s).has.call(s,r)||(s.add(r),At(s,"add",r,r)),this},set(r,s){!t&&!Ze(s)&&!Zt(s)&&(s=re(s));const i=re(this),{has:l,get:a}=uo(i);let f=l.call(i,r);f||(r=re(r),f=l.call(i,r));const c=a.call(i,r);return i.set(r,s),f?He(s,c)&&At(i,"set",r,s):At(i,"add",r,s),this},delete(r){const s=re(this),{has:i,get:l}=uo(s);let a=i.call(s,r);a||(r=re(r),a=i.call(s,r)),l&&l.call(s,r);const f=s.delete(r);return a&&At(s,"delete",r,void 0),f},clear(){const r=re(this),s=r.size!==0,i=r.clear();return s&&At(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=su(r,e,t)}),n}function rs(e,t){const n=iu(e,t);return(o,r,s)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?o:Reflect.get(ie(n,r)&&r in o?n:o,r,s)}const lu={get:rs(!1,!1)},au={get:rs(!1,!0)},cu={get:rs(!0,!1)};const Tl=new WeakMap,Pl=new WeakMap,Ol=new WeakMap,uu=new WeakMap;function fu(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function du(e){return e.__v_skip||!Object.isExtensible(e)?0:fu(Bc(e))}function gt(e){return Zt(e)?e:ss(e,!1,nu,lu,Tl)}function Il(e){return ss(e,!1,ru,au,Pl)}function vn(e){return ss(e,!0,ou,cu,Ol)}function ss(e,t,n,o,r){if(!ue(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=r.get(e);if(s)return s;const i=du(e);if(i===0)return e;const l=new Proxy(e,i===2?o:n);return r.set(e,l),l}function Ot(e){return Zt(e)?Ot(e.__v_raw):!!(e&&e.__v_isReactive)}function Zt(e){return!!(e&&e.__v_isReadonly)}function Ze(e){return!!(e&&e.__v_isShallow)}function is(e){return e?!!e.__v_raw:!1}function re(e){const t=e&&e.__v_raw;return t?re(t):e}function ls(e){return!ie(e,"__v_skip")&&Object.isExtensible(e)&&al(e,"__v_skip",!0),e}const Ie=e=>ue(e)?gt(e):e,Rr=e=>ue(e)?vn(e):e;function he(e){return e?e.__v_isRef===!0:!1}function G(e){return Rl(e,!1)}function yo(e){return Rl(e,!0)}function Rl(e,t){return he(e)?e:new hu(e,t)}class hu{constructor(t,n){this.dep=new qo,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:re(t),this._value=n?t:Ie(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,o=this.__v_isShallow||Ze(t)||Zt(t);t=o?t:re(t),He(t,n)&&(this._rawValue=t,this._value=o?t:Ie(t),this.dep.trigger())}}function Re(e){return he(e)?e.value:e}const pu={get:(e,t,n)=>t==="__v_raw"?e:Re(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return he(r)&&!he(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function kl(e){return Ot(e)?e:new Proxy(e,pu)}class mu{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new qo,{get:o,set:r}=t(n.track.bind(n),n.trigger.bind(n));this._get=o,this._set=r}get value(){return this._value=this._get()}set value(t){this._set(t)}}function Dl(e){return new mu(e)}function gu(e){const t=K(e)?new Array(e.length):{};for(const n in e)t[n]=Ml(e,n);return t}class vu{constructor(t,n,o){this._object=t,this._key=n,this._defaultValue=o,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Jc(re(this._object),this._key)}}class _u{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function Ll(e,t,n){return he(e)?e:Q(e)?new _u(e):ue(e)&&arguments.length>1?Ml(e,t,n):G(e)}function Ml(e,t,n){const o=e[t];return he(o)?o:new vu(e,t,n)}class yu{constructor(t,n,o){this.fn=t,this.setter=n,this._value=void 0,this.dep=new qo(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Kn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=o}notify(){if(this.flags|=16,!(this.flags&8)&&de!==this)return _l(this,!0),!0}get value(){const t=this.dep.track();return wl(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function bu(e,t,n=!1){let o,r;return Q(e)?o=e:(o=e.get,r=e.set),new yu(o,r,n)}const ho={},Co=new WeakMap;let Gt;function wu(e,t=!1,n=Gt){if(n){let o=Co.get(n);o||Co.set(n,o=[]),o.push(e)}}function xu(e,t,n=se){const{immediate:o,deep:r,once:s,scheduler:i,augmentJob:l,call:a}=n,f=E=>r?E:Ze(E)||r===!1||r===0?Tt(E,1):Tt(E);let c,u,d,m,p=!1,v=!1;if(he(e)?(u=()=>e.value,p=Ze(e)):Ot(e)?(u=()=>f(e),p=!0):K(e)?(v=!0,p=e.some(E=>Ot(E)||Ze(E)),u=()=>e.map(E=>{if(he(E))return E.value;if(Ot(E))return f(E);if(Q(E))return a?a(E,2):E()})):Q(e)?t?u=a?()=>a(e,2):e:u=()=>{if(d){Ht();try{d()}finally{Ut()}}const E=Gt;Gt=c;try{return a?a(e,3,[m]):e(m)}finally{Gt=E}}:u=mt,t&&r){const E=u,k=r===!0?1/0:r;u=()=>Tt(E(),k)}const y=es(),w=()=>{c.stop(),y&&y.active&&Xr(y.effects,c)};if(s&&t){const E=t;t=(...k)=>{E(...k),w()}}let A=v?new Array(e.length).fill(ho):ho;const S=E=>{if(!(!(c.flags&1)||!c.dirty&&!E))if(t){const k=c.run();if(r||p||(v?k.some((M,F)=>He(M,A[F])):He(k,A))){d&&d();const M=Gt;Gt=c;try{const F=[k,A===ho?void 0:v&&A[0]===ho?[]:A,m];a?a(t,3,F):t(...F),A=k}finally{Gt=M}}}else c.run()};return l&&l(S),c=new gl(u),c.scheduler=i?()=>i(S,!1):S,m=E=>wu(E,!1,c),d=c.onStop=()=>{const E=Co.get(c);if(E){if(a)a(E,4);else for(const k of E)k();Co.delete(c)}},t?o?S(!0):A=c.run():i?i(S.bind(null,!0),!0):c.run(),w.pause=c.pause.bind(c),w.resume=c.resume.bind(c),w.stop=w,w}function Tt(e,t=1/0,n){if(t<=0||!ue(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,he(e))Tt(e.value,t,n);else if(K(e))for(let o=0;o<e.length;o++)Tt(e[o],t,n);else if(Fo(e)||dn(e))e.forEach(o=>{Tt(o,t,n)});else if(ll(e)){for(const o in e)Tt(e[o],t,n);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&Tt(e[o],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function oo(e,t,n,o){try{return o?e(...o):e()}catch(r){ro(r,t,n)}}function it(e,t,n,o){if(Q(e)){const r=oo(e,t,n,o);return r&&sl(r)&&r.catch(s=>{ro(s,t,n)}),r}if(K(e)){const r=[];for(let s=0;s<e.length;s++)r.push(it(e[s],t,n,o));return r}}function ro(e,t,n,o=!0){const r=t?t.vnode:null,{errorHandler:s,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||se;if(t){let l=t.parent;const a=t.proxy,f=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let u=0;u<c.length;u++)if(c[u](e,a,f)===!1)return}l=l.parent}if(s){Ht(),oo(s,null,10,[e,a,f]),Ut();return}}Eu(e,n,r,o,i)}function Eu(e,t,n,o=!0,r=!1){if(r)throw e;console.error(e)}const Fe=[];let dt=-1;const hn=[];let $t=null,an=0;const $l=Promise.resolve();let Ao=null;function vt(e){const t=Ao||$l;return e?t.then(this?e.bind(this):e):t}function Su(e){let t=dt+1,n=Fe.length;for(;t<n;){const o=t+n>>>1,r=Fe[o],s=Gn(r);s<e||s===e&&r.flags&2?t=o+1:n=o}return t}function as(e){if(!(e.flags&1)){const t=Gn(e),n=Fe[Fe.length-1];!n||!(e.flags&2)&&t>=Gn(n)?Fe.push(e):Fe.splice(Su(t),0,e),e.flags|=1,Bl()}}function Bl(){Ao||(Ao=$l.then(Nl))}function Cu(e){K(e)?hn.push(...e):$t&&e.id===-1?$t.splice(an+1,0,e):e.flags&1||(hn.push(e),e.flags|=1),Bl()}function $s(e,t,n=dt+1){for(;n<Fe.length;n++){const o=Fe[n];if(o&&o.flags&2){if(e&&o.id!==e.uid)continue;Fe.splice(n,1),n--,o.flags&4&&(o.flags&=-2),o(),o.flags&4||(o.flags&=-2)}}}function Fl(e){if(hn.length){const t=[...new Set(hn)].sort((n,o)=>Gn(n)-Gn(o));if(hn.length=0,$t){$t.push(...t);return}for($t=t,an=0;an<$t.length;an++){const n=$t[an];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}$t=null,an=0}}const Gn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Nl(e){try{for(dt=0;dt<Fe.length;dt++){const t=Fe[dt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),oo(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;dt<Fe.length;dt++){const t=Fe[dt];t&&(t.flags&=-2)}dt=-1,Fe.length=0,Fl(),Ao=null,(Fe.length||hn.length)&&Nl()}}let Ce=null,Vl=null;function To(e){const t=Ce;return Ce=e,Vl=e&&e.type.__scopeId||null,t}function _n(e,t=Ce,n){if(!t||e._n)return e;const o=(...r)=>{o._d&&Ys(-1);const s=To(t);let i;try{i=e(...r)}finally{To(s),o._d&&Ys(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function Bn(e,t){if(Ce===null)return e;const n=Zo(Ce),o=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[s,i,l,a=se]=t[r];s&&(Q(s)&&(s={mounted:s,updated:s}),s.deep&&Tt(i),o.push({dir:s,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function zt(e,t,n,o){const r=e.dirs,s=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];s&&(l.oldValue=s[i].value);let a=l.dir[o];a&&(Ht(),it(a,n,8,[e.el,l,e,t]),Ut())}}const jl=Symbol("_vte"),Hl=e=>e.__isTeleport,Fn=e=>e&&(e.disabled||e.disabled===""),Bs=e=>e&&(e.defer||e.defer===""),Fs=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Ns=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,kr=(e,t)=>{const n=e&&e.to;return pe(n)?t?t(n):null:n},Ul={name:"Teleport",__isTeleport:!0,process(e,t,n,o,r,s,i,l,a,f){const{mc:c,pc:u,pbc:d,o:{insert:m,querySelector:p,createText:v,createComment:y}}=f,w=Fn(t.props);let{shapeFlag:A,children:S,dynamicChildren:E}=t;if(e==null){const k=t.el=v(""),M=t.anchor=v("");m(k,n,o),m(M,n,o);const F=(P,U)=>{A&16&&(r&&r.isCE&&(r.ce._teleportTarget=P),c(S,P,U,r,s,i,l,a))},C=()=>{const P=t.target=kr(t.props,p),U=ql(P,t,v,m);P&&(i!=="svg"&&Fs(P)?i="svg":i!=="mathml"&&Ns(P)&&(i="mathml"),w||(F(P,U),bo(t,!1)))};w&&(F(n,M),bo(t,!0)),Bs(t.props)?$e(()=>{C(),t.el.__isMounted=!0},s):C()}else{if(Bs(t.props)&&!e.el.__isMounted){$e(()=>{Ul.process(e,t,n,o,r,s,i,l,a,f),delete e.el.__isMounted},s);return}t.el=e.el,t.targetStart=e.targetStart;const k=t.anchor=e.anchor,M=t.target=e.target,F=t.targetAnchor=e.targetAnchor,C=Fn(e.props),P=C?n:M,U=C?k:F;if(i==="svg"||Fs(M)?i="svg":(i==="mathml"||Ns(M))&&(i="mathml"),E?(d(e.dynamicChildren,E,P,r,s,i,l),ps(e,t,!0)):a||u(e,t,P,U,r,s,i,l,!1),w)C?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):po(t,n,k,f,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const Y=t.target=kr(t.props,p);Y&&po(t,Y,null,f,0)}else C&&po(t,M,F,f,1);bo(t,w)}},remove(e,t,n,{um:o,o:{remove:r}},s){const{shapeFlag:i,children:l,anchor:a,targetStart:f,targetAnchor:c,target:u,props:d}=e;if(u&&(r(f),r(c)),s&&r(a),i&16){const m=s||!Fn(d);for(let p=0;p<l.length;p++){const v=l[p];o(v,t,n,m,!!v.dynamicChildren)}}},move:po,hydrate:Au};function po(e,t,n,{o:{insert:o},m:r},s=2){s===0&&o(e.targetAnchor,t,n);const{el:i,anchor:l,shapeFlag:a,children:f,props:c}=e,u=s===2;if(u&&o(i,t,n),(!u||Fn(c))&&a&16)for(let d=0;d<f.length;d++)r(f[d],t,n,2);u&&o(l,t,n)}function Au(e,t,n,o,r,s,{o:{nextSibling:i,parentNode:l,querySelector:a,insert:f,createText:c}},u){const d=t.target=kr(t.props,a);if(d){const m=Fn(t.props),p=d._lpa||d.firstChild;if(t.shapeFlag&16)if(m)t.anchor=u(i(e),t,l(e),n,o,r,s),t.targetStart=p,t.targetAnchor=p&&i(p);else{t.anchor=i(e);let v=p;for(;v;){if(v&&v.nodeType===8){if(v.data==="teleport start anchor")t.targetStart=v;else if(v.data==="teleport anchor"){t.targetAnchor=v,d._lpa=t.targetAnchor&&i(t.targetAnchor);break}}v=i(v)}t.targetAnchor||ql(d,t,c,f),u(p&&i(p),t,d,n,o,r,s)}bo(t,m)}return t.anchor&&i(t.anchor)}const zl=Ul;function bo(e,t){const n=e.ctx;if(n&&n.ut){let o,r;for(t?(o=e.el,r=e.anchor):(o=e.targetStart,r=e.targetAnchor);o&&o!==r;)o.nodeType===1&&o.setAttribute("data-v-owner",n.uid),o=o.nextSibling;n.ut()}}function ql(e,t,n,o){const r=t.targetStart=n(""),s=t.targetAnchor=n("");return r[jl]=s,e&&(o(r,e),o(s,e)),s}const Bt=Symbol("_leaveCb"),mo=Symbol("_enterCb");function Tu(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return at(()=>{e.isMounted=!0}),fs(()=>{e.isUnmounting=!0}),e}const Xe=[Function,Array],Kl={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Xe,onEnter:Xe,onAfterEnter:Xe,onEnterCancelled:Xe,onBeforeLeave:Xe,onLeave:Xe,onAfterLeave:Xe,onLeaveCancelled:Xe,onBeforeAppear:Xe,onAppear:Xe,onAfterAppear:Xe,onAppearCancelled:Xe},Wl=e=>{const t=e.subTree;return t.component?Wl(t.component):t},Pu={name:"BaseTransition",props:Kl,setup(e,{slots:t}){const n=kt(),o=Tu();return()=>{const r=t.default&&Jl(t.default(),!0);if(!r||!r.length)return;const s=Gl(r),i=re(e),{mode:l}=i;if(o.isLeaving)return ur(s);const a=Vs(s);if(!a)return ur(s);let f=Dr(a,i,o,n,u=>f=u);a.type!==Ne&&Yn(a,f);let c=n.subTree&&Vs(n.subTree);if(c&&c.type!==Ne&&!Yt(a,c)&&Wl(n).type!==Ne){let u=Dr(c,i,o,n);if(Yn(c,u),l==="out-in"&&a.type!==Ne)return o.isLeaving=!0,u.afterLeave=()=>{o.isLeaving=!1,n.job.flags&8||n.update(),delete u.afterLeave,c=void 0},ur(s);l==="in-out"&&a.type!==Ne?u.delayLeave=(d,m,p)=>{const v=Yl(o,c);v[String(c.key)]=c,d[Bt]=()=>{m(),d[Bt]=void 0,delete f.delayedLeave,c=void 0},f.delayedLeave=()=>{p(),delete f.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return s}}};function Gl(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==Ne){t=n;break}}return t}const Ou=Pu;function Yl(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function Dr(e,t,n,o,r){const{appear:s,mode:i,persisted:l=!1,onBeforeEnter:a,onEnter:f,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:m,onAfterLeave:p,onLeaveCancelled:v,onBeforeAppear:y,onAppear:w,onAfterAppear:A,onAppearCancelled:S}=t,E=String(e.key),k=Yl(n,e),M=(P,U)=>{P&&it(P,o,9,U)},F=(P,U)=>{const Y=U[1];M(P,U),K(P)?P.every($=>$.length<=1)&&Y():P.length<=1&&Y()},C={mode:i,persisted:l,beforeEnter(P){let U=a;if(!n.isMounted)if(s)U=y||a;else return;P[Bt]&&P[Bt](!0);const Y=k[E];Y&&Yt(e,Y)&&Y.el[Bt]&&Y.el[Bt](),M(U,[P])},enter(P){let U=f,Y=c,$=u;if(!n.isMounted)if(s)U=w||f,Y=A||c,$=S||u;else return;let Z=!1;const me=P[mo]=ye=>{Z||(Z=!0,ye?M($,[P]):M(Y,[P]),C.delayedLeave&&C.delayedLeave(),P[mo]=void 0)};U?F(U,[P,me]):me()},leave(P,U){const Y=String(e.key);if(P[mo]&&P[mo](!0),n.isUnmounting)return U();M(d,[P]);let $=!1;const Z=P[Bt]=me=>{$||($=!0,U(),me?M(v,[P]):M(p,[P]),P[Bt]=void 0,k[Y]===e&&delete k[Y])};k[Y]=e,m?F(m,[P,Z]):Z()},clone(P){const U=Dr(P,t,n,o,r);return r&&r(U),U}};return C}function ur(e){if(so(e))return e=Vt(e),e.children=null,e}function Vs(e){if(!so(e))return Hl(e.type)&&e.children?Gl(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&Q(n.default))return n.default()}}function Yn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Yn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Jl(e,t=!1,n){let o=[],r=0;for(let s=0;s<e.length;s++){let i=e[s];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:s);i.type===Ee?(i.patchFlag&128&&r++,o=o.concat(Jl(i.children,t,l))):(t||i.type!==Ne)&&o.push(l!=null?Vt(i,{key:l}):i)}if(r>1)for(let s=0;s<o.length;s++)o[s].patchFlag=-2;return o}/*! #__NO_SIDE_EFFECTS__ */function Qe(e,t){return Q(e)?we({name:e.name},t,{setup:e}):e}function cs(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Po(e,t,n,o,r=!1){if(K(e)){e.forEach((p,v)=>Po(p,t&&(K(t)?t[v]:t),n,o,r));return}if(pn(o)&&!r){o.shapeFlag&512&&o.type.__asyncResolved&&o.component.subTree.component&&Po(e,t,n,o.component.subTree);return}const s=o.shapeFlag&4?Zo(o.component):o.el,i=r?null:s,{i:l,r:a}=e,f=t&&t.r,c=l.refs===se?l.refs={}:l.refs,u=l.setupState,d=re(u),m=u===se?()=>!1:p=>ie(d,p);if(f!=null&&f!==a&&(pe(f)?(c[f]=null,m(f)&&(u[f]=null)):he(f)&&(f.value=null)),Q(a))oo(a,l,12,[i,c]);else{const p=pe(a),v=he(a);if(p||v){const y=()=>{if(e.f){const w=p?m(a)?u[a]:c[a]:a.value;r?K(w)&&Xr(w,s):K(w)?w.includes(s)||w.push(s):p?(c[a]=[s],m(a)&&(u[a]=c[a])):(a.value=[s],e.k&&(c[e.k]=a.value))}else p?(c[a]=i,m(a)&&(u[a]=i)):v&&(a.value=i,e.k&&(c[e.k]=i))};i?(y.id=-1,$e(y,n)):y()}}}const js=e=>e.nodeType===8;jo().requestIdleCallback;jo().cancelIdleCallback;function Iu(e,t){if(js(e)&&e.data==="["){let n=1,o=e.nextSibling;for(;o;){if(o.nodeType===1){if(t(o)===!1)break}else if(js(o))if(o.data==="]"){if(--n===0)break}else o.data==="["&&n++;o=o.nextSibling}}else t(e)}const pn=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function X0(e){Q(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,hydrate:s,timeout:i,suspensible:l=!0,onError:a}=e;let f=null,c,u=0;const d=()=>(u++,f=null,m()),m=()=>{let p;return f||(p=f=t().catch(v=>{if(v=v instanceof Error?v:new Error(String(v)),a)return new Promise((y,w)=>{a(v,()=>y(d()),()=>w(v),u+1)});throw v}).then(v=>p!==f&&f?f:(v&&(v.__esModule||v[Symbol.toStringTag]==="Module")&&(v=v.default),c=v,v)))};return Qe({name:"AsyncComponentWrapper",__asyncLoader:m,__asyncHydrate(p,v,y){const w=s?()=>{const A=s(y,S=>Iu(p,S));A&&(v.bum||(v.bum=[])).push(A)}:y;c?w():m().then(()=>!v.isUnmounted&&w())},get __asyncResolved(){return c},setup(){const p=Se;if(cs(p),c)return()=>fr(c,p);const v=S=>{f=null,ro(S,p,13,!o)};if(l&&p.suspense||bn)return m().then(S=>()=>fr(S,p)).catch(S=>(v(S),()=>o?L(o,{error:S}):null));const y=G(!1),w=G(),A=G(!!r);return r&&setTimeout(()=>{A.value=!1},r),i!=null&&setTimeout(()=>{if(!y.value&&!w.value){const S=new Error(`Async component timed out after ${i}ms.`);v(S),w.value=S}},i),m().then(()=>{y.value=!0,p.parent&&so(p.parent.vnode)&&p.parent.update()}).catch(S=>{v(S),w.value=S}),()=>{if(y.value&&c)return fr(c,p);if(w.value&&o)return L(o,{error:w.value});if(n&&!A.value)return L(n)}}})}function fr(e,t){const{ref:n,props:o,children:r,ce:s}=t.vnode,i=L(e,o,r);return i.ref=n,i.ce=s,delete t.vnode.ce,i}const so=e=>e.type.__isKeepAlive;function us(e,t){Ql(e,"a",t)}function Wo(e,t){Ql(e,"da",t)}function Ql(e,t,n=Se){const o=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Go(t,o,n),n){let r=n.parent;for(;r&&r.parent;)so(r.parent.vnode)&&Ru(o,t,n,r),r=r.parent}}function Ru(e,t,n,o){const r=Go(t,e,o,!0);io(()=>{Xr(o[t],r)},n)}function Go(e,t,n=Se,o=!1){if(n){const r=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...i)=>{Ht();const l=lo(n),a=it(t,n,e,i);return l(),Ut(),a});return o?r.unshift(s):r.push(s),s}}const Rt=e=>(t,n=Se)=>{(!bn||e==="sp")&&Go(e,(...o)=>t(...o),n)},ku=Rt("bm"),at=Rt("m"),Du=Rt("bu"),Lu=Rt("u"),fs=Rt("bum"),io=Rt("um"),Mu=Rt("sp"),$u=Rt("rtg"),Bu=Rt("rtc");function Fu(e,t=Se){Go("ec",e,t)}const Xl="components";function Yo(e,t){return ea(Xl,e,!0,t)||e}const Zl=Symbol.for("v-ndc");function Z0(e){return pe(e)?ea(Xl,e,!1)||e:e||Zl}function ea(e,t,n=!0,o=!1){const r=Ce||Se;if(r){const s=r.type;{const l=Tf(s,!1);if(l&&(l===t||l===Je(t)||l===Vo(Je(t))))return s}const i=Hs(r[e]||s[e],t)||Hs(r.appContext[e],t);return!i&&o?s:i}}function Hs(e,t){return e&&(e[t]||e[Je(t)]||e[Vo(Je(t))])}function ta(e,t,n,o){let r;const s=n,i=K(e);if(i||pe(e)){const l=i&&Ot(e);let a=!1;l&&(a=!Ze(e),e=Ko(e)),r=new Array(e.length);for(let f=0,c=e.length;f<c;f++)r[f]=t(a?Ie(e[f]):e[f],f,void 0,s)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,s)}else if(ue(e))if(e[Symbol.iterator])r=Array.from(e,(l,a)=>t(l,a,void 0,s));else{const l=Object.keys(e);r=new Array(l.length);for(let a=0,f=l.length;a<f;a++){const c=l[a];r[a]=t(e[c],c,a,s)}}else r=[];return r}function em(e,t,n={},o,r){if(Ce.ce||Ce.parent&&pn(Ce.parent)&&Ce.parent.ce)return t!=="default"&&(n.name=t),Ye(),Ro(Ee,null,[L("slot",n,o)],64);let s=e[t];s&&s._c&&(s._d=!1),Ye();const i=s&&na(s(n)),l=n.key||i&&i.key,a=Ro(Ee,{key:(l&&!st(l)?l:`_${t}`)+(!i&&o?"_fb":"")},i||[],i&&e._===1?64:-2);return s&&s._c&&(s._d=!0),a}function na(e){return e.some(t=>yn(t)?!(t.type===Ne||t.type===Ee&&!na(t.children)):!0)?e:null}const Lr=e=>e?wa(e)?Zo(e):Lr(e.parent):null,Nn=we(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Lr(e.parent),$root:e=>Lr(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ds(e),$forceUpdate:e=>e.f||(e.f=()=>{as(e.update)}),$nextTick:e=>e.n||(e.n=vt.bind(e.proxy)),$watch:e=>af.bind(e)}),dr=(e,t)=>e!==se&&!e.__isScriptSetup&&ie(e,t),Nu={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:o,data:r,props:s,accessCache:i,type:l,appContext:a}=e;let f;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return s[t]}else{if(dr(o,t))return i[t]=1,o[t];if(r!==se&&ie(r,t))return i[t]=2,r[t];if((f=e.propsOptions[0])&&ie(f,t))return i[t]=3,s[t];if(n!==se&&ie(n,t))return i[t]=4,n[t];Mr&&(i[t]=0)}}const c=Nn[t];let u,d;if(c)return t==="$attrs"&&Oe(e.attrs,"get",""),c(e);if((u=l.__cssModules)&&(u=u[t]))return u;if(n!==se&&ie(n,t))return i[t]=4,n[t];if(d=a.config.globalProperties,ie(d,t))return d[t]},set({_:e},t,n){const{data:o,setupState:r,ctx:s}=e;return dr(r,t)?(r[t]=n,!0):o!==se&&ie(o,t)?(o[t]=n,!0):ie(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:s}},i){let l;return!!n[i]||e!==se&&ie(e,i)||dr(t,i)||(l=s[0])&&ie(l,i)||ie(o,i)||ie(Nn,i)||ie(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ie(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Oo(e){return K(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}function tm(e,t){return!e||!t?e||t:K(e)&&K(t)?e.concat(t):we({},Oo(e),Oo(t))}let Mr=!0;function Vu(e){const t=ds(e),n=e.proxy,o=e.ctx;Mr=!1,t.beforeCreate&&Us(t.beforeCreate,e,"bc");const{data:r,computed:s,methods:i,watch:l,provide:a,inject:f,created:c,beforeMount:u,mounted:d,beforeUpdate:m,updated:p,activated:v,deactivated:y,beforeDestroy:w,beforeUnmount:A,destroyed:S,unmounted:E,render:k,renderTracked:M,renderTriggered:F,errorCaptured:C,serverPrefetch:P,expose:U,inheritAttrs:Y,components:$,directives:Z,filters:me}=t;if(f&&ju(f,o,null),i)for(const W in i){const ne=i[W];Q(ne)&&(o[W]=ne.bind(n))}if(r){const W=r.call(n,n);ue(W)&&(e.data=gt(W))}if(Mr=!0,s)for(const W in s){const ne=s[W],De=Q(ne)?ne.bind(n,n):Q(ne.get)?ne.get.bind(n,n):mt,Te=!Q(ne)&&Q(ne.set)?ne.set.bind(n):mt,Ue=_e({get:De,set:Te});Object.defineProperty(o,W,{enumerable:!0,configurable:!0,get:()=>Ue.value,set:xe=>Ue.value=xe})}if(l)for(const W in l)oa(l[W],o,n,W);if(a){const W=Q(a)?a.call(n):a;Reflect.ownKeys(W).forEach(ne=>{mn(ne,W[ne])})}c&&Us(c,e,"c");function ee(W,ne){K(ne)?ne.forEach(De=>W(De.bind(n))):ne&&W(ne.bind(n))}if(ee(ku,u),ee(at,d),ee(Du,m),ee(Lu,p),ee(us,v),ee(Wo,y),ee(Fu,C),ee(Bu,M),ee($u,F),ee(fs,A),ee(io,E),ee(Mu,P),K(U))if(U.length){const W=e.exposed||(e.exposed={});U.forEach(ne=>{Object.defineProperty(W,ne,{get:()=>n[ne],set:De=>n[ne]=De})})}else e.exposed||(e.exposed={});k&&e.render===mt&&(e.render=k),Y!=null&&(e.inheritAttrs=Y),$&&(e.components=$),Z&&(e.directives=Z),P&&cs(e)}function ju(e,t,n=mt){K(e)&&(e=$r(e));for(const o in e){const r=e[o];let s;ue(r)?"default"in r?s=ke(r.from||o,r.default,!0):s=ke(r.from||o):s=ke(r),he(s)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>s.value,set:i=>s.value=i}):t[o]=s}}function Us(e,t,n){it(K(e)?e.map(o=>o.bind(t.proxy)):e.bind(t.proxy),t,n)}function oa(e,t,n,o){let r=o.includes(".")?ma(n,o):()=>n[o];if(pe(e)){const s=t[e];Q(s)&&be(r,s)}else if(Q(e))be(r,e.bind(n));else if(ue(e))if(K(e))e.forEach(s=>oa(s,t,n,o));else{const s=Q(e.handler)?e.handler.bind(n):t[e.handler];Q(s)&&be(r,s,e)}}function ds(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:s,config:{optionMergeStrategies:i}}=e.appContext,l=s.get(t);let a;return l?a=l:!r.length&&!n&&!o?a=t:(a={},r.length&&r.forEach(f=>Io(a,f,i,!0)),Io(a,t,i)),ue(t)&&s.set(t,a),a}function Io(e,t,n,o=!1){const{mixins:r,extends:s}=t;s&&Io(e,s,n,!0),r&&r.forEach(i=>Io(e,i,n,!0));for(const i in t)if(!(o&&i==="expose")){const l=Hu[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Hu={data:zs,props:qs,emits:qs,methods:Dn,computed:Dn,beforeCreate:Me,created:Me,beforeMount:Me,mounted:Me,beforeUpdate:Me,updated:Me,beforeDestroy:Me,beforeUnmount:Me,destroyed:Me,unmounted:Me,activated:Me,deactivated:Me,errorCaptured:Me,serverPrefetch:Me,components:Dn,directives:Dn,watch:zu,provide:zs,inject:Uu};function zs(e,t){return t?e?function(){return we(Q(e)?e.call(this,this):e,Q(t)?t.call(this,this):t)}:t:e}function Uu(e,t){return Dn($r(e),$r(t))}function $r(e){if(K(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Me(e,t){return e?[...new Set([].concat(e,t))]:t}function Dn(e,t){return e?we(Object.create(null),e,t):t}function qs(e,t){return e?K(e)&&K(t)?[...new Set([...e,...t])]:we(Object.create(null),Oo(e),Oo(t??{})):t}function zu(e,t){if(!e)return t;if(!t)return e;const n=we(Object.create(null),e);for(const o in t)n[o]=Me(e[o],t[o]);return n}function ra(){return{app:null,config:{isNativeTag:Mc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let qu=0;function Ku(e,t){return function(o,r=null){Q(o)||(o=we({},o)),r!=null&&!ue(r)&&(r=null);const s=ra(),i=new WeakSet,l=[];let a=!1;const f=s.app={_uid:qu++,_component:o,_props:r,_container:null,_context:s,_instance:null,version:Of,get config(){return s.config},set config(c){},use(c,...u){return i.has(c)||(c&&Q(c.install)?(i.add(c),c.install(f,...u)):Q(c)&&(i.add(c),c(f,...u))),f},mixin(c){return s.mixins.includes(c)||s.mixins.push(c),f},component(c,u){return u?(s.components[c]=u,f):s.components[c]},directive(c,u){return u?(s.directives[c]=u,f):s.directives[c]},mount(c,u,d){if(!a){const m=f._ceVNode||L(o,r);return m.appContext=s,d===!0?d="svg":d===!1&&(d=void 0),u&&t?t(m,c):e(m,c,d),a=!0,f._container=c,c.__vue_app__=f,Zo(m.component)}},onUnmount(c){l.push(c)},unmount(){a&&(it(l,f._instance,16),e(null,f._container),delete f._container.__vue_app__)},provide(c,u){return s.provides[c]=u,f},runWithContext(c){const u=Xt;Xt=f;try{return c()}finally{Xt=u}}};return f}}let Xt=null;function mn(e,t){if(Se){let n=Se.provides;const o=Se.parent&&Se.parent.provides;o===n&&(n=Se.provides=Object.create(o)),n[e]=t}}function ke(e,t,n=!1){const o=Se||Ce;if(o||Xt){const r=Xt?Xt._context.provides:o?o.parent==null?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&Q(t)?t.call(o&&o.proxy):t}}function Wu(){return!!(Se||Ce||Xt)}const sa={},ia=()=>Object.create(sa),la=e=>Object.getPrototypeOf(e)===sa;function Gu(e,t,n,o=!1){const r={},s=ia();e.propsDefaults=Object.create(null),aa(e,t,r,s);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=o?r:Il(r):e.type.props?e.props=r:e.props=s,e.attrs=s}function Yu(e,t,n,o){const{props:r,attrs:s,vnode:{patchFlag:i}}=e,l=re(r),[a]=e.propsOptions;let f=!1;if((o||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let u=0;u<c.length;u++){let d=c[u];if(Qo(e.emitsOptions,d))continue;const m=t[d];if(a)if(ie(s,d))m!==s[d]&&(s[d]=m,f=!0);else{const p=Je(d);r[p]=Br(a,l,p,m,e,!1)}else m!==s[d]&&(s[d]=m,f=!0)}}}else{aa(e,t,r,s)&&(f=!0);let c;for(const u in l)(!t||!ie(t,u)&&((c=_t(u))===u||!ie(t,c)))&&(a?n&&(n[u]!==void 0||n[c]!==void 0)&&(r[u]=Br(a,l,u,void 0,e,!0)):delete r[u]);if(s!==l)for(const u in s)(!t||!ie(t,u))&&(delete s[u],f=!0)}f&&At(e.attrs,"set","")}function aa(e,t,n,o){const[r,s]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(Ln(a))continue;const f=t[a];let c;r&&ie(r,c=Je(a))?!s||!s.includes(c)?n[c]=f:(l||(l={}))[c]=f:Qo(e.emitsOptions,a)||(!(a in o)||f!==o[a])&&(o[a]=f,i=!0)}if(s){const a=re(n),f=l||se;for(let c=0;c<s.length;c++){const u=s[c];n[u]=Br(r,a,u,f[u],e,!ie(f,u))}}return i}function Br(e,t,n,o,r,s){const i=e[n];if(i!=null){const l=ie(i,"default");if(l&&o===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&Q(a)){const{propsDefaults:f}=r;if(n in f)o=f[n];else{const c=lo(r);o=f[n]=a.call(null,t),c()}}else o=a;r.ce&&r.ce._setProp(n,o)}i[0]&&(s&&!l?o=!1:i[1]&&(o===""||o===_t(n))&&(o=!0))}return o}const Ju=new WeakMap;function ca(e,t,n=!1){const o=n?Ju:t.propsCache,r=o.get(e);if(r)return r;const s=e.props,i={},l=[];let a=!1;if(!Q(e)){const c=u=>{a=!0;const[d,m]=ca(u,t,!0);we(i,d),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!s&&!a)return ue(e)&&o.set(e,fn),fn;if(K(s))for(let c=0;c<s.length;c++){const u=Je(s[c]);Ks(u)&&(i[u]=se)}else if(s)for(const c in s){const u=Je(c);if(Ks(u)){const d=s[c],m=i[u]=K(d)||Q(d)?{type:d}:we({},d),p=m.type;let v=!1,y=!0;if(K(p))for(let w=0;w<p.length;++w){const A=p[w],S=Q(A)&&A.name;if(S==="Boolean"){v=!0;break}else S==="String"&&(y=!1)}else v=Q(p)&&p.name==="Boolean";m[0]=v,m[1]=y,(v||ie(m,"default"))&&l.push(u)}}const f=[i,l];return ue(e)&&o.set(e,f),f}function Ks(e){return e[0]!=="$"&&!Ln(e)}const ua=e=>e[0]==="_"||e==="$stable",hs=e=>K(e)?e.map(ht):[ht(e)],Qu=(e,t,n)=>{if(t._n)return t;const o=_n((...r)=>hs(t(...r)),n);return o._c=!1,o},fa=(e,t,n)=>{const o=e._ctx;for(const r in e){if(ua(r))continue;const s=e[r];if(Q(s))t[r]=Qu(r,s,o);else if(s!=null){const i=hs(s);t[r]=()=>i}}},da=(e,t)=>{const n=hs(t);e.slots.default=()=>n},ha=(e,t,n)=>{for(const o in t)(n||o!=="_")&&(e[o]=t[o])},Xu=(e,t,n)=>{const o=e.slots=ia();if(e.vnode.shapeFlag&32){const r=t._;r?(ha(o,t,n),n&&al(o,"_",r,!0)):fa(t,o)}else t&&da(e,t)},Zu=(e,t,n)=>{const{vnode:o,slots:r}=e;let s=!0,i=se;if(o.shapeFlag&32){const l=t._;l?n&&l===1?s=!1:ha(r,t,n):(s=!t.$stable,fa(t,r)),i=t}else t&&(da(e,t),i={default:1});if(s)for(const l in r)!ua(l)&&i[l]==null&&delete r[l]},$e=pf;function ef(e){return tf(e)}function tf(e,t){const n=jo();n.__VUE__=!0;const{insert:o,remove:r,patchProp:s,createElement:i,createText:l,createComment:a,setText:f,setElementText:c,parentNode:u,nextSibling:d,setScopeId:m=mt,insertStaticContent:p}=e,v=(h,g,_,T=null,b=null,O=null,B=void 0,D=null,R=!!g.dynamicChildren)=>{if(h===g)return;h&&!Yt(h,g)&&(T=x(h),xe(h,b,O,!0),h=null),g.patchFlag===-2&&(R=!1,g.dynamicChildren=null);const{type:I,ref:J,shapeFlag:j}=g;switch(I){case Xo:y(h,g,_,T);break;case Ne:w(h,g,_,T);break;case wo:h==null&&A(g,_,T,B);break;case Ee:$(h,g,_,T,b,O,B,D,R);break;default:j&1?k(h,g,_,T,b,O,B,D,R):j&6?Z(h,g,_,T,b,O,B,D,R):(j&64||j&128)&&I.process(h,g,_,T,b,O,B,D,R,z)}J!=null&&b&&Po(J,h&&h.ref,O,g||h,!g)},y=(h,g,_,T)=>{if(h==null)o(g.el=l(g.children),_,T);else{const b=g.el=h.el;g.children!==h.children&&f(b,g.children)}},w=(h,g,_,T)=>{h==null?o(g.el=a(g.children||""),_,T):g.el=h.el},A=(h,g,_,T)=>{[h.el,h.anchor]=p(h.children,g,_,T,h.el,h.anchor)},S=({el:h,anchor:g},_,T)=>{let b;for(;h&&h!==g;)b=d(h),o(h,_,T),h=b;o(g,_,T)},E=({el:h,anchor:g})=>{let _;for(;h&&h!==g;)_=d(h),r(h),h=_;r(g)},k=(h,g,_,T,b,O,B,D,R)=>{g.type==="svg"?B="svg":g.type==="math"&&(B="mathml"),h==null?M(g,_,T,b,O,B,D,R):P(h,g,b,O,B,D,R)},M=(h,g,_,T,b,O,B,D)=>{let R,I;const{props:J,shapeFlag:j,transition:q,dirs:X}=h;if(R=h.el=i(h.type,O,J&&J.is,J),j&8?c(R,h.children):j&16&&C(h.children,R,null,T,b,hr(h,O),B,D),X&&zt(h,null,T,"created"),F(R,h,h.scopeId,B,T),J){for(const fe in J)fe!=="value"&&!Ln(fe)&&s(R,fe,null,J[fe],O,T);"value"in J&&s(R,"value",null,J.value,O),(I=J.onVnodeBeforeMount)&&ft(I,T,h)}X&&zt(h,null,T,"beforeMount");const oe=nf(b,q);oe&&q.beforeEnter(R),o(R,g,_),((I=J&&J.onVnodeMounted)||oe||X)&&$e(()=>{I&&ft(I,T,h),oe&&q.enter(R),X&&zt(h,null,T,"mounted")},b)},F=(h,g,_,T,b)=>{if(_&&m(h,_),T)for(let O=0;O<T.length;O++)m(h,T[O]);if(b){let O=b.subTree;if(g===O||_a(O.type)&&(O.ssContent===g||O.ssFallback===g)){const B=b.vnode;F(h,B,B.scopeId,B.slotScopeIds,b.parent)}}},C=(h,g,_,T,b,O,B,D,R=0)=>{for(let I=R;I<h.length;I++){const J=h[I]=D?Ft(h[I]):ht(h[I]);v(null,J,g,_,T,b,O,B,D)}},P=(h,g,_,T,b,O,B)=>{const D=g.el=h.el;let{patchFlag:R,dynamicChildren:I,dirs:J}=g;R|=h.patchFlag&16;const j=h.props||se,q=g.props||se;let X;if(_&&qt(_,!1),(X=q.onVnodeBeforeUpdate)&&ft(X,_,g,h),J&&zt(g,h,_,"beforeUpdate"),_&&qt(_,!0),(j.innerHTML&&q.innerHTML==null||j.textContent&&q.textContent==null)&&c(D,""),I?U(h.dynamicChildren,I,D,_,T,hr(g,b),O):B||ne(h,g,D,null,_,T,hr(g,b),O,!1),R>0){if(R&16)Y(D,j,q,_,b);else if(R&2&&j.class!==q.class&&s(D,"class",null,q.class,b),R&4&&s(D,"style",j.style,q.style,b),R&8){const oe=g.dynamicProps;for(let fe=0;fe<oe.length;fe++){const ae=oe[fe],qe=j[ae],Pe=q[ae];(Pe!==qe||ae==="value")&&s(D,ae,qe,Pe,b,_)}}R&1&&h.children!==g.children&&c(D,g.children)}else!B&&I==null&&Y(D,j,q,_,b);((X=q.onVnodeUpdated)||J)&&$e(()=>{X&&ft(X,_,g,h),J&&zt(g,h,_,"updated")},T)},U=(h,g,_,T,b,O,B)=>{for(let D=0;D<g.length;D++){const R=h[D],I=g[D],J=R.el&&(R.type===Ee||!Yt(R,I)||R.shapeFlag&70)?u(R.el):_;v(R,I,J,null,T,b,O,B,!0)}},Y=(h,g,_,T,b)=>{if(g!==_){if(g!==se)for(const O in g)!Ln(O)&&!(O in _)&&s(h,O,g[O],null,b,T);for(const O in _){if(Ln(O))continue;const B=_[O],D=g[O];B!==D&&O!=="value"&&s(h,O,D,B,b,T)}"value"in _&&s(h,"value",g.value,_.value,b)}},$=(h,g,_,T,b,O,B,D,R)=>{const I=g.el=h?h.el:l(""),J=g.anchor=h?h.anchor:l("");let{patchFlag:j,dynamicChildren:q,slotScopeIds:X}=g;X&&(D=D?D.concat(X):X),h==null?(o(I,_,T),o(J,_,T),C(g.children||[],_,J,b,O,B,D,R)):j>0&&j&64&&q&&h.dynamicChildren?(U(h.dynamicChildren,q,_,b,O,B,D),(g.key!=null||b&&g===b.subTree)&&ps(h,g,!0)):ne(h,g,_,J,b,O,B,D,R)},Z=(h,g,_,T,b,O,B,D,R)=>{g.slotScopeIds=D,h==null?g.shapeFlag&512?b.ctx.activate(g,_,T,B,R):me(g,_,T,b,O,B,R):ye(h,g,R)},me=(h,g,_,T,b,O,B)=>{const D=h.component=xf(h,T,b);if(so(h)&&(D.ctx.renderer=z),Ef(D,!1,B),D.asyncDep){if(b&&b.registerDep(D,ee,B),!h.el){const R=D.subTree=L(Ne);w(null,R,g,_)}}else ee(D,h,g,_,b,O,B)},ye=(h,g,_)=>{const T=g.component=h.component;if(df(h,g,_))if(T.asyncDep&&!T.asyncResolved){W(T,g,_);return}else T.next=g,T.update();else g.el=h.el,T.vnode=g},ee=(h,g,_,T,b,O,B)=>{const D=()=>{if(h.isMounted){let{next:j,bu:q,u:X,parent:oe,vnode:fe}=h;{const Ke=pa(h);if(Ke){j&&(j.el=fe.el,W(h,j,B)),Ke.asyncDep.then(()=>{h.isUnmounted||D()});return}}let ae=j,qe;qt(h,!1),j?(j.el=fe.el,W(h,j,B)):j=fe,q&&_o(q),(qe=j.props&&j.props.onVnodeBeforeUpdate)&&ft(qe,oe,j,fe),qt(h,!0);const Pe=pr(h),et=h.subTree;h.subTree=Pe,v(et,Pe,u(et.el),x(et),h,b,O),j.el=Pe.el,ae===null&&hf(h,Pe.el),X&&$e(X,b),(qe=j.props&&j.props.onVnodeUpdated)&&$e(()=>ft(qe,oe,j,fe),b)}else{let j;const{el:q,props:X}=g,{bm:oe,m:fe,parent:ae,root:qe,type:Pe}=h,et=pn(g);if(qt(h,!1),oe&&_o(oe),!et&&(j=X&&X.onVnodeBeforeMount)&&ft(j,ae,g),qt(h,!0),q&&ve){const Ke=()=>{h.subTree=pr(h),ve(q,h.subTree,h,b,null)};et&&Pe.__asyncHydrate?Pe.__asyncHydrate(q,h,Ke):Ke()}else{qe.ce&&qe.ce._injectChildStyle(Pe);const Ke=h.subTree=pr(h);v(null,Ke,_,T,h,b,O),g.el=Ke.el}if(fe&&$e(fe,b),!et&&(j=X&&X.onVnodeMounted)){const Ke=g;$e(()=>ft(j,ae,Ke),b)}(g.shapeFlag&256||ae&&pn(ae.vnode)&&ae.vnode.shapeFlag&256)&&h.a&&$e(h.a,b),h.isMounted=!0,g=_=T=null}};h.scope.on();const R=h.effect=new gl(D);h.scope.off();const I=h.update=R.run.bind(R),J=h.job=R.runIfDirty.bind(R);J.i=h,J.id=h.uid,R.scheduler=()=>as(J),qt(h,!0),I()},W=(h,g,_)=>{g.component=h;const T=h.vnode.props;h.vnode=g,h.next=null,Yu(h,g.props,T,_),Zu(h,g.children,_),Ht(),$s(h),Ut()},ne=(h,g,_,T,b,O,B,D,R=!1)=>{const I=h&&h.children,J=h?h.shapeFlag:0,j=g.children,{patchFlag:q,shapeFlag:X}=g;if(q>0){if(q&128){Te(I,j,_,T,b,O,B,D,R);return}else if(q&256){De(I,j,_,T,b,O,B,D,R);return}}X&8?(J&16&&Le(I,b,O),j!==I&&c(_,j)):J&16?X&16?Te(I,j,_,T,b,O,B,D,R):Le(I,b,O,!0):(J&8&&c(_,""),X&16&&C(j,_,T,b,O,B,D,R))},De=(h,g,_,T,b,O,B,D,R)=>{h=h||fn,g=g||fn;const I=h.length,J=g.length,j=Math.min(I,J);let q;for(q=0;q<j;q++){const X=g[q]=R?Ft(g[q]):ht(g[q]);v(h[q],X,_,null,b,O,B,D,R)}I>J?Le(h,b,O,!0,!1,j):C(g,_,T,b,O,B,D,R,j)},Te=(h,g,_,T,b,O,B,D,R)=>{let I=0;const J=g.length;let j=h.length-1,q=J-1;for(;I<=j&&I<=q;){const X=h[I],oe=g[I]=R?Ft(g[I]):ht(g[I]);if(Yt(X,oe))v(X,oe,_,null,b,O,B,D,R);else break;I++}for(;I<=j&&I<=q;){const X=h[j],oe=g[q]=R?Ft(g[q]):ht(g[q]);if(Yt(X,oe))v(X,oe,_,null,b,O,B,D,R);else break;j--,q--}if(I>j){if(I<=q){const X=q+1,oe=X<J?g[X].el:T;for(;I<=q;)v(null,g[I]=R?Ft(g[I]):ht(g[I]),_,oe,b,O,B,D,R),I++}}else if(I>q)for(;I<=j;)xe(h[I],b,O,!0),I++;else{const X=I,oe=I,fe=new Map;for(I=oe;I<=q;I++){const We=g[I]=R?Ft(g[I]):ht(g[I]);We.key!=null&&fe.set(We.key,I)}let ae,qe=0;const Pe=q-oe+1;let et=!1,Ke=0;const An=new Array(Pe);for(I=0;I<Pe;I++)An[I]=0;for(I=X;I<=j;I++){const We=h[I];if(qe>=Pe){xe(We,b,O,!0);continue}let ut;if(We.key!=null)ut=fe.get(We.key);else for(ae=oe;ae<=q;ae++)if(An[ae-oe]===0&&Yt(We,g[ae])){ut=ae;break}ut===void 0?xe(We,b,O,!0):(An[ut-oe]=I+1,ut>=Ke?Ke=ut:et=!0,v(We,g[ut],_,null,b,O,B,D,R),qe++)}const Is=et?of(An):fn;for(ae=Is.length-1,I=Pe-1;I>=0;I--){const We=oe+I,ut=g[We],Rs=We+1<J?g[We+1].el:T;An[I]===0?v(null,ut,_,Rs,b,O,B,D,R):et&&(ae<0||I!==Is[ae]?Ue(ut,_,Rs,2):ae--)}}},Ue=(h,g,_,T,b=null)=>{const{el:O,type:B,transition:D,children:R,shapeFlag:I}=h;if(I&6){Ue(h.component.subTree,g,_,T);return}if(I&128){h.suspense.move(g,_,T);return}if(I&64){B.move(h,g,_,z);return}if(B===Ee){o(O,g,_);for(let j=0;j<R.length;j++)Ue(R[j],g,_,T);o(h.anchor,g,_);return}if(B===wo){S(h,g,_);return}if(T!==2&&I&1&&D)if(T===0)D.beforeEnter(O),o(O,g,_),$e(()=>D.enter(O),b);else{const{leave:j,delayLeave:q,afterLeave:X}=D,oe=()=>o(O,g,_),fe=()=>{j(O,()=>{oe(),X&&X()})};q?q(O,oe,fe):fe()}else o(O,g,_)},xe=(h,g,_,T=!1,b=!1)=>{const{type:O,props:B,ref:D,children:R,dynamicChildren:I,shapeFlag:J,patchFlag:j,dirs:q,cacheIndex:X}=h;if(j===-2&&(b=!1),D!=null&&Po(D,null,_,h,!0),X!=null&&(g.renderCache[X]=void 0),J&256){g.ctx.deactivate(h);return}const oe=J&1&&q,fe=!pn(h);let ae;if(fe&&(ae=B&&B.onVnodeBeforeUnmount)&&ft(ae,g,h),J&6)ze(h.component,_,T);else{if(J&128){h.suspense.unmount(_,T);return}oe&&zt(h,null,g,"beforeUnmount"),J&64?h.type.remove(h,g,_,z,T):I&&!I.hasOnce&&(O!==Ee||j>0&&j&64)?Le(I,g,_,!1,!0):(O===Ee&&j&384||!b&&J&16)&&Le(R,g,_),T&&je(h)}(fe&&(ae=B&&B.onVnodeUnmounted)||oe)&&$e(()=>{ae&&ft(ae,g,h),oe&&zt(h,null,g,"unmounted")},_)},je=h=>{const{type:g,el:_,anchor:T,transition:b}=h;if(g===Ee){ge(_,T);return}if(g===wo){E(h);return}const O=()=>{r(_),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(h.shapeFlag&1&&b&&!b.persisted){const{leave:B,delayLeave:D}=b,R=()=>B(_,O);D?D(h.el,O,R):R()}else O()},ge=(h,g)=>{let _;for(;h!==g;)_=d(h),r(h),h=_;r(g)},ze=(h,g,_)=>{const{bum:T,scope:b,job:O,subTree:B,um:D,m:R,a:I}=h;Ws(R),Ws(I),T&&_o(T),b.stop(),O&&(O.flags|=8,xe(B,h,g,_)),D&&$e(D,g),$e(()=>{h.isUnmounted=!0},g),g&&g.pendingBranch&&!g.isUnmounted&&h.asyncDep&&!h.asyncResolved&&h.suspenseId===g.pendingId&&(g.deps--,g.deps===0&&g.resolve())},Le=(h,g,_,T=!1,b=!1,O=0)=>{for(let B=O;B<h.length;B++)xe(h[B],g,_,T,b)},x=h=>{if(h.shapeFlag&6)return x(h.component.subTree);if(h.shapeFlag&128)return h.suspense.next();const g=d(h.anchor||h.el),_=g&&g[jl];return _?d(_):g};let H=!1;const N=(h,g,_)=>{h==null?g._vnode&&xe(g._vnode,null,null,!0):v(g._vnode||null,h,g,null,null,null,_),g._vnode=h,H||(H=!0,$s(),Fl(),H=!1)},z={p:v,um:xe,m:Ue,r:je,mt:me,mc:C,pc:ne,pbc:U,n:x,o:e};let le,ve;return{render:N,hydrate:le,createApp:Ku(N,le)}}function hr({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function qt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function nf(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ps(e,t,n=!1){const o=e.children,r=t.children;if(K(o)&&K(r))for(let s=0;s<o.length;s++){const i=o[s];let l=r[s];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[s]=Ft(r[s]),l.el=i.el),!n&&l.patchFlag!==-2&&ps(i,l)),l.type===Xo&&(l.el=i.el)}}function of(e){const t=e.slice(),n=[0];let o,r,s,i,l;const a=e.length;for(o=0;o<a;o++){const f=e[o];if(f!==0){if(r=n[n.length-1],e[r]<f){t[o]=r,n.push(o);continue}for(s=0,i=n.length-1;s<i;)l=s+i>>1,e[n[l]]<f?s=l+1:i=l;f<e[n[s]]&&(s>0&&(t[o]=n[s-1]),n[s]=o)}}for(s=n.length,i=n[s-1];s-- >0;)n[s]=i,i=t[i];return n}function pa(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:pa(t)}function Ws(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const rf=Symbol.for("v-scx"),sf=()=>ke(rf);function nm(e,t){return Jo(e,null,t)}function lf(e,t){return Jo(e,null,{flush:"sync"})}function be(e,t,n){return Jo(e,t,n)}function Jo(e,t,n=se){const{immediate:o,deep:r,flush:s,once:i}=n,l=we({},n),a=t&&o||!t&&s!=="post";let f;if(bn){if(s==="sync"){const m=sf();f=m.__watcherHandles||(m.__watcherHandles=[])}else if(!a){const m=()=>{};return m.stop=mt,m.resume=mt,m.pause=mt,m}}const c=Se;l.call=(m,p,v)=>it(m,c,p,v);let u=!1;s==="post"?l.scheduler=m=>{$e(m,c&&c.suspense)}:s!=="sync"&&(u=!0,l.scheduler=(m,p)=>{p?m():as(m)}),l.augmentJob=m=>{t&&(m.flags|=4),u&&(m.flags|=2,c&&(m.id=c.uid,m.i=c))};const d=xu(e,t,l);return bn&&(f?f.push(d):a&&d()),d}function af(e,t,n){const o=this.proxy,r=pe(e)?e.includes(".")?ma(o,e):()=>o[e]:e.bind(o,o);let s;Q(t)?s=t:(s=t.handler,n=t);const i=lo(this),l=Jo(r,s.bind(o),n);return i(),l}function ma(e,t){const n=t.split(".");return()=>{let o=e;for(let r=0;r<n.length&&o;r++)o=o[n[r]];return o}}function om(e,t,n=se){const o=kt(),r=Je(t),s=_t(t),i=ga(e,r),l=Dl((a,f)=>{let c,u=se,d;return lf(()=>{const m=e[r];He(c,m)&&(c=m,f())}),{get(){return a(),n.get?n.get(c):c},set(m){const p=n.set?n.set(m):m;if(!He(p,c)&&!(u!==se&&He(m,u)))return;const v=o.vnode.props;v&&(t in v||r in v||s in v)&&(`onUpdate:${t}`in v||`onUpdate:${r}`in v||`onUpdate:${s}`in v)||(c=m,f()),o.emit(`update:${t}`,p),He(m,p)&&He(m,u)&&!He(p,d)&&f(),u=m,d=p}}});return l[Symbol.iterator]=()=>{let a=0;return{next(){return a<2?{value:a++?i||se:l,done:!1}:{done:!0}}}},l}const ga=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Je(t)}Modifiers`]||e[`${_t(t)}Modifiers`];function cf(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||se;let r=n;const s=t.startsWith("update:"),i=s&&ga(o,t.slice(7));i&&(i.trim&&(r=n.map(c=>pe(c)?c.trim():c)),i.number&&(r=n.map(Tr)));let l,a=o[l=sr(t)]||o[l=sr(Je(t))];!a&&s&&(a=o[l=sr(_t(t))]),a&&it(a,e,6,r);const f=o[l+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,it(f,e,6,r)}}function va(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(r!==void 0)return r;const s=e.emits;let i={},l=!1;if(!Q(e)){const a=f=>{const c=va(f,t,!0);c&&(l=!0,we(i,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!s&&!l?(ue(e)&&o.set(e,null),null):(K(s)?s.forEach(a=>i[a]=null):we(i,s),ue(e)&&o.set(e,i),i)}function Qo(e,t){return!e||!Bo(t)?!1:(t=t.slice(2).replace(/Once$/,""),ie(e,t[0].toLowerCase()+t.slice(1))||ie(e,_t(t))||ie(e,t))}function pr(e){const{type:t,vnode:n,proxy:o,withProxy:r,propsOptions:[s],slots:i,attrs:l,emit:a,render:f,renderCache:c,props:u,data:d,setupState:m,ctx:p,inheritAttrs:v}=e,y=To(e);let w,A;try{if(n.shapeFlag&4){const E=r||o,k=E;w=ht(f.call(k,E,c,u,m,d,p)),A=l}else{const E=t;w=ht(E.length>1?E(u,{attrs:l,slots:i,emit:a}):E(u,null)),A=t.props?l:uf(l)}}catch(E){Vn.length=0,ro(E,e,1),w=L(Ne)}let S=w;if(A&&v!==!1){const E=Object.keys(A),{shapeFlag:k}=S;E.length&&k&7&&(s&&E.some(Qr)&&(A=ff(A,s)),S=Vt(S,A,!1,!0))}return n.dirs&&(S=Vt(S,null,!1,!0),S.dirs=S.dirs?S.dirs.concat(n.dirs):n.dirs),n.transition&&Yn(S,n.transition),w=S,To(y),w}const uf=e=>{let t;for(const n in e)(n==="class"||n==="style"||Bo(n))&&((t||(t={}))[n]=e[n]);return t},ff=(e,t)=>{const n={};for(const o in e)(!Qr(o)||!(o.slice(9)in t))&&(n[o]=e[o]);return n};function df(e,t,n){const{props:o,children:r,component:s}=e,{props:i,children:l,patchFlag:a}=t,f=s.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return o?Gs(o,i,f):!!i;if(a&8){const c=t.dynamicProps;for(let u=0;u<c.length;u++){const d=c[u];if(i[d]!==o[d]&&!Qo(f,d))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:o===i?!1:o?i?Gs(o,i,f):!0:!!i;return!1}function Gs(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const s=o[r];if(t[s]!==e[s]&&!Qo(n,s))return!0}return!1}function hf({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o===e)(e=t.vnode).el=n,t=t.parent;else break}}const _a=e=>e.__isSuspense;function pf(e,t){t&&t.pendingBranch?K(e)?t.effects.push(...e):t.effects.push(e):Cu(e)}const Ee=Symbol.for("v-fgt"),Xo=Symbol.for("v-txt"),Ne=Symbol.for("v-cmt"),wo=Symbol.for("v-stc"),Vn=[];let Ge=null;function Ye(e=!1){Vn.push(Ge=e?null:[])}function mf(){Vn.pop(),Ge=Vn[Vn.length-1]||null}let Jn=1;function Ys(e,t=!1){Jn+=e,e<0&&Ge&&t&&(Ge.hasOnce=!0)}function ya(e){return e.dynamicChildren=Jn>0?Ge||fn:null,mf(),Jn>0&&Ge&&Ge.push(e),e}function It(e,t,n,o,r,s){return ya(V(e,t,n,o,r,s,!0))}function Ro(e,t,n,o,r){return ya(L(e,t,n,o,r,!0))}function yn(e){return e?e.__v_isVNode===!0:!1}function Yt(e,t){return e.type===t.type&&e.key===t.key}const ba=({key:e})=>e??null,xo=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?pe(e)||he(e)||Q(e)?{i:Ce,r:e,k:t,f:!!n}:e:null);function V(e,t=null,n=null,o=0,r=null,s=e===Ee?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ba(t),ref:t&&xo(t),scopeId:Vl,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Ce};return l?(ms(a,n),s&128&&e.normalize(a)):n&&(a.shapeFlag|=pe(n)?8:16),Jn>0&&!i&&Ge&&(a.patchFlag>0||s&6)&&a.patchFlag!==32&&Ge.push(a),a}const L=gf;function gf(e,t=null,n=null,o=0,r=null,s=!1){if((!e||e===Zl)&&(e=Ne),yn(e)){const l=Vt(e,t,!0);return n&&ms(l,n),Jn>0&&!s&&Ge&&(l.shapeFlag&6?Ge[Ge.indexOf(e)]=l:Ge.push(l)),l.patchFlag=-2,l}if(Pf(e)&&(e=e.__vccOpts),t){t=vf(t);let{class:l,style:a}=t;l&&!pe(l)&&(t.class=Pt(l)),ue(a)&&(is(a)&&!K(a)&&(a=we({},a)),t.style=Ho(a))}const i=pe(e)?1:_a(e)?128:Hl(e)?64:ue(e)?4:Q(e)?2:0;return V(e,t,n,o,r,i,s,!0)}function vf(e){return e?is(e)||la(e)?we({},e):e:null}function Vt(e,t,n=!1,o=!1){const{props:r,ref:s,patchFlag:i,children:l,transition:a}=e,f=t?en(r||{},t):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&ba(f),ref:t&&t.ref?n&&s?K(s)?s.concat(xo(t)):[s,xo(t)]:xo(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ee?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Vt(e.ssContent),ssFallback:e.ssFallback&&Vt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&o&&Yn(c,a.clone(c)),c}function Qn(e=" ",t=0){return L(Xo,null,e,t)}function _f(e,t){const n=L(wo,null,e);return n.staticCount=t,n}function yf(e="",t=!1){return t?(Ye(),Ro(Ne,null,e)):L(Ne,null,e)}function ht(e){return e==null||typeof e=="boolean"?L(Ne):K(e)?L(Ee,null,e.slice()):yn(e)?Ft(e):L(Xo,null,String(e))}function Ft(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Vt(e)}function ms(e,t){let n=0;const{shapeFlag:o}=e;if(t==null)t=null;else if(K(t))n=16;else if(typeof t=="object")if(o&65){const r=t.default;r&&(r._c&&(r._d=!1),ms(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!la(t)?t._ctx=Ce:r===3&&Ce&&(Ce.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Q(t)?(t={default:t,_ctx:Ce},n=32):(t=String(t),o&64?(n=16,t=[Qn(t)]):n=8);e.children=t,e.shapeFlag|=n}function en(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const r in o)if(r==="class")t.class!==o.class&&(t.class=Pt([t.class,o.class]));else if(r==="style")t.style=Ho([t.style,o.style]);else if(Bo(r)){const s=t[r],i=o[r];i&&s!==i&&!(K(s)&&s.includes(i))&&(t[r]=s?[].concat(s,i):i)}else r!==""&&(t[r]=o[r])}return t}function ft(e,t,n,o=null){it(e,t,7,[n,o])}const bf=ra();let wf=0;function xf(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||bf,s={uid:wf++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new hl(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ca(o,r),emitsOptions:va(o,r),emit:null,emitted:null,propsDefaults:se,inheritAttrs:o.inheritAttrs,ctx:se,data:se,props:se,attrs:se,slots:se,refs:se,setupState:se,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=cf.bind(null,s),e.ce&&e.ce(s),s}let Se=null;const kt=()=>Se||Ce;let ko,Fr;{const e=jo(),t=(n,o)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(o),s=>{r.length>1?r.forEach(i=>i(s)):r[0](s)}};ko=t("__VUE_INSTANCE_SETTERS__",n=>Se=n),Fr=t("__VUE_SSR_SETTERS__",n=>bn=n)}const lo=e=>{const t=Se;return ko(e),e.scope.on(),()=>{e.scope.off(),ko(t)}},Js=()=>{Se&&Se.scope.off(),ko(null)};function wa(e){return e.vnode.shapeFlag&4}let bn=!1;function Ef(e,t=!1,n=!1){t&&Fr(t);const{props:o,children:r}=e.vnode,s=wa(e);Gu(e,o,s,t),Xu(e,r,n);const i=s?Sf(e,t):void 0;return t&&Fr(!1),i}function Sf(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Nu);const{setup:o}=n;if(o){Ht();const r=e.setupContext=o.length>1?Af(e):null,s=lo(e),i=oo(o,e,0,[e.props,r]),l=sl(i);if(Ut(),s(),(l||e.sp)&&!pn(e)&&cs(e),l){if(i.then(Js,Js),t)return i.then(a=>{Qs(e,a,t)}).catch(a=>{ro(a,e,0)});e.asyncDep=i}else Qs(e,i,t)}else xa(e,t)}function Qs(e,t,n){Q(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ue(t)&&(e.setupState=kl(t)),xa(e,n)}let Xs;function xa(e,t,n){const o=e.type;if(!e.render){if(!t&&Xs&&!o.render){const r=o.template||ds(e).template;if(r){const{isCustomElement:s,compilerOptions:i}=e.appContext.config,{delimiters:l,compilerOptions:a}=o,f=we(we({isCustomElement:s,delimiters:l},i),a);o.render=Xs(r,f)}}e.render=o.render||mt}{const r=lo(e);Ht();try{Vu(e)}finally{Ut(),r()}}}const Cf={get(e,t){return Oe(e,"get",""),e[t]}};function Af(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Cf),slots:e.slots,emit:e.emit,expose:t}}function Zo(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(kl(ls(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Nn)return Nn[n](e)},has(t,n){return n in t||n in Nn}})):e.proxy}function Tf(e,t=!0){return Q(e)?e.displayName||e.name:e.name||t&&e.__name}function Pf(e){return Q(e)&&"__vccOpts"in e}const _e=(e,t)=>bu(e,t,bn);function gs(e,t,n){const o=arguments.length;return o===2?ue(t)&&!K(t)?yn(t)?L(e,null,[t]):L(e,t):L(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):o===3&&yn(n)&&(n=[n]),L(e,t,n))}const Of="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Nr;const Zs=typeof window<"u"&&window.trustedTypes;if(Zs)try{Nr=Zs.createPolicy("vue",{createHTML:e=>e})}catch{}const Ea=Nr?e=>Nr.createHTML(e):e=>e,If="http://www.w3.org/2000/svg",Rf="http://www.w3.org/1998/Math/MathML",Ct=typeof document<"u"?document:null,ei=Ct&&Ct.createElement("template"),kf={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r=t==="svg"?Ct.createElementNS(If,e):t==="mathml"?Ct.createElementNS(Rf,e):n?Ct.createElement(e,{is:n}):Ct.createElement(e);return e==="select"&&o&&o.multiple!=null&&r.setAttribute("multiple",o.multiple),r},createText:e=>Ct.createTextNode(e),createComment:e=>Ct.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ct.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,s){const i=n?n.previousSibling:t.lastChild;if(r&&(r===s||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===s||!(r=r.nextSibling)););else{ei.innerHTML=Ea(o==="svg"?`<svg>${e}</svg>`:o==="mathml"?`<math>${e}</math>`:e);const l=ei.content;if(o==="svg"||o==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Dt="transition",Pn="animation",Xn=Symbol("_vtc"),Sa={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Df=we({},Kl,Sa),Lf=e=>(e.displayName="Transition",e.props=Df,e),Ca=Lf((e,{slots:t})=>gs(Ou,Mf(e),t)),Kt=(e,t=[])=>{K(e)?e.forEach(n=>n(...t)):e&&e(...t)},ti=e=>e?K(e)?e.some(t=>t.length>1):e.length>1:!1;function Mf(e){const t={};for(const $ in e)$ in Sa||(t[$]=e[$]);if(e.css===!1)return t;const{name:n="v",type:o,duration:r,enterFromClass:s=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:a=s,appearActiveClass:f=i,appearToClass:c=l,leaveFromClass:u=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:m=`${n}-leave-to`}=e,p=$f(r),v=p&&p[0],y=p&&p[1],{onBeforeEnter:w,onEnter:A,onEnterCancelled:S,onLeave:E,onLeaveCancelled:k,onBeforeAppear:M=w,onAppear:F=A,onAppearCancelled:C=S}=t,P=($,Z,me,ye)=>{$._enterCancelled=ye,Wt($,Z?c:l),Wt($,Z?f:i),me&&me()},U=($,Z)=>{$._isLeaving=!1,Wt($,u),Wt($,m),Wt($,d),Z&&Z()},Y=$=>(Z,me)=>{const ye=$?F:A,ee=()=>P(Z,$,me);Kt(ye,[Z,ee]),ni(()=>{Wt(Z,$?a:s),xt(Z,$?c:l),ti(ye)||oi(Z,o,v,ee)})};return we(t,{onBeforeEnter($){Kt(w,[$]),xt($,s),xt($,i)},onBeforeAppear($){Kt(M,[$]),xt($,a),xt($,f)},onEnter:Y(!1),onAppear:Y(!0),onLeave($,Z){$._isLeaving=!0;const me=()=>U($,Z);xt($,u),$._enterCancelled?(xt($,d),ii()):(ii(),xt($,d)),ni(()=>{$._isLeaving&&(Wt($,u),xt($,m),ti(E)||oi($,o,y,me))}),Kt(E,[$,me])},onEnterCancelled($){P($,!1,void 0,!0),Kt(S,[$])},onAppearCancelled($){P($,!0,void 0,!0),Kt(C,[$])},onLeaveCancelled($){U($),Kt(k,[$])}})}function $f(e){if(e==null)return null;if(ue(e))return[mr(e.enter),mr(e.leave)];{const t=mr(e);return[t,t]}}function mr(e){return Vc(e)}function xt(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Xn]||(e[Xn]=new Set)).add(t)}function Wt(e,t){t.split(/\s+/).forEach(o=>o&&e.classList.remove(o));const n=e[Xn];n&&(n.delete(t),n.size||(e[Xn]=void 0))}function ni(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Bf=0;function oi(e,t,n,o){const r=e._endId=++Bf,s=()=>{r===e._endId&&o()};if(n!=null)return setTimeout(s,n);const{type:i,timeout:l,propCount:a}=Ff(e,t);if(!i)return o();const f=i+"end";let c=0;const u=()=>{e.removeEventListener(f,d),s()},d=m=>{m.target===e&&++c>=a&&u()};setTimeout(()=>{c<a&&u()},l+1),e.addEventListener(f,d)}function Ff(e,t){const n=window.getComputedStyle(e),o=p=>(n[p]||"").split(", "),r=o(`${Dt}Delay`),s=o(`${Dt}Duration`),i=ri(r,s),l=o(`${Pn}Delay`),a=o(`${Pn}Duration`),f=ri(l,a);let c=null,u=0,d=0;t===Dt?i>0&&(c=Dt,u=i,d=s.length):t===Pn?f>0&&(c=Pn,u=f,d=a.length):(u=Math.max(i,f),c=u>0?i>f?Dt:Pn:null,d=c?c===Dt?s.length:a.length:0);const m=c===Dt&&/\b(transform|all)(,|$)/.test(o(`${Dt}Property`).toString());return{type:c,timeout:u,propCount:d,hasTransform:m}}function ri(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,o)=>si(n)+si(e[o])))}function si(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function ii(){return document.body.offsetHeight}function Nf(e,t,n){const o=e[Xn];o&&(t=(t?[t,...o]:[...o]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Do=Symbol("_vod"),Aa=Symbol("_vsh"),Ta={beforeMount(e,{value:t},{transition:n}){e[Do]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):On(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),On(e,!0),o.enter(e)):o.leave(e,()=>{On(e,!1)}):On(e,t))},beforeUnmount(e,{value:t}){On(e,t)}};function On(e,t){e.style.display=t?e[Do]:"none",e[Aa]=!t}const Vf=Symbol(""),jf=/(^|;)\s*display\s*:/;function Hf(e,t,n){const o=e.style,r=pe(n);let s=!1;if(n&&!r){if(t)if(pe(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&Eo(o,l,"")}else for(const i in t)n[i]==null&&Eo(o,i,"");for(const i in n)i==="display"&&(s=!0),Eo(o,i,n[i])}else if(r){if(t!==n){const i=o[Vf];i&&(n+=";"+i),o.cssText=n,s=jf.test(n)}}else t&&e.removeAttribute("style");Do in e&&(e[Do]=s?o.display:"",e[Aa]&&(o.display="none"))}const li=/\s*!important$/;function Eo(e,t,n){if(K(n))n.forEach(o=>Eo(e,t,o));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const o=Uf(e,t);li.test(n)?e.setProperty(_t(o),n.replace(li,""),"important"):e[o]=n}}const ai=["Webkit","Moz","ms"],gr={};function Uf(e,t){const n=gr[t];if(n)return n;let o=Je(t);if(o!=="filter"&&o in e)return gr[t]=o;o=Vo(o);for(let r=0;r<ai.length;r++){const s=ai[r]+o;if(s in e)return gr[t]=s}return t}const ci="http://www.w3.org/1999/xlink";function ui(e,t,n,o,r,s=Kc(t)){o&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(ci,t.slice(6,t.length)):e.setAttributeNS(ci,t,n):n==null||s&&!cl(n)?e.removeAttribute(t):e.setAttribute(t,s?"":st(n)?String(n):n)}function fi(e,t,n,o,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Ea(n):n);return}const s=e.tagName;if(t==="value"&&s!=="PROGRESS"&&!s.includes("-")){const l=s==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=cl(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function Jt(e,t,n,o){e.addEventListener(t,n,o)}function zf(e,t,n,o){e.removeEventListener(t,n,o)}const di=Symbol("_vei");function qf(e,t,n,o,r=null){const s=e[di]||(e[di]={}),i=s[t];if(o&&i)i.value=o;else{const[l,a]=Kf(t);if(o){const f=s[t]=Yf(o,r);Jt(e,l,f,a)}else i&&(zf(e,l,i,a),s[t]=void 0)}}const hi=/(?:Once|Passive|Capture)$/;function Kf(e){let t;if(hi.test(e)){t={};let o;for(;o=e.match(hi);)e=e.slice(0,e.length-o[0].length),t[o[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):_t(e.slice(2)),t]}let vr=0;const Wf=Promise.resolve(),Gf=()=>vr||(Wf.then(()=>vr=0),vr=Date.now());function Yf(e,t){const n=o=>{if(!o._vts)o._vts=Date.now();else if(o._vts<=n.attached)return;it(Jf(o,n.value),t,5,[o])};return n.value=e,n.attached=Gf(),n}function Jf(e,t){if(K(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(o=>r=>!r._stopped&&o&&o(r))}else return t}const pi=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Qf=(e,t,n,o,r,s)=>{const i=r==="svg";t==="class"?Nf(e,o,i):t==="style"?Hf(e,n,o):Bo(t)?Qr(t)||qf(e,t,n,o,s):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Xf(e,t,o,i))?(fi(e,t,o),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ui(e,t,o,i,s,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!pe(o))?fi(e,Je(t),o,s,t):(t==="true-value"?e._trueValue=o:t==="false-value"&&(e._falseValue=o),ui(e,t,o,i))};function Xf(e,t,n,o){if(o)return!!(t==="innerHTML"||t==="textContent"||t in e&&pi(t)&&Q(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return pi(t)&&pe(n)?!1:t in e}const Lo=e=>{const t=e.props["onUpdate:modelValue"]||!1;return K(t)?n=>_o(t,n):t};function Zf(e){e.target.composing=!0}function mi(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const gn=Symbol("_assign"),gi={created(e,{modifiers:{lazy:t,trim:n,number:o}},r){e[gn]=Lo(r);const s=o||r.props&&r.props.type==="number";Jt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),s&&(l=Tr(l)),e[gn](l)}),n&&Jt(e,"change",()=>{e.value=e.value.trim()}),t||(Jt(e,"compositionstart",Zf),Jt(e,"compositionend",mi),Jt(e,"change",mi))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:o,trim:r,number:s}},i){if(e[gn]=Lo(i),e.composing)return;const l=(s||e.type==="number")&&!/^0\d/.test(e.value)?Tr(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(o&&t===n||r&&e.value.trim()===a)||(e.value=a))}},ed={deep:!0,created(e,t,n){e[gn]=Lo(n),Jt(e,"change",()=>{const o=e._modelValue,r=td(e),s=e.checked,i=e[gn];if(K(o)){const l=ul(o,r),a=l!==-1;if(s&&!a)i(o.concat(r));else if(!s&&a){const f=[...o];f.splice(l,1),i(f)}}else if(Fo(o)){const l=new Set(o);s?l.add(r):l.delete(r),i(l)}else i(Pa(e,s))})},mounted:vi,beforeUpdate(e,t,n){e[gn]=Lo(n),vi(e,t,n)}};function vi(e,{value:t,oldValue:n},o){e._modelValue=t;let r;if(K(t))r=ul(t,o.props.value)>-1;else if(Fo(t))r=t.has(o.props.value);else{if(t===n)return;r=Uo(t,Pa(e,!0))}e.checked!==r&&(e.checked=r)}function td(e){return"_value"in e?e._value:e.value}function Pa(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const nd=["ctrl","shift","alt","meta"],od={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>nd.some(n=>e[`${n}Key`]&&!t.includes(n))},rm=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(r,...s)=>{for(let i=0;i<t.length;i++){const l=od[t[i]];if(l&&l(r,t))return}return e(r,...s)})},rd={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},sm=(e,t)=>{const n=e._withKeys||(e._withKeys={}),o=t.join(".");return n[o]||(n[o]=r=>{if(!("key"in r))return;const s=_t(r.key);if(t.some(i=>i===s||rd[i]===s))return e(r)})},sd=we({patchProp:Qf},kf);let _i;function id(){return _i||(_i=ef(sd))}const Oa=(...e)=>{const t=id().createApp(...e),{mount:n}=t;return t.mount=o=>{const r=ad(o);if(!r)return;const s=t._component;!Q(s)&&!s.render&&!s.template&&(s.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,ld(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function ld(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function ad(e){return pe(e)?document.querySelector(e):e}var cd=!1;/*!
 * pinia v2.2.6
 * (c) 2024 Eduardo San Martin Morote
 * @license MIT
 */let Ia;const er=e=>Ia=e,Ra=Symbol();function Vr(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var jn;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(jn||(jn={}));function ud(){const e=pl(!0),t=e.run(()=>G({}));let n=[],o=[];const r=ls({install(s){er(r),r._a=s,s.provide(Ra,r),s.config.globalProperties.$pinia=r,o.forEach(i=>n.push(i)),o=[]},use(s){return!this._a&&!cd?o.push(s):n.push(s),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const ka=()=>{};function yi(e,t,n,o=ka){e.push(t);const r=()=>{const s=e.indexOf(t);s>-1&&(e.splice(s,1),o())};return!n&&es()&&ml(r),r}function on(e,...t){e.slice().forEach(n=>{n(...t)})}const fd=e=>e(),bi=Symbol(),_r=Symbol();function jr(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,o)=>e.set(o,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const o=t[n],r=e[n];Vr(r)&&Vr(o)&&e.hasOwnProperty(n)&&!he(o)&&!Ot(o)?e[n]=jr(r,o):e[n]=o}return e}const dd=Symbol();function hd(e){return!Vr(e)||!e.hasOwnProperty(dd)}const{assign:Mt}=Object;function pd(e){return!!(he(e)&&e.effect)}function md(e,t,n,o){const{state:r,actions:s,getters:i}=t,l=n.state.value[e];let a;function f(){l||(n.state.value[e]=r?r():{});const c=gu(n.state.value[e]);return Mt(c,s,Object.keys(i||{}).reduce((u,d)=>(u[d]=ls(_e(()=>{er(n);const m=n._s.get(e);return i[d].call(m,m)})),u),{}))}return a=Da(e,f,t,n,o,!0),a}function Da(e,t,n={},o,r,s){let i;const l=Mt({actions:{}},n),a={deep:!0};let f,c,u=[],d=[],m;const p=o.state.value[e];!s&&!p&&(o.state.value[e]={}),G({});let v;function y(C){let P;f=c=!1,typeof C=="function"?(C(o.state.value[e]),P={type:jn.patchFunction,storeId:e,events:m}):(jr(o.state.value[e],C),P={type:jn.patchObject,payload:C,storeId:e,events:m});const U=v=Symbol();vt().then(()=>{v===U&&(f=!0)}),c=!0,on(u,P,o.state.value[e])}const w=s?function(){const{state:P}=n,U=P?P():{};this.$patch(Y=>{Mt(Y,U)})}:ka;function A(){i.stop(),u=[],d=[],o._s.delete(e)}const S=(C,P="")=>{if(bi in C)return C[_r]=P,C;const U=function(){er(o);const Y=Array.from(arguments),$=[],Z=[];function me(W){$.push(W)}function ye(W){Z.push(W)}on(d,{args:Y,name:U[_r],store:k,after:me,onError:ye});let ee;try{ee=C.apply(this&&this.$id===e?this:k,Y)}catch(W){throw on(Z,W),W}return ee instanceof Promise?ee.then(W=>(on($,W),W)).catch(W=>(on(Z,W),Promise.reject(W))):(on($,ee),ee)};return U[bi]=!0,U[_r]=P,U},E={_p:o,$id:e,$onAction:yi.bind(null,d),$patch:y,$reset:w,$subscribe(C,P={}){const U=yi(u,C,P.detached,()=>Y()),Y=i.run(()=>be(()=>o.state.value[e],$=>{(P.flush==="sync"?c:f)&&C({storeId:e,type:jn.direct,events:m},$)},Mt({},a,P)));return U},$dispose:A},k=gt(E);o._s.set(e,k);const F=(o._a&&o._a.runWithContext||fd)(()=>o._e.run(()=>(i=pl()).run(()=>t({action:S}))));for(const C in F){const P=F[C];if(he(P)&&!pd(P)||Ot(P))s||(p&&hd(P)&&(he(P)?P.value=p[C]:jr(P,p[C])),o.state.value[e][C]=P);else if(typeof P=="function"){const U=S(P,C);F[C]=U,l.actions[C]=P}}return Mt(k,F),Mt(re(k),F),Object.defineProperty(k,"$state",{get:()=>o.state.value[e],set:C=>{y(P=>{Mt(P,C)})}}),o._p.forEach(C=>{Mt(k,i.run(()=>C({store:k,app:o._a,pinia:o,options:l})))}),p&&s&&n.hydrate&&n.hydrate(k.$state,p),f=!0,c=!0,k}/*! #__NO_SIDE_EFFECTS__ */function vs(e,t,n){let o,r;const s=typeof t=="function";typeof e=="string"?(o=e,r=s?n:t):(r=e,o=e.id);function i(l,a){const f=Wu();return l=l||(f?ke(Ra,null):null),l&&er(l),l=Ia,l._s.has(o)||(s?Da(o,t,r,l):md(o,r,l)),l._s.get(o)}return i.$id=o,i}function Hr(e){{const t=re(e),n={};for(const o in t){const r=t[o];(he(r)||Ot(r))&&(n[o]=Ll(e,o))}return n}}function gd(){}const yt=Object.assign,_s=typeof window<"u",ao=e=>e!==null&&typeof e=="object",jt=e=>e!=null,Ur=e=>typeof e=="function",vd=e=>ao(e)&&Ur(e.then)&&Ur(e.catch),im=e=>Object.prototype.toString.call(e)==="[object Date]"&&!Number.isNaN(e.getTime()),La=e=>typeof e=="number"||/^\d+(\.\d+)?$/.test(e),_d=()=>_s?/ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase()):!1;function wi(e,t){const n=t.split(".");let o=e;return n.forEach(r=>{var s;o=ao(o)&&(s=o[r])!=null?s:""}),o}function yd(e,t,n){return t.reduce((o,r)=>(o[r]=e[r],o),{})}const lm=(e,t)=>JSON.stringify(e)===JSON.stringify(t),am=e=>Array.isArray(e)?e:[e],Mo=null,Ae=[Number,String],ot={type:Boolean,default:!0},cm=e=>({type:e,required:!0}),um=()=>({type:Array,default:()=>[]}),bd=e=>({type:Number,default:e}),wd=e=>({type:Ae,default:e}),Ve=e=>({type:String,default:e});var En=typeof window<"u";function xi(e){return En?requestAnimationFrame(e):-1}function fm(e){En&&cancelAnimationFrame(e)}function dm(e){xi(()=>xi(e))}var xd=e=>e===window,Ei=(e,t)=>({top:0,left:0,right:e,bottom:t,width:e,height:t}),Ma=e=>{const t=Re(e);if(xd(t)){const n=t.innerWidth,o=t.innerHeight;return Ei(n,o)}return t!=null&&t.getBoundingClientRect?t.getBoundingClientRect():Ei(0,0)};function Ed(e){const t=ke(e,null);if(t){const n=kt(),{link:o,unlink:r,internalChildren:s}=t;o(n),io(()=>r(n));const i=_e(()=>s.indexOf(n));return{parent:t,index:i}}return{parent:null,index:G(-1)}}function Sd(e){const t=[],n=o=>{Array.isArray(o)&&o.forEach(r=>{var s;yn(r)&&(t.push(r),(s=r.component)!=null&&s.subTree&&(t.push(r.component.subTree),n(r.component.subTree.children)),r.children&&n(r.children))})};return n(e),t}var Si=(e,t)=>{const n=e.indexOf(t);return n===-1?e.findIndex(o=>t.key!==void 0&&t.key!==null&&o.type===t.type&&o.key===t.key):n};function Cd(e,t,n){const o=Sd(e.subTree.children);n.sort((s,i)=>Si(o,s.vnode)-Si(o,i.vnode));const r=n.map(s=>s.proxy);t.sort((s,i)=>{const l=r.indexOf(s),a=r.indexOf(i);return l-a})}function Ad(e){const t=gt([]),n=gt([]),o=kt();return{children:t,linkChildren:s=>{mn(e,Object.assign({link:a=>{a.proxy&&(n.push(a),t.push(a.proxy),Cd(o,t,n))},unlink:a=>{const f=n.indexOf(a);t.splice(f,1),n.splice(f,1)},children:t,internalChildren:n},s))}}}function $a(e){let t;at(()=>{e(),vt(()=>{t=!0})}),us(()=>{t&&e()})}function Ba(e,t,n={}){if(!En)return;const{target:o=window,passive:r=!1,capture:s=!1}=n;let i=!1,l;const a=u=>{if(i)return;const d=Re(u);d&&!l&&(d.addEventListener(e,t,{capture:s,passive:r}),l=!0)},f=u=>{if(i)return;const d=Re(u);d&&l&&(d.removeEventListener(e,t,s),l=!1)};io(()=>f(o)),Wo(()=>f(o)),$a(()=>a(o));let c;return he(o)&&(c=be(o,(u,d)=>{f(d),a(u)})),()=>{c==null||c(),f(o),i=!0}}var go,yr;function Td(){if(!go&&(go=G(0),yr=G(0),En)){const e=()=>{go.value=window.innerWidth,yr.value=window.innerHeight};e(),window.addEventListener("resize",e,{passive:!0}),window.addEventListener("orientationchange",e,{passive:!0})}return{width:go,height:yr}}var Pd=/scroll|auto|overlay/i,Fa=En?window:void 0;function Od(e){return e.tagName!=="HTML"&&e.tagName!=="BODY"&&e.nodeType===1}function Na(e,t=Fa){let n=e;for(;n&&n!==t&&Od(n);){const{overflowY:o}=window.getComputedStyle(n);if(Pd.test(o))return n;n=n.parentNode}return t}function hm(e,t=Fa){const n=G();return at(()=>{e.value&&(n.value=Na(e.value,t))}),n}var vo;function pm(){if(!vo&&(vo=G("visible"),En)){const e=()=>{vo.value=document.hidden?"hidden":"visible"};e(),window.addEventListener("visibilitychange",e)}return vo}var Id=Symbol("van-field");function mm(e){const t=ke(Id,null);t&&!t.customValue.value&&(t.customValue.value=e,be(e,()=>{t.resetValidation(),t.validateWithTrigger("onChange")}))}function Rd(e){const t="scrollTop"in e?e.scrollTop:e.pageYOffset;return Math.max(t,0)}function Ci(e,t){"scrollTop"in e?e.scrollTop=t:e.scrollTo(e.scrollX,t)}function Va(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0}function kd(e){Ci(window,e),Ci(document.body,e)}function gm(e,t){if(e===window)return 0;const n=t?Rd(t):Va();return Ma(e).top+n}const Dd=_d();function vm(){Dd&&kd(Va())}const Ld=e=>e.stopPropagation();function ys(e,t){(typeof e.cancelable!="boolean"||e.cancelable)&&e.preventDefault(),t&&Ld(e)}function _m(e){const t=Re(e);if(!t)return!1;const n=window.getComputedStyle(t),o=n.display==="none",r=t.offsetParent===null&&n.position!=="fixed";return o||r}const{width:ja,height:Ha}=Td();function nt(e){if(jt(e))return La(e)?`${e}px`:String(e)}function Md(e){if(jt(e)){if(Array.isArray(e))return{width:nt(e[0]),height:nt(e[1])};const t=nt(e);return{width:t,height:t}}}function bs(e){const t={};return e!==void 0&&(t.zIndex=+e),t}let br;function $d(){if(!br){const e=document.documentElement,t=e.style.fontSize||window.getComputedStyle(e).fontSize;br=parseFloat(t)}return br}function Bd(e){return e=e.replace(/rem/g,""),+e*$d()}function Fd(e){return e=e.replace(/vw/g,""),+e*ja.value/100}function Nd(e){return e=e.replace(/vh/g,""),+e*Ha.value/100}function ym(e){if(typeof e=="number")return e;if(_s){if(e.includes("rem"))return Bd(e);if(e.includes("vw"))return Fd(e);if(e.includes("vh"))return Nd(e)}return parseFloat(e)}const Vd=/-(\w)/g,Ua=e=>e.replace(Vd,(t,n)=>n.toUpperCase());function bm(e,t=2){let n=e+"";for(;n.length<t;)n="0"+n;return n}const wm=(e,t,n)=>Math.min(Math.max(e,t),n);function Ai(e,t,n){const o=e.indexOf(t);return o===-1?e:t==="-"&&o!==0?e.slice(0,o):e.slice(0,o+1)+e.slice(o).replace(n,"")}function xm(e,t=!0,n=!0){t?e=Ai(e,".",/\./g):e=e.split(".")[0],n?e=Ai(e,"-",/-/g):e=e.replace(/-/,"");const o=t?/[^-0-9.]/g:/[^-0-9]/g;return e.replace(o,"")}const{hasOwnProperty:jd}=Object.prototype;function Hd(e,t,n){const o=t[n];jt(o)&&(!jd.call(e,n)||!ao(o)?e[n]=o:e[n]=za(Object(e[n]),o))}function za(e,t){return Object.keys(t).forEach(n=>{Hd(e,t,n)}),e}var Ud={name:"姓名",tel:"电话",save:"保存",clear:"清空",cancel:"取消",confirm:"确认",delete:"删除",loading:"加载中...",noCoupon:"暂无优惠券",nameEmpty:"请填写姓名",addContact:"添加联系人",telInvalid:"请填写正确的电话",vanCalendar:{end:"结束",start:"开始",title:"日期选择",weekdays:["日","一","二","三","四","五","六"],monthTitle:(e,t)=>`${e}年${t}月`,rangePrompt:e=>`最多选择 ${e} 天`},vanCascader:{select:"请选择"},vanPagination:{prev:"上一页",next:"下一页"},vanPullRefresh:{pulling:"下拉即可刷新...",loosing:"释放即可刷新..."},vanSubmitBar:{label:"合计:"},vanCoupon:{unlimited:"无门槛",discount:e=>`${e}折`,condition:e=>`满${e}元可用`},vanCouponCell:{title:"优惠券",count:e=>`${e}张可用`},vanCouponList:{exchange:"兑换",close:"不使用",enable:"可用",disabled:"不可用",placeholder:"输入优惠码"},vanAddressEdit:{area:"地区",areaEmpty:"请选择地区",addressEmpty:"请填写详细地址",addressDetail:"详细地址",defaultAddress:"设为默认收货地址"},vanAddressList:{add:"新增地址"}};const Ti=G("zh-CN"),Pi=gt({"zh-CN":Ud}),zd={messages(){return Pi[Ti.value]},use(e,t){Ti.value=e,this.add({[e]:t})},add(e={}){za(Pi,e)}};var qd=zd;function Kd(e){const t=Ua(e)+".";return(n,...o)=>{const r=qd.messages(),s=wi(r,t+n)||wi(r,n);return Ur(s)?s(...o):s}}function zr(e,t){return t?typeof t=="string"?` ${e}--${t}`:Array.isArray(t)?t.reduce((n,o)=>n+zr(e,o),""):Object.keys(t).reduce((n,o)=>n+(t[o]?zr(e,o):""),""):""}function Wd(e){return(t,n)=>(t&&typeof t!="string"&&(n=t,t=""),t=t?`${e}__${t}`:e,`${t}${zr(t,n)}`)}function ct(e){const t=`van-${e}`;return[t,Wd(t),Kd(t)]}const Sn="van-hairline",Em=`${Sn}--top`,Sm=`${Sn}--left`,Gd=`${Sn}--bottom`,Yd=`${Sn}--surround`,Jd=`${Sn}--top-bottom`,Cm=`${Sn}-unset--top-bottom`,qr="van-haptics-feedback",Am=Symbol("van-form"),Oi=5;function qa(e,{args:t=[],done:n,canceled:o,error:r}){if(e){const s=e.apply(null,t);vd(s)?s.then(i=>{i?n():o&&o()}).catch(r||gd):s?n():o&&o()}else n()}function bt(e){return e.install=t=>{const{name:n}=e;n&&(t.component(n,e),t.component(Ua(`-${n}`),e))},e}const Ka=Symbol();function Qd(e){const t=ke(Ka,null);t&&be(t,n=>{n&&e()})}const Xd=(e,t)=>{const n=G(),o=()=>{n.value=Ma(e).height};return at(()=>{vt(o);for(let r=1;r<=3;r++)setTimeout(o,100*r)}),Qd(()=>vt(o)),be([ja,Ha],o),n};function Wa(e,t){const n=Xd(e);return o=>L("div",{class:t("placeholder"),style:{height:n.value?`${n.value}px`:void 0}},[o()])}function Ga(e){const t=kt();t&&yt(t.proxy,e)}const Ya={to:[String,Object],url:String,replace:Boolean};function Zd({to:e,url:t,replace:n,$router:o}){e&&o?o[n?"replace":"push"](e):t&&(n?location.replace(t):location.href=t)}function Ja(){const e=kt().proxy;return()=>Zd(e)}const[eh,Ii]=ct("badge"),th={dot:Boolean,max:Ae,tag:Ve("div"),color:String,offset:Array,content:Ae,showZero:ot,position:Ve("top-right")};var nh=Qe({name:eh,props:th,setup(e,{slots:t}){const n=()=>{if(t.content)return!0;const{content:l,showZero:a}=e;return jt(l)&&l!==""&&(a||l!==0&&l!=="0")},o=()=>{const{dot:l,max:a,content:f}=e;if(!l&&n())return t.content?t.content():jt(a)&&La(f)&&+f>+a?`${a}+`:f},r=l=>l.startsWith("-")?l.replace("-",""):`-${l}`,s=_e(()=>{const l={background:e.color};if(e.offset){const[a,f]=e.offset,{position:c}=e,[u,d]=c.split("-");t.default?(typeof f=="number"?l[u]=nt(u==="top"?f:-f):l[u]=u==="top"?nt(f):r(f),typeof a=="number"?l[d]=nt(d==="left"?a:-a):l[d]=d==="left"?nt(a):r(a)):(l.marginTop=nt(f),l.marginLeft=nt(a))}return l}),i=()=>{if(n()||e.dot)return L("div",{class:Ii([e.position,{dot:e.dot,fixed:!!t.default}]),style:s.value},[o()])};return()=>{if(t.default){const{tag:l}=e;return L(l,{class:Ii("wrapper")},{default:()=>[t.default(),i()]})}return i()}}});const Qa=bt(nh);let oh=2e3;const rh=()=>++oh,[sh,Tm]=ct("config-provider"),ih=Symbol(sh),[lh,Ri]=ct("icon"),ah=e=>e==null?void 0:e.includes("/"),ch={dot:Boolean,tag:Ve("i"),name:String,size:Ae,badge:Ae,color:String,badgeProps:Object,classPrefix:String};var uh=Qe({name:lh,props:ch,setup(e,{slots:t}){const n=ke(ih,null),o=_e(()=>e.classPrefix||(n==null?void 0:n.iconPrefix)||Ri());return()=>{const{tag:r,dot:s,name:i,size:l,badge:a,color:f}=e,c=ah(i);return L(Qa,en({dot:s,tag:r,class:[o.value,c?"":`${o.value}-${i}`],style:{color:f,fontSize:nt(l)},content:a},e.badgeProps),{default:()=>{var u;return[(u=t.default)==null?void 0:u.call(t),c&&L("img",{class:Ri("image"),src:i},null)]}})}}});const Cn=bt(uh),[fh,Hn]=ct("loading"),dh=Array(12).fill(null).map((e,t)=>L("i",{class:Hn("line",String(t+1))},null)),hh=L("svg",{class:Hn("circular"),viewBox:"25 25 50 50"},[L("circle",{cx:"50",cy:"50",r:"20",fill:"none"},null)]),ph={size:Ae,type:Ve("circular"),color:String,vertical:Boolean,textSize:Ae,textColor:String};var mh=Qe({name:fh,props:ph,setup(e,{slots:t}){const n=_e(()=>yt({color:e.color},Md(e.size))),o=()=>{const s=e.type==="spinner"?dh:hh;return L("span",{class:Hn("spinner",e.type),style:n.value},[t.icon?t.icon():s])},r=()=>{var s;if(t.default)return L("span",{class:Hn("text"),style:{fontSize:nt(e.textSize),color:(s=e.textColor)!=null?s:e.color}},[t.default()])};return()=>{const{type:s,vertical:i}=e;return L("div",{class:Hn([s,{vertical:i}]),"aria-live":"polite","aria-busy":!0},[o(),r()])}}});const Xa=bt(mh),[gh,rn]=ct("button"),vh=yt({},Ya,{tag:Ve("button"),text:String,icon:String,type:Ve("default"),size:Ve("normal"),color:String,block:Boolean,plain:Boolean,round:Boolean,square:Boolean,loading:Boolean,hairline:Boolean,disabled:Boolean,iconPrefix:String,nativeType:Ve("button"),loadingSize:Ae,loadingText:String,loadingType:String,iconPosition:Ve("left")});var _h=Qe({name:gh,props:vh,emits:["click"],setup(e,{emit:t,slots:n}){const o=Ja(),r=()=>n.loading?n.loading():L(Xa,{size:e.loadingSize,type:e.loadingType,class:rn("loading")},null),s=()=>{if(e.loading)return r();if(n.icon)return L("div",{class:rn("icon")},[n.icon()]);if(e.icon)return L(Cn,{name:e.icon,class:rn("icon"),classPrefix:e.iconPrefix},null)},i=()=>{let f;if(e.loading?f=e.loadingText:f=n.default?n.default():e.text,f)return L("span",{class:rn("text")},[f])},l=()=>{const{color:f,plain:c}=e;if(f){const u={color:c?f:"white"};return c||(u.background=f),f.includes("gradient")?u.border=0:u.borderColor=f,u}},a=f=>{e.loading?ys(f):e.disabled||(t("click",f),o())};return()=>{const{tag:f,type:c,size:u,block:d,round:m,plain:p,square:v,loading:y,disabled:w,hairline:A,nativeType:S,iconPosition:E}=e,k=[rn([c,u,{plain:p,block:d,round:m,square:v,loading:y,disabled:w,hairline:A}]),{[Yd]:A}];return L(f,{type:S,class:k,style:l(),disabled:w,onClick:a},{default:()=>[L("div",{class:rn("content")},[E==="left"&&s(),i(),E==="right"&&s()])]})}}});const yh=bt(_h),Za={show:Boolean,zIndex:Ae,overlay:ot,duration:Ae,teleport:[String,Object],lockScroll:ot,lazyRender:ot,beforeClose:Function,overlayStyle:Object,overlayClass:Mo,transitionAppear:Boolean,closeOnClickOverlay:ot},Pm=Object.keys(Za);function bh(e,t){return e>t?"horizontal":t>e?"vertical":""}function wh(){const e=G(0),t=G(0),n=G(0),o=G(0),r=G(0),s=G(0),i=G(""),l=G(!0),a=()=>i.value==="vertical",f=()=>i.value==="horizontal",c=()=>{n.value=0,o.value=0,r.value=0,s.value=0,i.value="",l.value=!0};return{move:m=>{const p=m.touches[0];n.value=(p.clientX<0?0:p.clientX)-e.value,o.value=p.clientY-t.value,r.value=Math.abs(n.value),s.value=Math.abs(o.value);const v=10;(!i.value||r.value<v&&s.value<v)&&(i.value=bh(r.value,s.value)),l.value&&(r.value>Oi||s.value>Oi)&&(l.value=!1)},start:m=>{c(),e.value=m.touches[0].clientX,t.value=m.touches[0].clientY},reset:c,startX:e,startY:t,deltaX:n,deltaY:o,offsetX:r,offsetY:s,direction:i,isVertical:a,isHorizontal:f,isTap:l}}let In=0;const ki="van-overflow-hidden";function xh(e,t){const n=wh(),o="01",r="10",s=c=>{n.move(c);const u=n.deltaY.value>0?r:o,d=Na(c.target,e.value),{scrollHeight:m,offsetHeight:p,scrollTop:v}=d;let y="11";v===0?y=p>=m?"00":"01":v+p>=m&&(y="10"),y!=="11"&&n.isVertical()&&!(parseInt(y,2)&parseInt(u,2))&&ys(c,!0)},i=()=>{document.addEventListener("touchstart",n.start),document.addEventListener("touchmove",s,{passive:!1}),In||document.body.classList.add(ki),In++},l=()=>{In&&(document.removeEventListener("touchstart",n.start),document.removeEventListener("touchmove",s),In--,In||document.body.classList.remove(ki))},a=()=>t()&&i(),f=()=>t()&&l();$a(a),Wo(f),fs(f),be(t,c=>{c?i():l()})}function ec(e){const t=G(!1);return be(e,n=>{n&&(t.value=n)},{immediate:!0}),n=>()=>t.value?n():null}const Di=()=>{var e;const{scopeId:t}=((e=kt())==null?void 0:e.vnode)||{};return t?{[t]:""}:null},[Eh,Sh]=ct("overlay"),Ch={show:Boolean,zIndex:Ae,duration:Ae,className:Mo,lockScroll:ot,lazyRender:ot,customStyle:Object,teleport:[String,Object]};var Ah=Qe({name:Eh,props:Ch,setup(e,{slots:t}){const n=G(),o=ec(()=>e.show||!e.lazyRender),r=i=>{e.lockScroll&&ys(i,!0)},s=o(()=>{var i;const l=yt(bs(e.zIndex),e.customStyle);return jt(e.duration)&&(l.animationDuration=`${e.duration}s`),Bn(L("div",{ref:n,style:l,class:[Sh(),e.className]},[(i=t.default)==null?void 0:i.call(t)]),[[Ta,e.show]])});return Ba("touchmove",r,{target:n}),()=>{const i=L(Ca,{name:"van-fade",appear:!0},{default:s});return e.teleport?L(zl,{to:e.teleport},{default:()=>[i]}):i}}});const Th=bt(Ah),Ph=yt({},Za,{round:Boolean,position:Ve("center"),closeIcon:Ve("cross"),closeable:Boolean,transition:String,iconPrefix:String,closeOnPopstate:Boolean,closeIconPosition:Ve("top-right"),safeAreaInsetTop:Boolean,safeAreaInsetBottom:Boolean}),[Oh,Li]=ct("popup");var Ih=Qe({name:Oh,inheritAttrs:!1,props:Ph,emits:["open","close","opened","closed","keydown","update:show","clickOverlay","clickCloseIcon"],setup(e,{emit:t,attrs:n,slots:o}){let r,s;const i=G(),l=G(),a=ec(()=>e.show||!e.lazyRender),f=_e(()=>{const M={zIndex:i.value};if(jt(e.duration)){const F=e.position==="center"?"animationDuration":"transitionDuration";M[F]=`${e.duration}s`}return M}),c=()=>{r||(r=!0,i.value=e.zIndex!==void 0?+e.zIndex:rh(),t("open"))},u=()=>{r&&qa(e.beforeClose,{done(){r=!1,t("close"),t("update:show",!1)}})},d=M=>{t("clickOverlay",M),e.closeOnClickOverlay&&u()},m=()=>{if(e.overlay)return L(Th,en({show:e.show,class:e.overlayClass,zIndex:i.value,duration:e.duration,customStyle:e.overlayStyle,role:e.closeOnClickOverlay?"button":void 0,tabindex:e.closeOnClickOverlay?0:void 0},Di(),{onClick:d}),{default:o["overlay-content"]})},p=M=>{t("clickCloseIcon",M),u()},v=()=>{if(e.closeable)return L(Cn,{role:"button",tabindex:0,name:e.closeIcon,class:[Li("close-icon",e.closeIconPosition),qr],classPrefix:e.iconPrefix,onClick:p},null)};let y;const w=()=>{y&&clearTimeout(y),y=setTimeout(()=>{t("opened")})},A=()=>t("closed"),S=M=>t("keydown",M),E=a(()=>{var M;const{round:F,position:C,safeAreaInsetTop:P,safeAreaInsetBottom:U}=e;return Bn(L("div",en({ref:l,style:f.value,role:"dialog",tabindex:0,class:[Li({round:F,[C]:C}),{"van-safe-area-top":P,"van-safe-area-bottom":U}],onKeydown:S},n,Di()),[(M=o.default)==null?void 0:M.call(o),v()]),[[Ta,e.show]])}),k=()=>{const{position:M,transition:F,transitionAppear:C}=e,P=M==="center"?"van-fade":`van-popup-slide-${M}`;return L(Ca,{name:F||P,appear:C,onAfterEnter:w,onAfterLeave:A},{default:E})};return be(()=>e.show,M=>{M&&!r&&(c(),n.tabindex===0&&vt(()=>{var F;(F=l.value)==null||F.focus()})),!M&&r&&(r=!1,t("close"))}),Ga({popupRef:l}),xh(l,()=>e.show&&e.lockScroll),Ba("popstate",()=>{e.closeOnPopstate&&(u(),s=!1)}),at(()=>{e.show&&c()}),us(()=>{s&&(t("update:show",!0),s=!1)}),Wo(()=>{e.show&&e.teleport&&(u(),s=!0)}),mn(Ka,()=>e.show),()=>e.teleport?L(zl,{to:e.teleport},{default:()=>[m(),k()]}):L(Ee,null,[m(),k()])}});const ws=bt(Ih);let Rn=0;function Rh(e){e?(Rn||document.body.classList.add("van-toast--unclickable"),Rn++):Rn&&(Rn--,Rn||document.body.classList.remove("van-toast--unclickable"))}const[kh,sn]=ct("toast"),Dh=["show","overlay","teleport","transition","overlayClass","overlayStyle","closeOnClickOverlay","zIndex"],Lh={icon:String,show:Boolean,type:Ve("text"),overlay:Boolean,message:Ae,iconSize:Ae,duration:bd(2e3),position:Ve("middle"),teleport:[String,Object],wordBreak:String,className:Mo,iconPrefix:String,transition:Ve("van-fade"),loadingType:String,forbidClick:Boolean,overlayClass:Mo,overlayStyle:Object,closeOnClick:Boolean,closeOnClickOverlay:Boolean,zIndex:Ae};var tc=Qe({name:kh,props:Lh,emits:["update:show"],setup(e,{emit:t,slots:n}){let o,r=!1;const s=()=>{const u=e.show&&e.forbidClick;r!==u&&(r=u,Rh(r))},i=u=>t("update:show",u),l=()=>{e.closeOnClick&&i(!1)},a=()=>clearTimeout(o),f=()=>{const{icon:u,type:d,iconSize:m,iconPrefix:p,loadingType:v}=e;if(u||d==="success"||d==="fail")return L(Cn,{name:u||d,size:m,class:sn("icon"),classPrefix:p},null);if(d==="loading")return L(Xa,{class:sn("loading"),size:m,type:v},null)},c=()=>{const{type:u,message:d}=e;if(n.message)return L("div",{class:sn("text")},[n.message()]);if(jt(d)&&d!=="")return u==="html"?L("div",{key:0,class:sn("text"),innerHTML:String(d)},null):L("div",{class:sn("text")},[d])};return be(()=>[e.show,e.forbidClick],s),be(()=>[e.show,e.type,e.message,e.duration],()=>{a(),e.show&&e.duration>0&&(o=setTimeout(()=>{i(!1)},e.duration))}),at(s),io(s),()=>L(ws,en({class:[sn([e.position,e.wordBreak==="normal"?"break-normal":e.wordBreak,{[e.type]:!e.icon}]),e.className],lockScroll:!1,onClick:l,onClosed:a,"onUpdate:show":i},yd(e,Dh)),{default:()=>[f(),c()]})}});function Mh(){const e=gt({show:!1}),t=r=>{e.show=r},n=r=>{yt(e,r,{transitionAppear:!0}),t(!0)},o=()=>t(!1);return Ga({open:n,close:o,toggle:t}),{open:n,close:o,state:e,toggle:t}}function $h(e){const t=Oa(e),n=document.createElement("div");return document.body.appendChild(n),{instance:t.mount(n),unmount(){t.unmount(),document.body.removeChild(n)}}}const Bh={icon:"",type:"text",message:"",className:"",overlay:!1,onClose:void 0,onOpened:void 0,duration:2e3,teleport:"body",iconSize:void 0,iconPrefix:void 0,position:"middle",transition:"van-fade",forbidClick:!1,loadingType:void 0,overlayClass:"",overlayStyle:void 0,closeOnClick:!1,closeOnClickOverlay:!1};let un=[],Fh=!1,Mi=yt({},Bh);const Nh=new Map;function nc(e){return ao(e)?e:{message:e}}function Vh(){const{instance:e,unmount:t}=$h({setup(){const n=G(""),{open:o,state:r,close:s,toggle:i}=Mh(),l=()=>{},a=()=>L(tc,en(r,{onClosed:l,"onUpdate:show":i}),null);return be(n,f=>{r.message=f}),kt().render=a,{open:o,close:s,message:n}}});return e}function jh(){if(!un.length||Fh){const e=Vh();un.push(e)}return un[un.length-1]}function tt(e={}){if(!_s)return{};const t=jh(),n=nc(e);return t.open(yt({},Mi,Nh.get(n.type||Mi.type),n)),t}const Hh=e=>t=>tt(yt({type:e},nc(t))),Uh=Hh("loading"),$i=e=>{un.length&&un[0].close()};bt(tc);const[zh,Et]=ct("nav-bar"),qh={title:String,fixed:Boolean,zIndex:Ae,border:ot,leftText:String,rightText:String,leftDisabled:Boolean,rightDisabled:Boolean,leftArrow:Boolean,placeholder:Boolean,safeAreaInsetTop:Boolean,clickable:ot};var Kh=Qe({name:zh,props:qh,emits:["clickLeft","clickRight"],setup(e,{emit:t,slots:n}){const o=G(),r=Wa(o,Et),s=c=>{e.leftDisabled||t("clickLeft",c)},i=c=>{e.rightDisabled||t("clickRight",c)},l=()=>n.left?n.left():[e.leftArrow&&L(Cn,{class:Et("arrow"),name:"arrow-left"},null),e.leftText&&L("span",{class:Et("text")},[e.leftText])],a=()=>n.right?n.right():L("span",{class:Et("text")},[e.rightText]),f=()=>{const{title:c,fixed:u,border:d,zIndex:m}=e,p=bs(m),v=e.leftArrow||e.leftText||n.left,y=e.rightText||n.right;return L("div",{ref:o,style:p,class:[Et({fixed:u}),{[Gd]:d,"van-safe-area-top":e.safeAreaInsetTop}]},[L("div",{class:Et("content")},[v&&L("div",{class:[Et("left",{disabled:e.leftDisabled}),e.clickable&&!e.leftDisabled?qr:""],onClick:s},[l()]),L("div",{class:[Et("title"),"van-ellipsis"]},[n.title?n.title():c]),y&&L("div",{class:[Et("right",{disabled:e.rightDisabled}),e.clickable&&!e.rightDisabled?qr:""],onClick:i},[a()])])])};return()=>e.fixed&&e.placeholder?r(f):f()}});const Wh=bt(Kh),[oc,Bi]=ct("tabbar"),Gh={route:Boolean,fixed:ot,border:ot,zIndex:Ae,placeholder:Boolean,activeColor:String,beforeChange:Function,inactiveColor:String,modelValue:wd(0),safeAreaInsetBottom:{type:Boolean,default:null}},rc=Symbol(oc);var Yh=Qe({name:oc,props:Gh,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const o=G(),{linkChildren:r}=Ad(rc),s=Wa(o,Bi),i=()=>{var f;return(f=e.safeAreaInsetBottom)!=null?f:e.fixed},l=()=>{var f;const{fixed:c,zIndex:u,border:d}=e;return L("div",{ref:o,role:"tablist",style:bs(u),class:[Bi({fixed:c}),{[Jd]:d,"van-safe-area-bottom":i()}]},[(f=n.default)==null?void 0:f.call(n)])};return r({props:e,setActive:(f,c)=>{qa(e.beforeChange,{args:[f],done(){t("update:modelValue",f),t("change",f),c()}})}}),()=>e.fixed&&e.placeholder?s(l):l()}});const Jh=bt(Yh),[Qh,wr]=ct("tabbar-item"),Xh=yt({},Ya,{dot:Boolean,icon:String,name:Ae,badge:Ae,badgeProps:Object,iconPrefix:String});var Zh=Qe({name:Qh,props:Xh,emits:["click"],setup(e,{emit:t,slots:n}){const o=Ja(),r=kt().proxy,{parent:s,index:i}=Ed(rc);if(!s)return;const l=_e(()=>{var c;const{route:u,modelValue:d}=s.props;if(u&&"$route"in r){const{$route:m}=r,{to:p}=e,v=ao(p)?p:{path:p};return!!m.matched.find(y=>{const w="path"in v&&v.path===y.path,A="name"in v&&v.name===y.name;return w||A})}return((c=e.name)!=null?c:i.value)===d}),a=c=>{var u;l.value||s.setActive((u=e.name)!=null?u:i.value,o),t("click",c)},f=()=>{if(n.icon)return n.icon({active:l.value});if(e.icon)return L(Cn,{name:e.icon,classPrefix:e.iconPrefix},null)};return()=>{var c;const{dot:u,badge:d}=e,{activeColor:m,inactiveColor:p}=s.props,v=l.value?m:p;return L("div",{role:"tab",class:wr({active:l.value}),style:{color:v},tabindex:0,"aria-selected":l.value,onClick:a},[L(Qa,en({dot:u,class:wr("icon"),content:d},e.badgeProps),{default:f}),L("div",{class:wr("text")},[(c=n.default)==null?void 0:c.call(n,{active:l.value})])])}}});const ep=bt(Zh),xs="/assets/logo-pNuosfk1.jpg",Om=Object.freeze(Object.defineProperty({__proto__:null,default:xs},Symbol.toStringTag,{value:"Module"})),Es=vs("dialog",()=>{const e=G(!1),t=G(!1);function n(){e.value=!0}function o(){e.value=!1}function r(){t.value=!0}function s(){t.value=!1}return{showBindPhone:e,openBindPhone:n,closeBindPhone:o,showRealNameAuth:t,openRealNameAuth:r,closeRealNameAuth:s}});/*!
  * vue-router v4.4.5
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const cn=typeof document<"u";function sc(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function tp(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&sc(e.default)}const ce=Object.assign;function xr(e,t){const n={};for(const o in t){const r=t[o];n[o]=lt(r)?r.map(e):e(r)}return n}const Un=()=>{},lt=Array.isArray,ic=/#/g,np=/&/g,op=/\//g,rp=/=/g,sp=/\?/g,lc=/\+/g,ip=/%5B/g,lp=/%5D/g,ac=/%5E/g,ap=/%60/g,cc=/%7B/g,cp=/%7C/g,uc=/%7D/g,up=/%20/g;function Ss(e){return encodeURI(""+e).replace(cp,"|").replace(ip,"[").replace(lp,"]")}function fp(e){return Ss(e).replace(cc,"{").replace(uc,"}").replace(ac,"^")}function Kr(e){return Ss(e).replace(lc,"%2B").replace(up,"+").replace(ic,"%23").replace(np,"%26").replace(ap,"`").replace(cc,"{").replace(uc,"}").replace(ac,"^")}function dp(e){return Kr(e).replace(rp,"%3D")}function hp(e){return Ss(e).replace(ic,"%23").replace(sp,"%3F")}function pp(e){return e==null?"":hp(e).replace(op,"%2F")}function Zn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const mp=/\/$/,gp=e=>e.replace(mp,"");function Er(e,t,n="/"){let o,r={},s="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(o=t.slice(0,a),s=t.slice(a+1,l>-1?l:t.length),r=e(s)),l>-1&&(o=o||t.slice(0,l),i=t.slice(l,t.length)),o=bp(o??t,n),{fullPath:o+(s&&"?")+s+i,path:o,query:r,hash:Zn(i)}}function vp(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Fi(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function _p(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&wn(t.matched[o],n.matched[r])&&fc(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function wn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function fc(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!yp(e[n],t[n]))return!1;return!0}function yp(e,t){return lt(e)?Ni(e,t):lt(t)?Ni(t,e):e===t}function Ni(e,t){return lt(t)?e.length===t.length&&e.every((n,o)=>n===t[o]):e.length===1&&e[0]===t}function bp(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];(r===".."||r===".")&&o.push("");let s=n.length-1,i,l;for(i=0;i<o.length;i++)if(l=o[i],l!==".")if(l==="..")s>1&&s--;else break;return n.slice(0,s).join("/")+"/"+o.slice(i).join("/")}const Lt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var eo;(function(e){e.pop="pop",e.push="push"})(eo||(eo={}));var zn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(zn||(zn={}));function wp(e){if(!e)if(cn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),gp(e)}const xp=/^[^#]+#/;function Ep(e,t){return e.replace(xp,"#")+t}function Sp(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}const tr=()=>({left:window.scrollX,top:window.scrollY});function Cp(e){let t;if("el"in e){const n=e.el,o=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Sp(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Vi(e,t){return(history.state?history.state.position-t:-1)+e}const Wr=new Map;function Ap(e,t){Wr.set(e,t)}function Tp(e){const t=Wr.get(e);return Wr.delete(e),t}let Pp=()=>location.protocol+"//"+location.host;function dc(e,t){const{pathname:n,search:o,hash:r}=t,s=e.indexOf("#");if(s>-1){let l=r.includes(e.slice(s))?e.slice(s).length:1,a=r.slice(l);return a[0]!=="/"&&(a="/"+a),Fi(a,"")}return Fi(n,e)+o+r}function Op(e,t,n,o){let r=[],s=[],i=null;const l=({state:d})=>{const m=dc(e,location),p=n.value,v=t.value;let y=0;if(d){if(n.value=m,t.value=d,i&&i===p){i=null;return}y=v?d.position-v.position:0}else o(m);r.forEach(w=>{w(n.value,p,{delta:y,type:eo.pop,direction:y?y>0?zn.forward:zn.back:zn.unknown})})};function a(){i=n.value}function f(d){r.push(d);const m=()=>{const p=r.indexOf(d);p>-1&&r.splice(p,1)};return s.push(m),m}function c(){const{history:d}=window;d.state&&d.replaceState(ce({},d.state,{scroll:tr()}),"")}function u(){for(const d of s)d();s=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:f,destroy:u}}function ji(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?tr():null}}function Ip(e){const{history:t,location:n}=window,o={value:dc(e,n)},r={value:t.state};r.value||s(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function s(a,f,c){const u=e.indexOf("#"),d=u>-1?(n.host&&document.querySelector("base")?e:e.slice(u))+a:Pp()+e+a;try{t[c?"replaceState":"pushState"](f,"",d),r.value=f}catch(m){console.error(m),n[c?"replace":"assign"](d)}}function i(a,f){const c=ce({},t.state,ji(r.value.back,a,r.value.forward,!0),f,{position:r.value.position});s(a,c,!0),o.value=a}function l(a,f){const c=ce({},r.value,t.state,{forward:a,scroll:tr()});s(c.current,c,!0);const u=ce({},ji(o.value,a,null),{position:c.position+1},f);s(a,u,!1),o.value=a}return{location:o,state:r,push:l,replace:i}}function Rp(e){e=wp(e);const t=Ip(e),n=Op(e,t.state,t.location,t.replace);function o(s,i=!0){i||n.pauseListeners(),history.go(s)}const r=ce({location:"",base:e,go:o,createHref:Ep.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function kp(e){return typeof e=="string"||e&&typeof e=="object"}function hc(e){return typeof e=="string"||typeof e=="symbol"}const pc=Symbol("");var Hi;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Hi||(Hi={}));function xn(e,t){return ce(new Error,{type:e,[pc]:!0},t)}function St(e,t){return e instanceof Error&&pc in e&&(t==null||!!(e.type&t))}const Ui="[^/]+?",Dp={sensitive:!1,strict:!1,start:!0,end:!0},Lp=/[.+*?^${}()[\]/\\]/g;function Mp(e,t){const n=ce({},Dp,t),o=[];let r=n.start?"^":"";const s=[];for(const f of e){const c=f.length?[]:[90];n.strict&&!f.length&&(r+="/");for(let u=0;u<f.length;u++){const d=f[u];let m=40+(n.sensitive?.25:0);if(d.type===0)u||(r+="/"),r+=d.value.replace(Lp,"\\$&"),m+=40;else if(d.type===1){const{value:p,repeatable:v,optional:y,regexp:w}=d;s.push({name:p,repeatable:v,optional:y});const A=w||Ui;if(A!==Ui){m+=10;try{new RegExp(`(${A})`)}catch(E){throw new Error(`Invalid custom RegExp for param "${p}" (${A}): `+E.message)}}let S=v?`((?:${A})(?:/(?:${A}))*)`:`(${A})`;u||(S=y&&f.length<2?`(?:/${S})`:"/"+S),y&&(S+="?"),r+=S,m+=20,y&&(m+=-8),v&&(m+=-20),A===".*"&&(m+=-50)}c.push(m)}o.push(c)}if(n.strict&&n.end){const f=o.length-1;o[f][o[f].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(f){const c=f.match(i),u={};if(!c)return null;for(let d=1;d<c.length;d++){const m=c[d]||"",p=s[d-1];u[p.name]=m&&p.repeatable?m.split("/"):m}return u}function a(f){let c="",u=!1;for(const d of e){(!u||!c.endsWith("/"))&&(c+="/"),u=!1;for(const m of d)if(m.type===0)c+=m.value;else if(m.type===1){const{value:p,repeatable:v,optional:y}=m,w=p in f?f[p]:"";if(lt(w)&&!v)throw new Error(`Provided param "${p}" is an array but it is not repeatable (* or + modifiers)`);const A=lt(w)?w.join("/"):w;if(!A)if(y)d.length<2&&(c.endsWith("/")?c=c.slice(0,-1):u=!0);else throw new Error(`Missing required param "${p}"`);c+=A}}return c||"/"}return{re:i,score:o,keys:s,parse:l,stringify:a}}function $p(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function mc(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const s=$p(o[n],r[n]);if(s)return s;n++}if(Math.abs(r.length-o.length)===1){if(zi(o))return 1;if(zi(r))return-1}return r.length-o.length}function zi(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Bp={type:0,value:""},Fp=/[a-zA-Z0-9_]/;function Np(e){if(!e)return[[]];if(e==="/")return[[Bp]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${f}": ${m}`)}let n=0,o=n;const r=[];let s;function i(){s&&r.push(s),s=[]}let l=0,a,f="",c="";function u(){f&&(n===0?s.push({type:0,value:f}):n===1||n===2||n===3?(s.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:f,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),f="")}function d(){f+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){o=n,n=4;continue}switch(n){case 0:a==="/"?(f&&u(),i()):a===":"?(u(),n=1):d();break;case 4:d(),n=o;break;case 1:a==="("?n=2:Fp.test(a)?d():(u(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:u(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${f}"`),u(),i(),r}function Vp(e,t,n){const o=Mp(Np(e.path),n),r=ce(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function jp(e,t){const n=[],o=new Map;t=Gi({strict:!1,end:!0,sensitive:!1},t);function r(u){return o.get(u)}function s(u,d,m){const p=!m,v=Ki(u);v.aliasOf=m&&m.record;const y=Gi(t,u),w=[v];if("alias"in u){const E=typeof u.alias=="string"?[u.alias]:u.alias;for(const k of E)w.push(Ki(ce({},v,{components:m?m.record.components:v.components,path:k,aliasOf:m?m.record:v})))}let A,S;for(const E of w){const{path:k}=E;if(d&&k[0]!=="/"){const M=d.record.path,F=M[M.length-1]==="/"?"":"/";E.path=d.record.path+(k&&F+k)}if(A=Vp(E,d,y),m?m.alias.push(A):(S=S||A,S!==A&&S.alias.push(A),p&&u.name&&!Wi(A)&&i(u.name)),gc(A)&&a(A),v.children){const M=v.children;for(let F=0;F<M.length;F++)s(M[F],A,m&&m.children[F])}m=m||A}return S?()=>{i(S)}:Un}function i(u){if(hc(u)){const d=o.get(u);d&&(o.delete(u),n.splice(n.indexOf(d),1),d.children.forEach(i),d.alias.forEach(i))}else{const d=n.indexOf(u);d>-1&&(n.splice(d,1),u.record.name&&o.delete(u.record.name),u.children.forEach(i),u.alias.forEach(i))}}function l(){return n}function a(u){const d=zp(u,n);n.splice(d,0,u),u.record.name&&!Wi(u)&&o.set(u.record.name,u)}function f(u,d){let m,p={},v,y;if("name"in u&&u.name){if(m=o.get(u.name),!m)throw xn(1,{location:u});y=m.record.name,p=ce(qi(d.params,m.keys.filter(S=>!S.optional).concat(m.parent?m.parent.keys.filter(S=>S.optional):[]).map(S=>S.name)),u.params&&qi(u.params,m.keys.map(S=>S.name))),v=m.stringify(p)}else if(u.path!=null)v=u.path,m=n.find(S=>S.re.test(v)),m&&(p=m.parse(v),y=m.record.name);else{if(m=d.name?o.get(d.name):n.find(S=>S.re.test(d.path)),!m)throw xn(1,{location:u,currentLocation:d});y=m.record.name,p=ce({},d.params,u.params),v=m.stringify(p)}const w=[];let A=m;for(;A;)w.unshift(A.record),A=A.parent;return{name:y,path:v,params:p,matched:w,meta:Up(w)}}e.forEach(u=>s(u));function c(){n.length=0,o.clear()}return{addRoute:s,resolve:f,removeRoute:i,clearRoutes:c,getRoutes:l,getRecordMatcher:r}}function qi(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function Ki(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Hp(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Hp(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]=typeof n=="object"?n[o]:n;return t}function Wi(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Up(e){return e.reduce((t,n)=>ce(t,n.meta),{})}function Gi(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function zp(e,t){let n=0,o=t.length;for(;n!==o;){const s=n+o>>1;mc(e,t[s])<0?o=s:n=s+1}const r=qp(e);return r&&(o=t.lastIndexOf(r,o-1)),o}function qp(e){let t=e;for(;t=t.parent;)if(gc(t)&&mc(e,t)===0)return t}function gc({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Kp(e){const t={};if(e===""||e==="?")return t;const o=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<o.length;++r){const s=o[r].replace(lc," "),i=s.indexOf("="),l=Zn(i<0?s:s.slice(0,i)),a=i<0?null:Zn(s.slice(i+1));if(l in t){let f=t[l];lt(f)||(f=t[l]=[f]),f.push(a)}else t[l]=a}return t}function Yi(e){let t="";for(let n in e){const o=e[n];if(n=dp(n),o==null){o!==void 0&&(t+=(t.length?"&":"")+n);continue}(lt(o)?o.map(s=>s&&Kr(s)):[o&&Kr(o)]).forEach(s=>{s!==void 0&&(t+=(t.length?"&":"")+n,s!=null&&(t+="="+s))})}return t}function Wp(e){const t={};for(const n in e){const o=e[n];o!==void 0&&(t[n]=lt(o)?o.map(r=>r==null?null:""+r):o==null?o:""+o)}return t}const Gp=Symbol(""),Ji=Symbol(""),nr=Symbol(""),Cs=Symbol(""),Gr=Symbol("");function kn(){let e=[];function t(o){return e.push(o),()=>{const r=e.indexOf(o);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Nt(e,t,n,o,r,s=i=>i()){const i=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise((l,a)=>{const f=d=>{d===!1?a(xn(4,{from:n,to:t})):d instanceof Error?a(d):kp(d)?a(xn(2,{from:t,to:d})):(i&&o.enterCallbacks[r]===i&&typeof d=="function"&&i.push(d),l())},c=s(()=>e.call(o&&o.instances[r],t,n,f));let u=Promise.resolve(c);e.length<3&&(u=u.then(f)),u.catch(d=>a(d))})}function Sr(e,t,n,o,r=s=>s()){const s=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(sc(a)){const c=(a.__vccOpts||a)[t];c&&s.push(Nt(c,n,o,i,l,r))}else{let f=a();s.push(()=>f.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const u=tp(c)?c.default:c;i.mods[l]=c,i.components[l]=u;const m=(u.__vccOpts||u)[t];return m&&Nt(m,n,o,i,l,r)()}))}}return s}function Qi(e){const t=ke(nr),n=ke(Cs),o=_e(()=>{const a=Re(e.to);return t.resolve(a)}),r=_e(()=>{const{matched:a}=o.value,{length:f}=a,c=a[f-1],u=n.matched;if(!c||!u.length)return-1;const d=u.findIndex(wn.bind(null,c));if(d>-1)return d;const m=Xi(a[f-2]);return f>1&&Xi(c)===m&&u[u.length-1].path!==m?u.findIndex(wn.bind(null,a[f-2])):d}),s=_e(()=>r.value>-1&&Xp(n.params,o.value.params)),i=_e(()=>r.value>-1&&r.value===n.matched.length-1&&fc(n.params,o.value.params));function l(a={}){return Qp(a)?t[Re(e.replace)?"replace":"push"](Re(e.to)).catch(Un):Promise.resolve()}return{route:o,href:_e(()=>o.value.href),isActive:s,isExactActive:i,navigate:l}}const Yp=Qe({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Qi,setup(e,{slots:t}){const n=gt(Qi(e)),{options:o}=ke(nr),r=_e(()=>({[Zi(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[Zi(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const s=t.default&&t.default(n);return e.custom?s:gs("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},s)}}}),Jp=Yp;function Qp(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Xp(e,t){for(const n in t){const o=t[n],r=e[n];if(typeof o=="string"){if(o!==r)return!1}else if(!lt(r)||r.length!==o.length||o.some((s,i)=>s!==r[i]))return!1}return!0}function Xi(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Zi=(e,t,n)=>e??t??n,Zp=Qe({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=ke(Gr),r=_e(()=>e.route||o.value),s=ke(Ji,0),i=_e(()=>{let f=Re(s);const{matched:c}=r.value;let u;for(;(u=c[f])&&!u.components;)f++;return f}),l=_e(()=>r.value.matched[i.value]);mn(Ji,_e(()=>i.value+1)),mn(Gp,l),mn(Gr,r);const a=G();return be(()=>[a.value,l.value,e.name],([f,c,u],[d,m,p])=>{c&&(c.instances[u]=f,m&&m!==c&&f&&f===d&&(c.leaveGuards.size||(c.leaveGuards=m.leaveGuards),c.updateGuards.size||(c.updateGuards=m.updateGuards))),f&&c&&(!m||!wn(c,m)||!d)&&(c.enterCallbacks[u]||[]).forEach(v=>v(f))},{flush:"post"}),()=>{const f=r.value,c=e.name,u=l.value,d=u&&u.components[c];if(!d)return el(n.default,{Component:d,route:f});const m=u.props[c],p=m?m===!0?f.params:typeof m=="function"?m(f):m:null,y=gs(d,ce({},p,t,{onVnodeUnmounted:w=>{w.component.isUnmounted&&(u.instances[c]=null)},ref:a}));return el(n.default,{Component:y,route:f})||y}}});function el(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const vc=Zp;function e0(e){const t=jp(e.routes,e),n=e.parseQuery||Kp,o=e.stringifyQuery||Yi,r=e.history,s=kn(),i=kn(),l=kn(),a=yo(Lt);let f=Lt;cn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=xr.bind(null,x=>""+x),u=xr.bind(null,pp),d=xr.bind(null,Zn);function m(x,H){let N,z;return hc(x)?(N=t.getRecordMatcher(x),z=H):z=x,t.addRoute(z,N)}function p(x){const H=t.getRecordMatcher(x);H&&t.removeRoute(H)}function v(){return t.getRoutes().map(x=>x.record)}function y(x){return!!t.getRecordMatcher(x)}function w(x,H){if(H=ce({},H||a.value),typeof x=="string"){const g=Er(n,x,H.path),_=t.resolve({path:g.path},H),T=r.createHref(g.fullPath);return ce(g,_,{params:d(_.params),hash:Zn(g.hash),redirectedFrom:void 0,href:T})}let N;if(x.path!=null)N=ce({},x,{path:Er(n,x.path,H.path).path});else{const g=ce({},x.params);for(const _ in g)g[_]==null&&delete g[_];N=ce({},x,{params:u(g)}),H.params=u(H.params)}const z=t.resolve(N,H),le=x.hash||"";z.params=c(d(z.params));const ve=vp(o,ce({},x,{hash:fp(le),path:z.path})),h=r.createHref(ve);return ce({fullPath:ve,hash:le,query:o===Yi?Wp(x.query):x.query||{}},z,{redirectedFrom:void 0,href:h})}function A(x){return typeof x=="string"?Er(n,x,a.value.path):ce({},x)}function S(x,H){if(f!==x)return xn(8,{from:H,to:x})}function E(x){return F(x)}function k(x){return E(ce(A(x),{replace:!0}))}function M(x){const H=x.matched[x.matched.length-1];if(H&&H.redirect){const{redirect:N}=H;let z=typeof N=="function"?N(x):N;return typeof z=="string"&&(z=z.includes("?")||z.includes("#")?z=A(z):{path:z},z.params={}),ce({query:x.query,hash:x.hash,params:z.path!=null?{}:x.params},z)}}function F(x,H){const N=f=w(x),z=a.value,le=x.state,ve=x.force,h=x.replace===!0,g=M(N);if(g)return F(ce(A(g),{state:typeof g=="object"?ce({},le,g.state):le,force:ve,replace:h}),H||N);const _=N;_.redirectedFrom=H;let T;return!ve&&_p(o,z,N)&&(T=xn(16,{to:_,from:z}),Ue(z,z,!0,!1)),(T?Promise.resolve(T):U(_,z)).catch(b=>St(b)?St(b,2)?b:Te(b):ne(b,_,z)).then(b=>{if(b){if(St(b,2))return F(ce({replace:h},A(b.to),{state:typeof b.to=="object"?ce({},le,b.to.state):le,force:ve}),H||_)}else b=$(_,z,!0,h,le);return Y(_,z,b),b})}function C(x,H){const N=S(x,H);return N?Promise.reject(N):Promise.resolve()}function P(x){const H=ge.values().next().value;return H&&typeof H.runWithContext=="function"?H.runWithContext(x):x()}function U(x,H){let N;const[z,le,ve]=t0(x,H);N=Sr(z.reverse(),"beforeRouteLeave",x,H);for(const g of z)g.leaveGuards.forEach(_=>{N.push(Nt(_,x,H))});const h=C.bind(null,x,H);return N.push(h),Le(N).then(()=>{N=[];for(const g of s.list())N.push(Nt(g,x,H));return N.push(h),Le(N)}).then(()=>{N=Sr(le,"beforeRouteUpdate",x,H);for(const g of le)g.updateGuards.forEach(_=>{N.push(Nt(_,x,H))});return N.push(h),Le(N)}).then(()=>{N=[];for(const g of ve)if(g.beforeEnter)if(lt(g.beforeEnter))for(const _ of g.beforeEnter)N.push(Nt(_,x,H));else N.push(Nt(g.beforeEnter,x,H));return N.push(h),Le(N)}).then(()=>(x.matched.forEach(g=>g.enterCallbacks={}),N=Sr(ve,"beforeRouteEnter",x,H,P),N.push(h),Le(N))).then(()=>{N=[];for(const g of i.list())N.push(Nt(g,x,H));return N.push(h),Le(N)}).catch(g=>St(g,8)?g:Promise.reject(g))}function Y(x,H,N){l.list().forEach(z=>P(()=>z(x,H,N)))}function $(x,H,N,z,le){const ve=S(x,H);if(ve)return ve;const h=H===Lt,g=cn?history.state:{};N&&(z||h?r.replace(x.fullPath,ce({scroll:h&&g&&g.scroll},le)):r.push(x.fullPath,le)),a.value=x,Ue(x,H,N,h),Te()}let Z;function me(){Z||(Z=r.listen((x,H,N)=>{if(!ze.listening)return;const z=w(x),le=M(z);if(le){F(ce(le,{replace:!0}),z).catch(Un);return}f=z;const ve=a.value;cn&&Ap(Vi(ve.fullPath,N.delta),tr()),U(z,ve).catch(h=>St(h,12)?h:St(h,2)?(F(h.to,z).then(g=>{St(g,20)&&!N.delta&&N.type===eo.pop&&r.go(-1,!1)}).catch(Un),Promise.reject()):(N.delta&&r.go(-N.delta,!1),ne(h,z,ve))).then(h=>{h=h||$(z,ve,!1),h&&(N.delta&&!St(h,8)?r.go(-N.delta,!1):N.type===eo.pop&&St(h,20)&&r.go(-1,!1)),Y(z,ve,h)}).catch(Un)}))}let ye=kn(),ee=kn(),W;function ne(x,H,N){Te(x);const z=ee.list();return z.length?z.forEach(le=>le(x,H,N)):console.error(x),Promise.reject(x)}function De(){return W&&a.value!==Lt?Promise.resolve():new Promise((x,H)=>{ye.add([x,H])})}function Te(x){return W||(W=!x,me(),ye.list().forEach(([H,N])=>x?N(x):H()),ye.reset()),x}function Ue(x,H,N,z){const{scrollBehavior:le}=e;if(!cn||!le)return Promise.resolve();const ve=!N&&Tp(Vi(x.fullPath,0))||(z||!N)&&history.state&&history.state.scroll||null;return vt().then(()=>le(x,H,ve)).then(h=>h&&Cp(h)).catch(h=>ne(h,x,H))}const xe=x=>r.go(x);let je;const ge=new Set,ze={currentRoute:a,listening:!0,addRoute:m,removeRoute:p,clearRoutes:t.clearRoutes,hasRoute:y,getRoutes:v,resolve:w,options:e,push:E,replace:k,go:xe,back:()=>xe(-1),forward:()=>xe(1),beforeEach:s.add,beforeResolve:i.add,afterEach:l.add,onError:ee.add,isReady:De,install(x){const H=this;x.component("RouterLink",Jp),x.component("RouterView",vc),x.config.globalProperties.$router=H,Object.defineProperty(x.config.globalProperties,"$route",{enumerable:!0,get:()=>Re(a)}),cn&&!je&&a.value===Lt&&(je=!0,E(r.location).catch(le=>{}));const N={};for(const le in Lt)Object.defineProperty(N,le,{get:()=>a.value[le],enumerable:!0});x.provide(nr,H),x.provide(Cs,Il(N)),x.provide(Gr,a);const z=x.unmount;ge.add(x),x.unmount=function(){ge.delete(x),ge.size<1&&(f=Lt,Z&&Z(),Z=null,a.value=Lt,je=!1,W=!1),z()}}};function Le(x){return x.reduce((H,N)=>H.then(()=>P(N)),Promise.resolve())}return ze}function t0(e,t){const n=[],o=[],r=[],s=Math.max(t.matched.length,e.matched.length);for(let i=0;i<s;i++){const l=t.matched[i];l&&(e.matched.find(f=>wn(f,l))?o.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(f=>wn(f,a))||r.push(a))}return[n,o,r]}function or(){return ke(nr)}function As(e){return ke(Cs)}function _c(e){return es()?(ml(e),!0):!1}function Cr(){const e=new Set,t=r=>{e.delete(r)};return{on:r=>{e.add(r);const s=()=>t(r);return _c(s),{off:s}},off:t,trigger:(...r)=>Promise.all(Array.from(e).map(s=>s(...r)))}}function pt(e){return typeof e=="function"?e():Re(e)}const yc=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const n0=()=>{};function tl(e,t=!1,n="Timeout"){return new Promise((o,r)=>{setTimeout(t?()=>r(n):o,e)})}function o0(e,...t){return t.some(n=>n in e)}function Ar(...e){if(e.length!==1)return Ll(...e);const t=e[0];return typeof t=="function"?vn(Dl(()=>({get:t,set:n0}))):G(t)}function Yr(e,t=!1){function n(u,{flush:d="sync",deep:m=!1,timeout:p,throwOnTimeout:v}={}){let y=null;const A=[new Promise(S=>{y=be(e,E=>{u(E)!==t&&(y?y():vt(()=>y==null?void 0:y()),S(E))},{flush:d,deep:m,immediate:!0})})];return p!=null&&A.push(tl(p,v).then(()=>pt(e)).finally(()=>y==null?void 0:y())),Promise.race(A)}function o(u,d){if(!he(u))return n(E=>E===u,d);const{flush:m="sync",deep:p=!1,timeout:v,throwOnTimeout:y}=d??{};let w=null;const S=[new Promise(E=>{w=be([e,u],([k,M])=>{t!==(k===M)&&(w?w():vt(()=>w==null?void 0:w()),E(k))},{flush:m,deep:p,immediate:!0})})];return v!=null&&S.push(tl(v,y).then(()=>pt(e)).finally(()=>(w==null||w(),pt(e)))),Promise.race(S)}function r(u){return n(d=>!!d,u)}function s(u){return o(null,u)}function i(u){return o(void 0,u)}function l(u){return n(Number.isNaN,u)}function a(u,d){return n(m=>{const p=Array.from(m);return p.includes(u)||p.includes(pt(u))},d)}function f(u){return c(1,u)}function c(u=1,d){let m=-1;return n(()=>(m+=1,m>=u),d)}return Array.isArray(pt(e))?{toMatch:n,toContains:a,changed:f,changedTimes:c,get not(){return Yr(e,!t)}}:{toMatch:n,toBe:o,toBeTruthy:r,toBeNull:s,toBeNaN:l,toBeUndefined:i,changed:f,changedTimes:c,get not(){return Yr(e,!t)}}}function r0(e){return Yr(e)}function s0(e,t,n={}){const{immediate:o=!0}=n,r=G(!1);let s=null;function i(){s&&(clearTimeout(s),s=null)}function l(){r.value=!1,i()}function a(...f){i(),r.value=!0,s=setTimeout(()=>{r.value=!1,s=null,e(...f)},pt(t))}return o&&(r.value=!0,yc&&a()),_c(l),{isPending:vn(r),start:a,stop:l}}const i0=yc?window:void 0,l0={json:"application/json",text:"text/plain"};function $o(e){return e&&o0(e,"immediate","refetch","initialData","timeout","beforeFetch","afterFetch","onFetchError","fetch","updateDataOnError")}const a0=/^(?:[a-z][a-z\d+\-.]*:)?\/\//i;function c0(e){return a0.test(e)}function qn(e){return typeof Headers<"u"&&e instanceof Headers?Object.fromEntries(e.entries()):e}function ln(e,...t){return e==="overwrite"?async n=>{const o=t[t.length-1];return o?{...n,...await o(n)}:n}:async n=>{for(const o of t)o&&(n={...n,...await o(n)});return n}}function u0(e={}){const t=e.combination||"chain",n=e.options||{},o=e.fetchOptions||{};function r(s,...i){const l=_e(()=>{const c=pt(e.baseUrl),u=pt(s);return c&&!c0(u)?d0(c,u):u});let a=n,f=o;return i.length>0&&($o(i[0])?a={...a,...i[0],beforeFetch:ln(t,n.beforeFetch,i[0].beforeFetch),afterFetch:ln(t,n.afterFetch,i[0].afterFetch),onFetchError:ln(t,n.onFetchError,i[0].onFetchError)}:f={...f,...i[0],headers:{...qn(f.headers)||{},...qn(i[0].headers)||{}}}),i.length>1&&$o(i[1])&&(a={...a,...i[1],beforeFetch:ln(t,n.beforeFetch,i[1].beforeFetch),afterFetch:ln(t,n.afterFetch,i[1].afterFetch),onFetchError:ln(t,n.onFetchError,i[1].onFetchError)}),f0(l,f,a)}return r}function f0(e,...t){var n;const o=typeof AbortController=="function";let r={},s={immediate:!0,refetch:!1,timeout:0,updateDataOnError:!1};const i={method:"GET",type:"text",payload:void 0};t.length>0&&($o(t[0])?s={...s,...t[0]}:r=t[0]),t.length>1&&$o(t[1])&&(s={...s,...t[1]});const{fetch:l=(n=i0)==null?void 0:n.fetch,initialData:a,timeout:f}=s,c=Cr(),u=Cr(),d=Cr(),m=G(!1),p=G(!1),v=G(!1),y=G(null),w=yo(null),A=yo(null),S=yo(a||null),E=_e(()=>o&&p.value);let k,M;const F=()=>{o&&(k==null||k.abort(),k=new AbortController,k.signal.onabort=()=>v.value=!0,r={...r,signal:k.signal})},C=ee=>{p.value=ee,m.value=!ee};f&&(M=s0(F,f,{immediate:!1}));let P=0;const U=async(ee=!1)=>{var W,ne;F(),C(!0),A.value=null,y.value=null,v.value=!1,P+=1;const De=P,Te={method:i.method,headers:{}};if(i.payload){const ge=qn(Te.headers),ze=pt(i.payload),Le=Object.getPrototypeOf(ze);!i.payloadType&&ze&&(Le===Object.prototype||Array.isArray(Le))&&!(ze instanceof FormData)&&(i.payloadType="json"),i.payloadType&&(ge["Content-Type"]=(W=l0[i.payloadType])!=null?W:i.payloadType),Te.body=i.payloadType==="json"?JSON.stringify(ze):ze}let Ue=!1;const xe={url:pt(e),options:{...Te,...r},cancel:()=>{Ue=!0}};if(s.beforeFetch&&Object.assign(xe,await s.beforeFetch(xe)),Ue||!l)return C(!1),Promise.resolve(null);let je=null;return M&&M.start(),l(xe.url,{...Te,...xe.options,headers:{...qn(Te.headers),...qn((ne=xe.options)==null?void 0:ne.headers)}}).then(async ge=>{if(w.value=ge,y.value=ge.status,je=await ge.clone()[i.type](),!ge.ok)throw S.value=a||null,new Error(ge.statusText);return s.afterFetch&&({data:je}=await s.afterFetch({data:je,response:ge})),S.value=je,c.trigger(ge),ge}).catch(async ge=>{let ze=ge.message||ge.name;if(s.onFetchError&&({error:ze,data:je}=await s.onFetchError({data:je,error:ge,response:w.value})),A.value=ze,s.updateDataOnError&&(S.value=je),u.trigger(ge),ee)throw ge;return null}).finally(()=>{De===P&&C(!1),M&&M.stop(),d.trigger(null)})},Y=Ar(s.refetch);be([Y,Ar(e)],([ee])=>ee&&U(),{deep:!0});const $={isFinished:vn(m),isFetching:vn(p),statusCode:y,response:w,error:A,data:S,canAbort:E,aborted:v,abort:F,execute:U,onFetchResponse:c.on,onFetchError:u.on,onFetchFinally:d.on,get:Z("GET"),put:Z("PUT"),post:Z("POST"),delete:Z("DELETE"),patch:Z("PATCH"),head:Z("HEAD"),options:Z("OPTIONS"),json:ye("json"),text:ye("text"),blob:ye("blob"),arrayBuffer:ye("arrayBuffer"),formData:ye("formData")};function Z(ee){return(W,ne)=>{if(!p.value)return i.method=ee,i.payload=W,i.payloadType=ne,he(i.payload)&&be([Y,Ar(i.payload)],([De])=>De&&U(),{deep:!0}),{...$,then(De,Te){return me().then(De,Te)}}}}function me(){return new Promise((ee,W)=>{r0(m).toBe(!0).then(()=>ee($)).catch(W)})}function ye(ee){return()=>{if(!p.value)return i.type=ee,{...$,then(W,ne){return me().then(W,ne)}}}}return s.immediate&&Promise.resolve().then(()=>U()),{...$,then(ee,W){return me().then(ee,W)}}}function d0(e,t){return!e.endsWith("/")&&!t.startsWith("/")?`${e}/${t}`:e.endsWith("/")&&t.startsWith("/")?`${e.slice(0,-1)}${t}`:`${e}${t}`}const h0="modulepreload",p0=function(e){return"/"+e},nl={},te=function(t,n,o){let r=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),l=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));r=Promise.allSettled(n.map(a=>{if(a=p0(a),a in nl)return;nl[a]=!0;const f=a.endsWith(".css"),c=f?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${c}`))return;const u=document.createElement("link");if(u.rel=f?"stylesheet":h0,f||(u.as="script"),u.crossOrigin="",u.href=a,l&&u.setAttribute("nonce",l),document.head.appendChild(u),f)return new Promise((d,m)=>{u.addEventListener("load",d),u.addEventListener("error",()=>m(new Error(`Unable to preload CSS for ${a}`)))})}))}function s(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return r.then(i=>{for(const l of i||[])l.status==="rejected"&&s(l.reason);return t().catch(s)})};var m0=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function g0(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Im(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function o(){return this instanceof o?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(o){var r=Object.getOwnPropertyDescriptor(e,o);Object.defineProperty(n,o,r.get?r:{enumerable:!0,get:function(){return e[o]}})}),n}var bc={exports:{}};/* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
 * @license MIT */(function(e,t){(function(n,o){e.exports=o()})(m0,function(){var n={};n.version="0.2.0";var o=n.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};n.configure=function(p){var v,y;for(v in p)y=p[v],y!==void 0&&p.hasOwnProperty(v)&&(o[v]=y);return this},n.status=null,n.set=function(p){var v=n.isStarted();p=r(p,o.minimum,1),n.status=p===1?null:p;var y=n.render(!v),w=y.querySelector(o.barSelector),A=o.speed,S=o.easing;return y.offsetWidth,l(function(E){o.positionUsing===""&&(o.positionUsing=n.getPositioningCSS()),a(w,i(p,A,S)),p===1?(a(y,{transition:"none",opacity:1}),y.offsetWidth,setTimeout(function(){a(y,{transition:"all "+A+"ms linear",opacity:0}),setTimeout(function(){n.remove(),E()},A)},A)):setTimeout(E,A)}),this},n.isStarted=function(){return typeof n.status=="number"},n.start=function(){n.status||n.set(0);var p=function(){setTimeout(function(){n.status&&(n.trickle(),p())},o.trickleSpeed)};return o.trickle&&p(),this},n.done=function(p){return!p&&!n.status?this:n.inc(.3+.5*Math.random()).set(1)},n.inc=function(p){var v=n.status;return v?(typeof p!="number"&&(p=(1-v)*r(Math.random()*v,.1,.95)),v=r(v+p,0,.994),n.set(v)):n.start()},n.trickle=function(){return n.inc(Math.random()*o.trickleRate)},function(){var p=0,v=0;n.promise=function(y){return!y||y.state()==="resolved"?this:(v===0&&n.start(),p++,v++,y.always(function(){v--,v===0?(p=0,n.done()):n.set((p-v)/p)}),this)}}(),n.render=function(p){if(n.isRendered())return document.getElementById("nprogress");c(document.documentElement,"nprogress-busy");var v=document.createElement("div");v.id="nprogress",v.innerHTML=o.template;var y=v.querySelector(o.barSelector),w=p?"-100":s(n.status||0),A=document.querySelector(o.parent),S;return a(y,{transition:"all 0 linear",transform:"translate3d("+w+"%,0,0)"}),o.showSpinner||(S=v.querySelector(o.spinnerSelector),S&&m(S)),A!=document.body&&c(A,"nprogress-custom-parent"),A.appendChild(v),v},n.remove=function(){u(document.documentElement,"nprogress-busy"),u(document.querySelector(o.parent),"nprogress-custom-parent");var p=document.getElementById("nprogress");p&&m(p)},n.isRendered=function(){return!!document.getElementById("nprogress")},n.getPositioningCSS=function(){var p=document.body.style,v="WebkitTransform"in p?"Webkit":"MozTransform"in p?"Moz":"msTransform"in p?"ms":"OTransform"in p?"O":"";return v+"Perspective"in p?"translate3d":v+"Transform"in p?"translate":"margin"};function r(p,v,y){return p<v?v:p>y?y:p}function s(p){return(-1+p)*100}function i(p,v,y){var w;return o.positionUsing==="translate3d"?w={transform:"translate3d("+s(p)+"%,0,0)"}:o.positionUsing==="translate"?w={transform:"translate("+s(p)+"%,0)"}:w={"margin-left":s(p)+"%"},w.transition="all "+v+"ms "+y,w}var l=function(){var p=[];function v(){var y=p.shift();y&&y(v)}return function(y){p.push(y),p.length==1&&v()}}(),a=function(){var p=["Webkit","O","Moz","ms"],v={};function y(E){return E.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(k,M){return M.toUpperCase()})}function w(E){var k=document.body.style;if(E in k)return E;for(var M=p.length,F=E.charAt(0).toUpperCase()+E.slice(1),C;M--;)if(C=p[M]+F,C in k)return C;return E}function A(E){return E=y(E),v[E]||(v[E]=w(E))}function S(E,k,M){k=A(k),E.style[k]=M}return function(E,k){var M=arguments,F,C;if(M.length==2)for(F in k)C=k[F],C!==void 0&&k.hasOwnProperty(F)&&S(E,F,C);else S(E,M[1],M[2])}}();function f(p,v){var y=typeof p=="string"?p:d(p);return y.indexOf(" "+v+" ")>=0}function c(p,v){var y=d(p),w=y+v;f(y,v)||(p.className=w.substring(1))}function u(p,v){var y=d(p),w;f(p,v)&&(w=y.replace(" "+v+" "," "),p.className=w.substring(1,w.length-1))}function d(p){return(" "+(p.className||"")+" ").replace(/\s+/gi," ")}function m(p){p&&p.parentNode&&p.parentNode.removeChild(p)}return n})})(bc);var v0=bc.exports;const Ts=g0(v0),_0={class:"popup-content text-center p-8"},y0=["innerHTML"],b0={class:"flex justify-center"},w0={__name:"GlobalLayout",setup(e){const t=G(!1),n=G([]),o=G(null),r=As();at(()=>{s()});const s=async()=>{const{data:f,error:c}=await tn("/notification/list").get().json();f.value&&!c.value&&f.value!==200&&(n.value=f.value.data.notifications,l())},i=(f,c)=>{const u=new Date,d=u.getHours()*60+u.getMinutes(),m=f.split(":").map(Number),p=c.split(":").map(Number),v=m[0]*60+m[1],y=p[0]*60+p[1];return y<v?d>=v||d<y:d>=v&&d<=y},l=()=>{for(let f of n.value)if(i(f.startTime,f.endTime)&&f.notificationPage===r.path){o.value=f,t.value=!0;break}};be(()=>r.path,()=>{l()});const a=()=>{t.value=!1};return(f,c)=>{const u=Yo("router-view"),d=yh,m=ws;return Ye(),It(Ee,null,[L(u),L(m,{show:t.value,"onUpdate:show":c[1]||(c[1]=p=>t.value=p),round:"",onClickOverlay:a},{default:_n(()=>{var p;return[V("div",_0,[V("div",{innerHTML:(p=o.value)==null?void 0:p.content},null,8,y0),V("div",b0,[L(d,{type:"primary",onClick:c[0]||(c[0]=v=>t.value=!1),class:"w-24"},{default:_n(()=>c[2]||(c[2]=[Qn("关闭")])),_:1})])])]}),_:1},8,["show"])],64)}}},wc="/assets/public_security_record_icon-1_QVWmWl.png",Rm=Object.freeze(Object.defineProperty({__proto__:null,default:wc},Symbol.toStringTag,{value:"Module"})),Ps=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},x0={class:"home-layout min-h-screen flex flex-col"},E0={class:"content flex flex-col flex-1"},S0={__name:"HomeLayout",setup(e){const t=or(),n=As(),o=G("index"),r=gt([{title:"首页",icon:"home-o",name:"index"},{title:"资产",icon:"gold-coin-o",name:"agent"},{title:"智能助手",icon:"chat-o",name:"ai"},{title:"我的",icon:"user-o",name:"me"},{title:"更多",icon:"more-o",name:"more"}]);at(()=>{const l=n.name;o.value=l});const s=l=>{if(l==="more"){window.location.href="https://www.tianyuancha.cn";return}t.push({name:l})},i=()=>{window.location.href="https://work.weixin.qq.com/kfid/kfc8a32720024833f57"};return(l,a)=>{const f=Yo("router-view"),c=ep,u=Jh;return Ye(),It("div",x0,[a[2]||(a[2]=V("div",{class:"header"},[V("img",{class:"logo rounded-full overflow-hidden",src:xs,alt:"Logo"}),V("div",{class:"title"},"天远数据")],-1)),V("div",E0,[L(f)]),L(u,{modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=d=>o.value=d),onChange:s},{default:_n(()=>[(Ye(!0),It(Ee,null,ta(r,(d,m)=>(Ye(),Ro(c,{key:m,name:d.name,icon:d.icon},{default:_n(()=>[Qn(zo(d.title),1)]),_:2},1032,["name","icon"]))),128))]),_:1},8,["modelValue"]),V("div",{onClick:i,class:"complaint-button"},a[1]||(a[1]=[V("span",null,"投诉",-1)])),a[3]||(a[3]=_f('<div class="disclaimer" data-v-c7aab9bd><div class="flex flex-col items-center" data-v-c7aab9bd><div class="flex items-center" data-v-c7aab9bd><img class="w-4 h-4 mr-2" src="'+wc+'" alt="公安备案" data-v-c7aab9bd><text data-v-c7aab9bd>琼公网安备46010002000584号</text></div><div data-v-c7aab9bd><a class="text-blue-500" href="https://beian.miit.gov.cn" data-v-c7aab9bd> 琼ICP备2024048057号-1 </a></div></div><div data-v-c7aab9bd>海南天远大数据科技有限公司版权所有</div></div>',1))])}}},C0=Ps(S0,[["__scopeId","data-v-c7aab9bd"]]),ol={__name:"PageLayout",setup(e){const t=or(),n=As(),o=G(""),r=()=>{t.back()};return at(()=>{}),be(()=>n.meta.title,s=>{o.value=s||"默认标题"},{immediate:!0}),(s,i)=>{const l=Wh,a=Yo("router-view");return Ye(),It(Ee,null,[L(l,{fixed:"",border:!1,placeholder:"",title:o.value,"left-text":"返回","left-arrow":"",onClickLeft:r},null,8,["title"]),L(a)],64)}}},xc="/assets/banner-NCGQsa2a.png",km=Object.freeze(Object.defineProperty({__proto__:null,default:xc},Symbol.toStringTag,{value:"Module"})),A0="data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1739502172305'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='4607'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='48'%20height='48'%3e%3cpath%20d='M569.2%20788.4H215.8c-77.8%200-141.5-63.7-141.5-141.5V207.5C74.3%20129.6%20137.9%2066%20215.8%2066h353.4c77.8%200%20141.5%2063.7%20141.5%20141.5v439.4c0%2077.8-63.7%20141.5-141.5%20141.5z'%20fill='%23D6F3FA'%20p-id='4608'%3e%3c/path%3e%3cpath%20d='M678.8%20276.5v42.7c0%200.6-0.5%201.1-1.1%201.1h-418c-0.6%200-1.1-0.5-1.1-1.1v-42.7c0-0.6%200.5-1.1%201.1-1.1h418c0.6%200%201.1%200.5%201.1%201.1zM677.6%20463h-418c-0.6%200-1.1%200.5-1.1%201.1v42.7c0%200.6%200.5%201.1%201.1%201.1h418c0.6%200%201.1-0.5%201.1-1.1v-42.7c0.1-0.6-0.4-1.1-1.1-1.1zM485%20650.7H259.6c-0.6%200-1.1%200.5-1.1%201.1v42.7c0%200.6%200.5%201.1%201.1%201.1H485c0.6%200%201.1-0.5%201.1-1.1v-42.7c0-0.6-0.5-1.1-1.1-1.1z%20m466.5-24.1c0-59.1-48.1-107.3-107.3-107.3-59.1%200-107.3%2048.1-107.3%20107.3%200%2059.1%2048.1%20107.3%20107.3%20107.3%2059.2-0.1%20107.3-48.2%20107.3-107.3z%20m-34.4%200c0%2040.2-32.7%2072.9-72.9%2072.9s-72.9-32.7-72.9-72.9%2032.7-72.9%2072.9-72.9%2072.9%2032.7%2072.9%2072.9zM706%20759.5c0-50.9-41.4-92.3-92.3-92.3s-92.3%2041.4-92.3%2092.3%2041.4%2092.3%2092.3%2092.3c50.9-0.1%2092.3-41.4%2092.3-92.3z%20m-34.4%200c0%2031.9-26%2057.9-57.9%2057.9s-57.9-26-57.9-57.9%2026-57.9%2057.9-57.9%2057.9%2026%2057.9%2057.9z%20m135.1%2080.4c-33.2%200-60%2026.9-60%2060%200%2033.2%2026.9%2060%2060%2060s60-26.9%2060-60c0-33.2-26.8-60-60-60z%20m-27.2-148.1L755%20649.3l-83.5%2048.2L696%20740l83.5-48.2z%20m-105.7%2099.9L650.5%20832%20787%20910.8l23.3-40.3-136.5-78.8z%20m-135.1%2095.4c0-12.4-10.1-22.5-22.5-22.5H246c-41.5%200-75.1-33.6-75.1-75.1V234.1c0-41.5%2033.6-75.1%2075.1-75.1h52.5c12.4%200%2022.5-10.1%2022.5-22.5S310.9%20114%20298.5%20114H246c-66.3%200-120.1%2053.8-120.1%20120.1v555.4c0%2066.3%2053.8%20120.1%20120.1%20120.1h270.2c12.4%200%2022.5-10.1%2022.5-22.5z%20m277.7-488.3V234.1c0-66.3-53.8-120.1-120.1-120.1H448.6c-12.4%200-22.5%2010.1-22.5%2022.5s10.1%2022.5%2022.5%2022.5h247.7c41.5%200%2075.1%2033.6%2075.1%2075.1v164.7c0%2012.4%2010.1%2022.5%2022.5%2022.5s22.5-10.1%2022.5-22.5zM408.1%20157.9v-42.7c0-0.6-0.5-1.1-1.1-1.1h-65.3c-0.6%200-1.1%200.5-1.1%201.1v42.7c0%200.6%200.5%201.1%201.1%201.1H407c0.6%200%201.1-0.5%201.1-1.1z'%20fill='%2318BAE5'%20p-id='4609'%3e%3c/path%3e%3c/svg%3e",T0="data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1739502235932'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='12319'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='48'%20height='48'%3e%3cpath%20d='M512%200C229.2%200%200%20229.2%200%20512s229.2%20512%20512%20512%20512-229.2%20512-512S794.8%200%20512%200z%20m177.3%20336.7c51%200%2092.7%2044.7%2092.7%2099.8s-41.4%2099.8-92.7%2099.8c-43.6%200-80.5-32.3-90.2-76.2%2015.5-20%2024.4-45.9%2024.4-73.9%200-6.4-0.7-12.4-1.5-18.4%2017.1-19.1%2040.7-31.1%2067.3-31.1zM515%20282.1c53.6%200%2096.8%2046.7%2096.8%20104.2s-43.6%20104.2-97.2%20104.2-96.8-46.7-96.8-104.2%2043.6-104.2%2097.2-104.2z%20m-181.1%2054.6c29.2%200%2055.4%2014.4%2072.4%2037.1-0.4%204-0.7%208-0.7%2012%200%2025.1%207.4%2048.3%2019.9%2067.5-7.4%2047.1-45.8%2083-91.6%2083-51.4%200-92.7-44.7-92.7-99.8%200-55%2041.4-99.8%2092.7-99.8zM179.5%20741.9c0-99.8%2069.5-180.4%20154.8-180.4%2016.6%200%2033.3%203.2%2049.1%209.2-42.5%2041.5-70.9%20102.6-74.3%20171.3l-129.6-0.1z%20m153.7%200c0-117%2081.3-212%20181.8-212s181.8%2095%20182.2%20212h-364z%20m387.6-0.4c-3.7-69.5-32.5-131.3-76.1-172.5%2014.4-4.8%2029.9-7.6%2045.1-7.6%2085.3%200%20154.8%2080.6%20154.8%20180l-123.8%200.1z'%20fill='%232ACE4D'%20p-id='12320'%3e%3c/path%3e%3c/svg%3e",P0="data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1741855510780'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='2636'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='200'%20height='200'%3e%3cpath%20d='M853.64224%2076.7488H155.25888c-49.03424%200-88.75008%2040.14592-88.75008%2089.18528l-0.41472%20766.18752c-0.00512%2013.45024%2016.256%2020.18816%2025.76384%2010.68032l148.16256-148.16256a15.09376%2015.09376%200%200%201%2010.6752-4.41856h602.94656c57.34912%200%20104.26368-46.9248%20104.26368-104.2688V181.02784c0-57.35424-46.91456-104.27904-104.26368-104.27904z%20m-299.33056%20544.1792a15.09376%2015.09376%200%200%201-15.08864%2015.08864H472.73472a15.09376%2015.09376%200%200%201-15.09376-15.08864v-60.5696a15.09376%2015.09376%200%200%201%2015.09376-15.08864h66.4832a15.09376%2015.09376%200%200%201%2015.08864%2015.08864v60.5696h0.00512z%20m39.45984-199.94112c-35.16928%2024.76032-51.65056%2048.84992-49.39776%2072.30464%200.05632%200.4608%200.08704%200.9216%200.08704%201.3824a15.08864%2015.08864%200%200%201-15.09376%2015.08864h-48.73216a15.08352%2015.08352%200%200%201-15.08864-15.08864v-6.60992c-1.32608-40.61184%2015.6672-72.77056%2050.92352-96.41984%200.24064-0.16384%200.49152-0.33792%200.72704-0.51712%2032.6144-24.86784%2048.2816-48.45056%2046.99136-70.74816-2.62144-27.33056-18.0992-42.40896-46.4384-45.29152a16.45568%2016.45568%200%200%200-1.81248-0.08192c-31.42144%200.3584-52.42368%2019.4816-62.98112%2057.3696a15.13472%2015.13472%200%200%201-17.4592%2010.85952L374.1696%20331.23328a15.11424%2015.11424%200%200%201-11.70944-18.61632c20.54144-80.15872%2076.60032-118.99392%20168.17152-116.5312%2081.30048%205.25312%20126.0032%2041.89696%20134.08256%20109.88544%200.0512%200.38912%200.08192%200.78848%200.10752%201.1776%202.3808%2044.47744-21.2992%2082.41152-71.05024%20113.83808z'%20fill='%23397B8B'%20p-id='2637'%3e%3c/path%3e%3c/svg%3e",O0="data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1740754594473'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='36608'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='200'%20height='200'%3e%3cpath%20d='M102.4%20256a102.4%20102.4%200%200%201%20102.4-102.4h340.2752a102.4%20102.4%200%200%201%2095.0784%2064.3584L665.6%20281.6c3.3792%208.3968%205.4272%2017.0496%206.2464%2025.6H819.2a51.2%2051.2%200%200%201%2051.2%2051.2v512a51.2%2051.2%200%200%201-51.2%2051.2H153.6a51.2%2051.2%200%200%201-51.2-51.2V256z'%20fill='%232357DF'%20opacity='.5'%20p-id='36609'%3e%3c/path%3e%3cpath%20d='M238.592%20409.6h681.8304a51.2%2051.2%200%200%201%2050.176%2061.44l-83.8656%20409.6a51.2%2051.2%200%200%201-50.176%2040.96H154.7776a51.2%2051.2%200%200%201-50.176-61.44l83.8656-409.6a51.2%2051.2%200%200%201%2050.176-40.96z'%20fill='%232357DF'%20p-id='36610'%3e%3c/path%3e%3cpath%20d='M358.4%20640m25.6%200l307.2%200q25.6%200%2025.6%2025.6l0%200q0%2025.6-25.6%2025.6l-307.2%200q-25.6%200-25.6-25.6l0%200q0-25.6%2025.6-25.6Z'%20fill='%23FFFFFF'%20p-id='36611'%3e%3c/path%3e%3c/svg%3e",Ec="/assets/index_b_banner-BpyDZxbT.png",Dm=Object.freeze(Object.defineProperty({__proto__:null,default:Ec},Symbol.toStringTag,{value:"Module"})),Sc="/assets/bg_icon-fK_2UIqI.png",Lm=Object.freeze(Object.defineProperty({__proto__:null,default:Sc},Symbol.toStringTag,{value:"Module"})),Cc="/assets/index_icon_1-DuSS1QwX.png",Mm=Object.freeze(Object.defineProperty({__proto__:null,default:Cc},Symbol.toStringTag,{value:"Module"})),Ac="/assets/index_icon_2-DltzlS5K.png",$m=Object.freeze(Object.defineProperty({__proto__:null,default:Ac},Symbol.toStringTag,{value:"Module"})),Tc="/assets/index_icon_3-DKlE-IyE.png",Bm=Object.freeze(Object.defineProperty({__proto__:null,default:Tc},Symbol.toStringTag,{value:"Module"})),Pc="/assets/index_icon_4-0U4lCmFY.png",Fm=Object.freeze(Object.defineProperty({__proto__:null,default:Pc},Symbol.toStringTag,{value:"Module"})),Oc="/assets/index_icon_5-BeZ5uoUf.png",Nm=Object.freeze(Object.defineProperty({__proto__:null,default:Oc},Symbol.toStringTag,{value:"Module"})),Ic="/assets/index_icon_6-DWvKoAxi.png",Vm=Object.freeze(Object.defineProperty({__proto__:null,default:Ic},Symbol.toStringTag,{value:"Module"})),Rc="/assets/index_icon_7-D2becLow.png",jm=Object.freeze(Object.defineProperty({__proto__:null,default:Rc},Symbol.toStringTag,{value:"Module"})),I0="home",rl={layout:I0},R0={class:"box-border min-h-screen from-blue-100 to-white bg-gradient-to-b"},k0={class:"relative p-4 pb-4 pt-2"},D0={class:"grid grid-cols-2 gap-3"},L0=["onClick"],M0={class:"min-h-18 flex flex-col items-start px-1"},$0={class:"mt-1 max-w-max text-left text-gray-600 font-bold"},B0={class:"max-w-max text-left text-xs text-gray-600"},kc={__name:"index",setup(e){const t=or(),n=co();Hr(n);function o(u){t.push(`/inquire/${u}`)}function r(){t.push({name:"invitation"})}const s=()=>{t.push({name:"promote"})},i=()=>{window.location.href="https://www.tybigdata.com/"},l=()=>{t.push("/help")};function a(){window.location.href="https://www.tianyuandata.com"}const f=G([{title:"个人风险",name:"riskassessment",subtitle:"一查全知",bg:Ac,bgColor:"bg-indigo-400",position:"rounded-tl-[35px] rounded-bl-[35px] rounded-tr-lg rounded-br-lg"},{title:"婚恋风险",name:"marriage",subtitle:"查婚姻状态让爱无忧",bg:Cc,bgColor:" bg-pink-400 ",position:"rounded-tr-[35px] rounded-br-[35px] rounded-tl-lg rounded-bl-lg"},{title:"家政风险",name:"homeservice",subtitle:"用人有保障",bg:Tc,bgColor:" bg-teal-500 ",position:"rounded-tl-[35px] rounded-bl-[35px] rounded-tr-lg rounded-br-lg"},{title:"租赁风险",name:"rentalinfo",subtitle:"一查明了",bg:Pc,bgColor:" bg-sky-500  ",position:"rounded-tl-[35px] rounded-bl-[35px] rounded-tr-lg rounded-br-lg"},{title:"老板企业风险",name:"companyinfo",subtitle:"合作更安心",bg:Oc,bgColor:" bg-blue-400  ",position:"rounded-tr-[35px] rounded-br-[35px] rounded-tl-lg rounded-bl-lg"},{title:"入职风险",name:"backgroundcheck",subtitle:"招聘有保障，选人更放心",bg:Ic,bgColor:" bg-orange-400 ",position:"rounded-tl-[35px] rounded-bl-[35px] rounded-tr-lg rounded-br-lg"},{title:"贷前风险",name:"preloanbackgroundcheck",subtitle:"招聘有保障，选人更放心",bg:Rc,bgColor:" bg-orange-400 ",position:"rounded-tr-[35px] rounded-br-[35px] rounded-tl-lg rounded-bl-lg"}]);G([]);function c(){t.push("/historyQuery")}return(u,d)=>{const m=Yo("rich-text");return Ye(),It("div",R0,[d[7]||(d[7]=V("div",{class:"relative p-4"},[V("img",{class:"h-full w-full rounded-xl overflow-hidden",src:xc})],-1)),V("div",null,[V("div",{class:"flex items-center justify-around gap-3 px-6 pb-1"},[V("div",{class:"",onClick:s},d[0]||(d[0]=[V("div",{class:"h-16 w-16 p-2 bg-gradient-to-b from-white to-blue-100/10 rounded-full shadow-lg flex items-center justify-center"},[V("img",{src:A0,alt:"直推报告",class:"w-12 h-12"})],-1),V("div",{class:"text-center mt-1 font-bold"},"直推报告",-1)])),V("div",{class:"",onClick:r},d[1]||(d[1]=[V("div",{class:"h-16 w-16 p-2 bg-gradient-to-b from-white to-blue-100/10 rounded-full shadow-lg flex items-center justify-center"},[V("img",{src:T0,alt:"邀请下级",class:"w-12 h-12"})],-1),V("div",{class:"text-center mt-1 font-bold"},"邀请下级",-1)])),V("div",{class:"",onClick:l},d[2]||(d[2]=[V("div",{class:"h-16 w-16 p-2 bg-gradient-to-b from-white to-blue-100/10 rounded-full shadow-lg flex items-center justify-center"},[V("img",{src:P0,alt:"帮助中心",class:"w-12 h-12"})],-1),V("div",{class:"text-center mt-1 font-bold"},"帮助中心",-1)])),V("div",{class:"",onClick:c},d[3]||(d[3]=[V("div",{class:"h-16 w-16 p-2 bg-gradient-to-b from-white to-blue-100/10 rounded-full shadow-lg flex items-center justify-center"},[V("img",{src:O0,alt:"我的报告",class:"w-12 h-12"})],-1),V("div",{class:"text-center mt-1 font-bold"},"我的报告",-1)]))])]),V("div",k0,[V("div",D0,[(Ye(!0),It(Ee,null,ta(Re(f),(p,v)=>(Ye(),It("div",{key:v,class:Pt(["relative flex flex-col px-4 py-2 shadow-lg min-h-24",[p.position,p.bgColor,p.title==="婚恋风险"?"row-span-2":""]]),style:Ho(`background: url(${p.bg}) no-repeat; background-size: cover; background-position: center;`),onClick:y=>o(p.name)},[V("div",M0,[V("div",$0,zo(p.title),1),V("div",B0,[L(m,{nodes:p.subtitle},null,8,["nodes"])]),V("div",{class:Pt(["mt-2 rounded-2xl px-2 text-xs text-white",[p.bgColor]])}," GO > ",2)])],14,L0))),128))]),V("div",{class:"mb-6 mt-6 py-4 flex flex-col items-center justify-center rounded-3xl from-blue-500 to-sky-400 bg-gradient-to-b text-center text-lg text-white line-height-12 shadow-xl",onClick:a},d[4]||(d[4]=[V("div",{class:"flex items-center text-xl"}," 天远数据邀您共赢，共享数据新价值！ ",-1),V("div",{class:"flex items-center"},"点击进入商务合作",-1)])),V("div",{class:"mt-4 rounded-2xl overflow-hidden",onClick:i},d[5]||(d[5]=[V("img",{src:Ec,class:"w-full h-full",mode:"widthFix"},null,-1)])),V("div",{class:"mt-4 box-border h-14 w-full flex items-center rounded-xl bg-white px-4 text-gray-700 shadow-xl",onClick:c},d[6]||(d[6]=[V("img",{class:"mr-4 h-10 w-10",src:Sc,mode:"widthFix"},null,-1),V("div",{class:""},[V("div",{class:"font-bold"},"我的历史查询记录"),V("div",{class:"text-xs"},"查询记录有效期为30天")],-1)]))])])}}};typeof rl=="function"&&rl(kc);const F0=Ps(kc,[["__scopeId","data-v-c348e695"]]),rr=vs("user",{state:()=>({userName:"",mobile:"",userAvatar:"",isLoggedIn:!1}),actions:{async fetchUserInfo(){const{data:e,error:t}=await tn("/user/detail").get().json();if(e.value&&!t.value)if(e.value.code===200){const n=e.value.data.userInfo;this.userName=n.mobile||"",this.mobile=n.mobile||"",this.userAvatar=n.userAvatar,this.isLoggedIn=!0,localStorage.setItem("userInfo",JSON.stringify({nickName:this.userName,avatar:this.userAvatar}))}else e.value.code===100009&&(localStorage.removeItem("token"),localStorage.removeItem("refreshAfter"),localStorage.removeItem("accessExpire"),localStorage.removeItem("userInfo"),localStorage.removeItem("agentInfo"),this.resetUser(),window.location.reload())},updateUserInfo(e){e&&(this.userName=e.mobile||e.nickName||"",this.userAvatar=e.avatar||"",this.isLoggedIn=!0)},resetUser(){this.userName="",this.userAvatar="",this.isLoggedIn=!1}}}),Dc=G(!1),N0=navigator.userAgent.toLowerCase(),V0=["micromessenger","wechat"].map(e=>e.toLowerCase());Dc.value=V0.some(e=>N0.includes(e));function Lc(){return{isWeChat:Dc}}const to=e0({history:Rp("/"),routes:[{path:"/",component:w0,children:[{path:"",component:C0,children:[{path:"",name:"index",component:F0},{path:"ai",name:"ai",component:()=>te(()=>import("./Ai-YqploJsL.js"),__vite__mapDeps([0,1,2]))},{path:"/agent",name:"agent",component:()=>te(()=>import("./Agent-CkNdLRoi.js"),__vite__mapDeps([3,4]))},{path:"me",name:"me",component:()=>te(()=>import("./Me-Dl0_HAnC.js"),__vite__mapDeps([5,6,7,8]))}]},{path:"",component:ol,children:[{path:"/historyQuery",name:"history",component:()=>te(()=>import("./HistoryQuery-1iwapoY5.js"),__vite__mapDeps([9,10,11,12,13,14,15])),meta:{title:"历史报告",requiresAuth:!0}},{path:"/help",name:"help",component:()=>te(()=>import("./Help-D3_uXjp4.js"),__vite__mapDeps([16,17,18,13,14,19,20,11,21,22,23,24])),meta:{title:"帮助中心"}},{path:"/help/detail",name:"helpDetail",component:()=>te(()=>import("./HelpDetail-D1SW-URi.js"),__vite__mapDeps([25,26])),meta:{title:"帮助中心"}},{path:"/help/guide",name:"helpGuide",component:()=>te(()=>import("./HelpGuide-CQ7UBUyy.js"),__vite__mapDeps([27,28])),meta:{title:"引导指南"}},{path:"/promote",name:"promote",component:()=>te(()=>import("./Promote-DfHzGVfL.js"),__vite__mapDeps([29,30,31,32,21,33,24,17,18,34,35,13,14,36,20,37,6,7,38,23])),meta:{title:"推广",requiresAuth:!0}},{path:"/withdraw",name:"withdraw",component:()=>te(()=>import("./Withdraw-DLB0_riQ.js"),__vite__mapDeps([39,13,14,36,20,37,40])),meta:{title:"提现",requiresAuth:!0}},{path:"/service",name:"service",component:()=>te(()=>import("./Service-BAdH7Lfo.js"),__vite__mapDeps([41,42])),meta:{title:"客服"}},{path:"/complaint",name:"complaint",component:()=>te(()=>import("./Complaint-CVQGXIDi.js"),__vite__mapDeps([43,44])),meta:{title:"投诉"}},{path:"/report",name:"report",component:()=>te(()=>import("./Report-eLfJCQYI.js"),__vite__mapDeps([45,46,19,20,11,21,47,23,24,48])),meta:{title:"报告结果",requiresAuth:!0,notNeedBindPhone:!0}},{path:"/example",name:"example",component:()=>te(()=>import("./Example-DNOHy4qv.js"),__vite__mapDeps([49,46,19,20,11,21,47,23,24])),meta:{title:"示例报告",notNeedBindPhone:!0}},{path:"/privacyPolicy",name:"privacyPolicy",component:()=>te(()=>import("./PrivacyPolicy-CD-0cajn.js"),[]),meta:{title:"隐私政策"}},{path:"/userAgreement",name:"userAgreement",component:()=>te(()=>import("./UserAgreement-D2B7i49f.js"),[]),meta:{title:"用户协议"}},{path:"/agentManageAgreement",name:"agentManageAgreement",component:()=>te(()=>import("./AgentManageAgreement-CN9hTEpX.js"),[]),meta:{title:"代理管理协议"}},{path:"/agentSerivceAgreement",name:"agentSerivceAgreement",component:()=>te(()=>import("./AgentServiceAgreement-D0oVWSj9.js"),__vite__mapDeps([50,51])),meta:{title:"信息技术服务合同"}},{path:"/inquire/:feature",name:"inquire",component:()=>te(()=>import("./Inquire-9DoGhFZB.js"),__vite__mapDeps([52,34,35,13,14,36,20,37,53,54,17,18,55,56,57,58,59,60,23,24])),meta:{title:"查询报告",requiresAuth:!0}},{path:"/authorization",name:"authorization",component:()=>te(()=>import("./Authorization-DS9nKW8t.js"),[]),meta:{title:"授权书"}},{path:"/payment/result",name:"paymentResult",component:()=>te(()=>import("./PaymentResult-BIo51pxg.js"),__vite__mapDeps([61,62])),meta:{title:"支付结果",requiresAuth:!0}}]},{path:"agent",component:ol,children:[{path:"promoteDetails",name:"promoteDetails",component:()=>te(()=>import("./AgentPromoteDetails-DaVxxXhZ.js"),__vite__mapDeps([63,10,11,12,64])),meta:{title:"直推报告收益明细",requiresAuth:!0,requiresAgent:!0}},{path:"rewardsDetails",name:"rewardsDetails",component:()=>te(()=>import("./AgentRewardsDetails-Ce1uKtNU.js"),__vite__mapDeps([65,10,11,12,66])),meta:{title:"代理奖励收益明细",requiresAuth:!0,requiresAgent:!0}},{path:"promote",name:"promote",component:()=>te(()=>import("./Promote-DfHzGVfL.js"),__vite__mapDeps([29,30,31,32,21,33,24,17,18,34,35,13,14,36,20,37,6,7,38,23])),meta:{title:"直推报告",requiresAuth:!0,requiresAgent:!0}},{path:"invitation",name:"invitation",component:()=>te(()=>import("./Invitation-BqOb762L.js"),__vite__mapDeps([67,30,31,32,21,33,24,68,53])),meta:{title:"邀请下级",requiresAuth:!0,requiresAgent:!0}},{path:"agentVip",name:"agentVip",component:()=>te(()=>import("./AgentVip-xsQMH3ix.js"),__vite__mapDeps([69,70])),meta:{title:"代理会员",requiresAuth:!0,requiresAgent:!0}},{path:"vipApply",name:"agentVipApply",component:()=>te(()=>import("./AgentVipApply-B035qoW2.js"),__vite__mapDeps([71,54,17,18,13,14,55,56,57,72])),meta:{title:"VIP代理申请",requiresAuth:!0,requiresAgent:!0}},{path:"vipConfig",name:"agentVipConfig",component:()=>te(()=>import("./AgentVipConfig-Cwd5W1mV.js"),__vite__mapDeps([73,31,32,34,35,13,14,36,20,37,74,23,24])),meta:{title:"代理会员报告配置",requiresAuth:!0,requiresAgent:!0}},{path:"withdraw",name:"withdraw",component:()=>te(()=>import("./Withdraw-DLB0_riQ.js"),__vite__mapDeps([39,13,14,36,20,37,40])),meta:{title:"提现",requiresAuth:!0,requiresAgent:!0}},{path:"withdrawDetails",name:"withdrawDetails",component:()=>te(()=>import("./WithdrawDetails-DqfIjLfk.js"),__vite__mapDeps([75,10,11,12,76])),meta:{title:"提现记录",requiresAuth:!0,requiresAgent:!0}},{path:"invitationAgentApply/self",name:"invitationAgentApplySelf",component:()=>te(()=>import("./InvitationAgentApply-BRDP4xVU.js"),__vite__mapDeps([77,55,56,13,14,36,20,37,19,11,21,78,53,79,23,24])),meta:{title:"代理申请",requiresAuth:!0}},{path:"subordinateList",name:"subordinateList",component:()=>te(()=>import("./SubordinateList-Zky1_PwQ.js"),__vite__mapDeps([80,81,82,10,11,12,83])),meta:{title:"我的下级",requiresAuth:!0,requiresAgent:!0}},{path:"subordinateDetail/:id",name:"subordinateDetail",component:()=>te(()=>import("./SubordinateDetail-B2wITVyQ.js"),__vite__mapDeps([84,81,82,10,11,12,85])),meta:{title:"下级贡献详情",requiresAuth:!0,requiresAgent:!0}}]},{path:"app",children:[{path:"authorization",name:"appAuthorization",component:()=>te(()=>import("./Authorization-DS9nKW8t.js"),[]),meta:{title:"授权书"}},{path:"privacyPolicy",name:"appPrivacyPolicy",component:()=>te(()=>import("./PrivacyPolicy-CD-0cajn.js"),[]),meta:{title:"隐私政策"}},{path:"userAgreement",name:"appUserAgreement",component:()=>te(()=>import("./UserAgreement-D2B7i49f.js"),[]),meta:{title:"用户协议"}},{path:"agentManageAgreement",name:"appAgentManageAgreement",component:()=>te(()=>import("./AgentManageAgreement-CN9hTEpX.js"),[]),meta:{title:"代理管理协议"}},{path:"agentSerivceAgreement",name:"appAgentSerivceAgreement",component:()=>te(()=>import("./AgentServiceAgreement-D0oVWSj9.js"),__vite__mapDeps([50,51])),meta:{title:"信息技术服务合同"}}]}]},{path:"/login",name:"login",component:()=>te(()=>import("./Login-DrsmQHFZ.js"),__vite__mapDeps([86,87]))},{path:"/agent/promotionInquire/:linkIdentifier",name:"promotionInquire",component:()=>te(()=>import("./PromotionInquire-xtNF1XAp.js"),__vite__mapDeps([88,34,35,13,14,36,20,37,53,54,17,18,55,56,57,58,59,89,23,24])),meta:{notNeedBindPhone:!0}},{path:"/agent/invitationAgentApply/:linkIdentifier",name:"invitationAgentApply",component:()=>te(()=>import("./InvitationAgentApply-BRDP4xVU.js"),__vite__mapDeps([77,55,56,13,14,36,20,37,19,11,21,78,53,79,23,24])),meta:{title:"代理申请"}},{path:"/report/share/:linkIdentifier",name:"reportShare",component:()=>te(()=>import("./ReportShare-vY0DFMhW.js"),__vite__mapDeps([90,46,19,20,11,21,47,23,24,91]))},{path:"/:pathMatch(.*)*",name:"NotFound",component:()=>te(()=>import("./NotFound-Dy1NN-TG.js"),__vite__mapDeps([92,93]))}]});Ts.configure({easing:"ease",speed:500,showSpinner:!1,trickleSpeed:200,minimum:.3});to.beforeEach(async(e,t,n)=>{Ts.start();const o=localStorage.getItem("token"),r=co(),s=rr(),i=Es(),{isWeChat:l}=Lc(),{isAgent:a,isLoaded:f}=Hr(r),{mobile:c,isLoggedIn:u}=Hr(s);if(e.meta.requiresAuth&&!o){l.value?(n("/"),location.reload()):n("/login");return}if(o){if(u.value||await s.fetchUserInfo(),!c.value&&e.meta.requiresAuth&&!e.meta.notNeedBindPhone){i.openBindPhone(),n(!1);return}if(e.meta.requiresAgent&&(f.value||await r.fetchAgentStatus(),!a.value)){n("/agent/invitationAgentApply/self");return}}n()});to.afterEach(()=>{const{aplus_queue:e}=window;e.push({action:"aplus.sendPV",arguments:[{is_auto:!1}]}),Ts.done()});const tn=u0({baseUrl:"/api/v1",options:{async beforeFetch({url:e,options:t}){Uh({message:"加载中...",forbidClick:!0,duration:0,loadingType:"spinner"});const n=Date.now(),o=e.includes("?")?"&":"?";e+=`${o}t=${n}`;const r=localStorage.getItem("token");let s="h5";const i=navigator.userAgent.toLowerCase();return/micromessenger/.test(i)&&(s="wxh5"),t.headers["X-Platform"]=s,r&&(t.headers={...t.headers,Authorization:`${r}`}),{url:e,options:t}},async afterFetch({data:e,response:t}){if($i(),t.status===401&&(localStorage.removeItem("token"),localStorage.removeItem("refreshAfter"),localStorage.removeItem("accessExpire"),to.replace("/login")),e.code!==200){if(e.code===100009){localStorage.removeItem("token"),localStorage.removeItem("refreshAfter"),localStorage.removeItem("accessExpire"),localStorage.removeItem("userInfo"),localStorage.removeItem("agentInfo");const n=rr(),o=co();n.resetUser(),o.resetAgent(),location.reload()}e.code!==200002&&e.code!==200003&&e.code!==200004&&e.code!==100009&&tt({message:e.msg})}return{data:e,response:t}},async onFetchError({error:e,response:t}){return console.log("error",e),$i(),t.status===401?(localStorage.removeItem("token"),localStorage.removeItem("refreshAfter"),localStorage.removeItem("accessExpire"),to.replace("/login")):typeof e=="string"&&tt({message:e}),{error:e}}}}),co=vs("agent",{state:()=>({isLoaded:!1,level:"",status:3,isAgent:!1,ancestorID:null,agentID:null,mobile:"",ExpiryTime:"",isRealName:!1}),actions:{async fetchAgentStatus(){const{data:e,error:t}=await tn("/agent/info").get().json();e.value&&!t.value&&(e.value.code===200?(this.level=e.value.data.level,this.isAgent=e.value.data.is_agent,this.status=e.value.data.status,this.agentID=e.value.data.agent_id,this.mobile=e.value.data.mobile,this.ExpiryTime=e.value.data.expiry_time,this.isRealName=e.value.data.is_real_name,localStorage.setItem("agentInfo",JSON.stringify({isAgent:this.isAgent,level:this.level,status:this.status,agentID:this.agentID,mobile:this.mobile,ExpiryTime:this.ExpiryTime,isRealName:this.isRealName}))):console.log("Error fetching agent info",e.value)),this.isLoaded=!0},updateAgentInfo(e){e&&(this.isAgent=e.isAgent||!1,this.level=e.level||"",this.status=e.status||3,this.agentID=e.agentID||null,this.mobile=e.mobile||"",this.isLoaded=!0,this.isRealName=e.isRealName||!1)},resetAgent(){this.isLoaded=!1,this.level="",this.status=3,this.isAgent=!1,this.ancestorID=null,this.agentID=null,this.mobile="",this.isRealName=!1}}}),j0={key:0},H0={class:"bind-phone-dialog"},U0={class:"title-bar"},z0={class:"px-8"},q0={class:"space-y-5"},K0={class:"flex items-center justify-between"},W0={class:"flex items-start space-x-2"},G0={__name:"BindPhoneDialog",emits:["bind-success"],setup(e,{emit:t}){const n=t,o=or(),r=Es(),s=co(),i=rr(),l=G(""),a=G(""),f=G(!1),c=G(60),u=G(!1);let d=null;const m=G(!1),p=G(!1),v=_e(()=>/^1[3-9]\d{9}$/.test(l.value)),y=_e(()=>v.value&&a.value.length===6&&u.value);async function w(){if(f.value||!v.value)return;if(!v.value){tt({message:"请输入有效的手机号"});return}const{data:F,error:C}=await tn("auth/sendSms").post({mobile:l.value,actionType:"bindMobile"}).json();F.value&&!C.value&&(F.value.code===200?(tt({message:"获取成功"}),A(),vt(()=>{const P=document.getElementById("verificationCode");P&&P.focus()})):tt(F.value.msg))}function A(){f.value=!0,c.value=60,d=setInterval(()=>{c.value>0?c.value--:(clearInterval(d),f.value=!1)},1e3)}async function S(){if(!v.value){tt({message:"请输入有效的手机号"});return}if(a.value.length!==6){tt({message:"请输入有效的验证码"});return}if(!u.value){tt({message:"请先同意用户协议"});return}const{data:F,error:C}=await tn("/user/bindMobile").post({mobile:l.value,code:a.value}).json();F.value&&!C.value&&(F.value.code===200?(tt({message:"绑定成功"}),localStorage.setItem("token",F.value.data.accessToken),localStorage.setItem("refreshAfter",F.value.data.refreshAfter),localStorage.setItem("accessExpire",F.value.data.accessExpire),E(),s.fetchAgentStatus(),i.fetchUserInfo(),n("bind-success")):tt(F.value.msg))}function E(){r.closeBindPhone(),l.value="",a.value="",u.value=!1,d&&clearInterval(d)}function k(){E(),o.push("/userAgreement")}function M(){E(),o.push("/privacyPolicy")}return(F,C)=>{const P=Cn,U=ws;return Re(r).showBindPhone?(Ye(),It("div",j0,[L(U,{show:Re(r).showBindPhone,"onUpdate:show":C[7]||(C[7]=Y=>Re(r).showBindPhone=Y),round:"",position:"bottom",style:{height:"80%"},onClose:E},{default:_n(()=>[V("div",H0,[V("div",U0,[C[8]||(C[8]=V("div",{class:"font-bold"},"绑定手机号码",-1)),C[9]||(C[9]=V("div",{class:"text-sm text-gray-500 mt-1"}," 为使用完整功能请绑定手机号码 ",-1)),C[10]||(C[10]=V("div",{class:"text-sm text-gray-500 mt-1"}," 如该微信号之前已绑定过手机号，请输入已绑定的手机号 ",-1)),L(P,{name:"cross",class:"close-icon",onClick:E})]),V("div",z0,[C[13]||(C[13]=V("div",{class:"mb-8 pt-8 text-left"},[V("div",{class:"flex flex-col items-center"},[V("img",{class:"h-16 w-16 rounded-full shadow",src:xs,alt:"Logo"}),V("div",{class:"text-3xl mt-4 text-slate-700 font-bold"}," 天远数据 ")])],-1)),V("div",q0,[V("div",{class:Pt(["input-container bg-blue-300/20",m.value?"focused":""])},[Bn(V("input",{"onUpdate:modelValue":C[0]||(C[0]=Y=>l.value=Y),class:"input-field",type:"tel",placeholder:"请输入手机号",maxlength:"11",onFocus:C[1]||(C[1]=Y=>m.value=!0),onBlur:C[2]||(C[2]=Y=>m.value=!1)},null,544),[[gi,l.value]])],2),V("div",K0,[V("div",{class:Pt(["input-container bg-blue-300/20",p.value?"focused":""])},[Bn(V("input",{"onUpdate:modelValue":C[3]||(C[3]=Y=>a.value=Y),id:"verificationCode",class:"input-field",placeholder:"请输入验证码",maxlength:"6",onFocus:C[4]||(C[4]=Y=>p.value=!0),onBlur:C[5]||(C[5]=Y=>p.value=!1)},null,544),[[gi,a.value]])],2),V("button",{class:Pt(["ml-2 px-4 py-2 text-sm font-bold flex-shrink-0 rounded-lg transition duration-300",f.value||!v.value?"cursor-not-allowed bg-gray-300 text-gray-500":"bg-blue-500 text-white hover:bg-blue-600"]),onClick:w},zo(f.value?`${c.value}s重新获取`:"获取验证码"),3)]),V("div",W0,[Bn(V("input",{type:"checkbox","onUpdate:modelValue":C[6]||(C[6]=Y=>u.value=Y),class:"mt-1"},null,512),[[ed,u.value]]),V("span",{class:"text-xs text-gray-400 leading-tight"},[C[11]||(C[11]=Qn(" 绑定手机号即代表您已阅读并同意 ")),V("a",{class:"cursor-pointer text-blue-400",onClick:k}," 《用户协议》 "),C[12]||(C[12]=Qn(" 和 ")),V("a",{class:"cursor-pointer text-blue-400",onClick:M}," 《隐私政策》 ")])])]),V("button",{class:Pt(["mt-10 w-full py-3 text-lg font-bold text-white bg-blue-500 rounded-full transition duration-300",{"opacity-50 cursor-not-allowed":!y.value}]),onClick:S}," 确认绑定 ",2)])])]),_:1},8,["show"])])):yf("",!0)}}},Y0=Ps(G0,[["__scopeId","data-v-110ee686"]]),J0={__name:"App",setup(e){const{isWeChat:t}=Lc(),n=co(),o=rr();Es(),at(()=>{r(),i(),localStorage.getItem("token")&&(n.fetchAgentStatus(),o.fetchUserInfo())});const r=()=>{const c="2.1",u=localStorage.getItem("tokenVersion");(!u||u!==c)&&(s(),localStorage.setItem("tokenVersion",c),console.log("Token version updated, cleared old authentication data"))},s=()=>{localStorage.removeItem("token"),localStorage.removeItem("refreshAfter"),localStorage.removeItem("accessExpire"),localStorage.removeItem("userInfo"),localStorage.removeItem("agentInfo")},i=async()=>{if(t.value){a();return}const c=localStorage.getItem("token"),u=localStorage.getItem("refreshAfter"),d=localStorage.getItem("accessExpire"),m=new Date().getTime();if(d){const p=parseInt(d)*1e3;if(m>p){t.value&&a();return}}if(!c){t.value&&a();return}if(u){const p=parseInt(u)*1e3;if(m<p)return}l()},l=async()=>{const{data:c,error:u}=await tn("/user/getToken").post().json();c.value&&!u.value&&c.value.code===200&&(localStorage.setItem("token",c.value.data.accessToken),localStorage.setItem("refreshAfter",c.value.data.refreshAfter),localStorage.setItem("accessExpire",c.value.data.accessExpire))},a=async()=>{const c=new URL(window.location.href),u=new URLSearchParams(c.search),d=u.get("code"),m=u.get("state");if(d&&m){const{data:p,error:v}=await tn("/user/wxh5Auth").post({code:d}).json();if(p.value&&!v.value&&p.value.code===200){localStorage.setItem("token",p.value.data.accessToken),localStorage.setItem("refreshAfter",p.value.data.refreshAfter),localStorage.setItem("accessExpire",p.value.data.accessExpire),u.delete("code"),u.delete("state");const y=`${c.origin}${c.pathname}?${u.toString()}`;window.history.replaceState({},"",y),n.fetchAgentStatus(),o.fetchUserInfo()}}else f()},f=()=>{const c=window.location.href;let v=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxa581992dc74d860e&redirect_uri=${encodeURIComponent(c)}&response_type=code&scope=snsapi_base&state=snsapi_base#wechat_redirect`;window.location.href=v};return(c,u)=>{const d=Y0;return Ye(),It(Ee,null,[L(Re(vc)),L(d)],64)}}},Os=Oa(J0);Os.use(ud());Os.use(to);Os.mount("#app");document.addEventListener("DOMContentLoaded",()=>{const e=document.getElementById("app-loading");e&&(e.style.opacity="0",setTimeout(()=>{e.parentNode.removeChild(e)},500))});export{Xa as $,_n as A,ct as B,Qe as C,ot as D,Ve as E,Ee as F,bt as G,qr as H,Cn as I,As as J,tm as K,gu as L,om as M,be as N,he as O,tt as P,yh as Q,ws as R,_f as S,Ca as T,Ae as U,nt as V,ed as W,io as X,vt as Y,f0 as Z,Ps as _,V as a,am as a$,te as a0,gt as a1,yt as a2,Ed as a3,Ga as a4,mm as a5,yd as a6,en as a7,um as a8,hm as a9,ja as aA,Ha as aB,cm as aC,Ad as aD,us as aE,Qd as aF,kd as aG,gm as aH,qa as aI,Zd as aJ,Qa as aK,nm as aL,dm as aM,mn as aN,Ta as aO,Q0 as aP,Ho as aQ,kt as aR,pm as aS,wm as aT,lm as aU,Cm as aV,Va as aW,ao as aX,vd as aY,Ur as aZ,Am as a_,wh as aa,Ba as ab,wd as ac,Rd as ad,ys as ae,Lu as af,Ma as ag,_m as ah,xs as ai,Wh as aj,Ya as ak,Mo as al,Ja as am,jt as an,ke as ao,Di as ap,Jd as aq,fm as ar,xi as as,Ci as at,Du as au,_s as av,Wo as aw,$a as ax,ym as ay,bs as az,ta as b,xm as b0,Id as b1,vm as b2,Wa as b3,Za as b4,Pm as b5,sm as b6,gd as b7,Sm as b8,Em as b9,Fm as bA,Nm as bB,Vm as bC,jm as bD,$h as ba,Mh as bb,Z0 as bc,wc as bd,X0 as be,Im as bf,m0 as bg,g0 as bh,bm as bi,im as bj,Y0 as bk,Yo as bl,em as bm,ec as bn,Md as bo,yo as bp,gs as bq,Ll as br,Om as bs,Rm as bt,km as bu,Dm as bv,Lm as bw,Mm as bx,$m as by,Bm as bz,It as c,Ye as d,_e as e,at as f,L as g,Qn as h,tn as i,or as j,rr as k,Es as l,ku as m,Pt as n,fs as o,Re as p,yf as q,G as r,Hr as s,zo as t,co as u,gi as v,Bn as w,rm as x,Ro as y,Lc as z};
