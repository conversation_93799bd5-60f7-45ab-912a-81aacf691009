{"version": 3, "sources": ["../../.pnpm/@vant+touch-emulator@1.4.0/node_modules/@vant/touch-emulator/dist/index.mjs"], "sourcesContent": ["/* eslint-disable */\n/**\n * Emulate touch event\n * Source：https://github.com/hammerjs/touchemulator\n */\n\n(function () {\n  if (typeof window === 'undefined') {\n    return;\n  }\n  var eventTarget;\n  var supportTouch = 'ontouchstart' in window;\n\n  // polyfills\n  if (!document.createTouch) {\n    document.createTouch = function (\n      view,\n      target,\n      identifier,\n      pageX,\n      pageY,\n      screenX,\n      screenY\n    ) {\n      // auto set\n      return new Touch(\n        target,\n        identifier,\n        {\n          pageX: pageX,\n          pageY: pageY,\n          screenX: screenX,\n          screenY: screenY,\n          clientX: pageX - window.pageXOffset,\n          clientY: pageY - window.pageYOffset,\n        },\n        0,\n        0\n      );\n    };\n  }\n\n  if (!document.createTouchList) {\n    document.createTouchList = function () {\n      var touchList = TouchList();\n      for (var i = 0; i < arguments.length; i++) {\n        touchList[i] = arguments[i];\n      }\n      touchList.length = arguments.length;\n      return touchList;\n    };\n  }\n\n  if (!Element.prototype.matches) {\n    Element.prototype.matches =\n      Element.prototype.msMatchesSelector ||\n      Element.prototype.webkitMatchesSelector;\n  }\n\n  if (!Element.prototype.closest) {\n    Element.prototype.closest = function (s) {\n      var el = this;\n\n      do {\n        if (el.matches(s)) return el;\n        el = el.parentElement || el.parentNode;\n      } while (el !== null && el.nodeType === 1);\n\n      return null;\n    };\n  }\n\n  /**\n   * create an touch point\n   * @constructor\n   * @param target\n   * @param identifier\n   * @param pos\n   * @param deltaX\n   * @param deltaY\n   * @returns {Object} touchPoint\n   */\n\n  var Touch = function Touch(target, identifier, pos, deltaX, deltaY) {\n    deltaX = deltaX || 0;\n    deltaY = deltaY || 0;\n\n    this.identifier = identifier;\n    this.target = target;\n    this.clientX = pos.clientX + deltaX;\n    this.clientY = pos.clientY + deltaY;\n    this.screenX = pos.screenX + deltaX;\n    this.screenY = pos.screenY + deltaY;\n    this.pageX = pos.pageX + deltaX;\n    this.pageY = pos.pageY + deltaY;\n  };\n\n  /**\n   * create empty touchlist with the methods\n   * @constructor\n   * @returns touchList\n   */\n  function TouchList() {\n    var touchList = [];\n\n    touchList['item'] = function (index) {\n      return this[index] || null;\n    };\n\n    // specified by Mozilla\n    touchList['identifiedTouch'] = function (id) {\n      return this[id + 1] || null;\n    };\n\n    return touchList;\n  }\n\n  /**\n   * only trigger touches when the left mousebutton has been pressed\n   * @param touchType\n   * @returns {Function}\n   */\n\n  var initiated = false;\n  function onMouse(touchType) {\n    return function (ev) {\n      // prevent mouse events\n\n      if (ev.type === 'mousedown') {\n        initiated = true;\n      }\n\n      if (ev.type === 'mouseup') {\n        initiated = false;\n      }\n\n      if (ev.type === 'mousemove' && !initiated) {\n        return;\n      }\n\n      // The EventTarget on which the touch point started when it was first placed on the surface,\n      // even if the touch point has since moved outside the interactive area of that element.\n      // also, when the target doesnt exist anymore, we update it\n      if (\n        ev.type === 'mousedown' ||\n        !eventTarget ||\n        (eventTarget && !eventTarget.dispatchEvent)\n      ) {\n        eventTarget = ev.target;\n      }\n\n      if (eventTarget.closest('[data-no-touch-simulate]') == null) {\n        triggerTouch(touchType, ev);\n      }\n\n      // reset\n      if (ev.type === 'mouseup') {\n        eventTarget = null;\n      }\n    };\n  }\n\n  /**\n   * trigger a touch event\n   * @param eventName\n   * @param mouseEv\n   */\n  function triggerTouch(eventName, mouseEv) {\n    var touchEvent = document.createEvent('Event');\n    touchEvent.initEvent(eventName, true, true);\n\n    touchEvent.altKey = mouseEv.altKey;\n    touchEvent.ctrlKey = mouseEv.ctrlKey;\n    touchEvent.metaKey = mouseEv.metaKey;\n    touchEvent.shiftKey = mouseEv.shiftKey;\n\n    touchEvent.touches = getActiveTouches(mouseEv);\n    touchEvent.targetTouches = getActiveTouches(mouseEv);\n    touchEvent.changedTouches = createTouchList(mouseEv);\n\n    eventTarget.dispatchEvent(touchEvent);\n  }\n\n  /**\n   * create a touchList based on the mouse event\n   * @param mouseEv\n   * @returns {TouchList}\n   */\n  function createTouchList(mouseEv) {\n    var touchList = TouchList();\n    touchList.push(new Touch(eventTarget, 1, mouseEv, 0, 0));\n    return touchList;\n  }\n\n  /**\n   * receive all active touches\n   * @param mouseEv\n   * @returns {TouchList}\n   */\n  function getActiveTouches(mouseEv) {\n    // empty list\n    if (mouseEv.type === 'mouseup') {\n      return TouchList();\n    }\n    return createTouchList(mouseEv);\n  }\n\n  /**\n   * TouchEmulator initializer\n   */\n  function TouchEmulator() {\n    window.addEventListener('mousedown', onMouse('touchstart'), true);\n    window.addEventListener('mousemove', onMouse('touchmove'), true);\n    window.addEventListener('mouseup', onMouse('touchend'), true);\n  }\n\n  // start distance when entering the multitouch mode\n  TouchEmulator['multiTouchOffset'] = 75;\n\n  if (!supportTouch) {\n    new TouchEmulator();\n  }\n})();\n"], "mappings": ";CAMC,WAAY;AACX,MAAI,OAAO,WAAW,aAAa;AACjC;AAAA,EACF;AACA,MAAI;AACJ,MAAI,eAAe,kBAAkB;AAGrC,MAAI,CAAC,SAAS,aAAa;AACzB,aAAS,cAAc,SACrB,MACA,QACA,YACA,OACA,OACA,SACA,SACA;AAEA,aAAO,IAAI;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,QAAQ,OAAO;AAAA,UACxB,SAAS,QAAQ,OAAO;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,SAAS,iBAAiB;AAC7B,aAAS,kBAAkB,WAAY;AACrC,UAAI,YAAY,UAAU;AAC1B,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,kBAAU,CAAC,IAAI,UAAU,CAAC;AAAA,MAC5B;AACA,gBAAU,SAAS,UAAU;AAC7B,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,CAAC,QAAQ,UAAU,SAAS;AAC9B,YAAQ,UAAU,UAChB,QAAQ,UAAU,qBAClB,QAAQ,UAAU;AAAA,EACtB;AAEA,MAAI,CAAC,QAAQ,UAAU,SAAS;AAC9B,YAAQ,UAAU,UAAU,SAAU,GAAG;AACvC,UAAI,KAAK;AAET,SAAG;AACD,YAAI,GAAG,QAAQ,CAAC,EAAG,QAAO;AAC1B,aAAK,GAAG,iBAAiB,GAAG;AAAA,MAC9B,SAAS,OAAO,QAAQ,GAAG,aAAa;AAExC,aAAO;AAAA,IACT;AAAA,EACF;AAaA,MAAI,QAAQ,SAASA,OAAM,QAAQ,YAAY,KAAK,QAAQ,QAAQ;AAClE,aAAS,UAAU;AACnB,aAAS,UAAU;AAEnB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,UAAU,IAAI,UAAU;AAC7B,SAAK,UAAU,IAAI,UAAU;AAC7B,SAAK,UAAU,IAAI,UAAU;AAC7B,SAAK,UAAU,IAAI,UAAU;AAC7B,SAAK,QAAQ,IAAI,QAAQ;AACzB,SAAK,QAAQ,IAAI,QAAQ;AAAA,EAC3B;AAOA,WAAS,YAAY;AACnB,QAAI,YAAY,CAAC;AAEjB,cAAU,MAAM,IAAI,SAAU,OAAO;AACnC,aAAO,KAAK,KAAK,KAAK;AAAA,IACxB;AAGA,cAAU,iBAAiB,IAAI,SAAU,IAAI;AAC3C,aAAO,KAAK,KAAK,CAAC,KAAK;AAAA,IACzB;AAEA,WAAO;AAAA,EACT;AAQA,MAAI,YAAY;AAChB,WAAS,QAAQ,WAAW;AAC1B,WAAO,SAAU,IAAI;AAGnB,UAAI,GAAG,SAAS,aAAa;AAC3B,oBAAY;AAAA,MACd;AAEA,UAAI,GAAG,SAAS,WAAW;AACzB,oBAAY;AAAA,MACd;AAEA,UAAI,GAAG,SAAS,eAAe,CAAC,WAAW;AACzC;AAAA,MACF;AAKA,UACE,GAAG,SAAS,eACZ,CAAC,eACA,eAAe,CAAC,YAAY,eAC7B;AACA,sBAAc,GAAG;AAAA,MACnB;AAEA,UAAI,YAAY,QAAQ,0BAA0B,KAAK,MAAM;AAC3D,qBAAa,WAAW,EAAE;AAAA,MAC5B;AAGA,UAAI,GAAG,SAAS,WAAW;AACzB,sBAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAOA,WAAS,aAAa,WAAW,SAAS;AACxC,QAAI,aAAa,SAAS,YAAY,OAAO;AAC7C,eAAW,UAAU,WAAW,MAAM,IAAI;AAE1C,eAAW,SAAS,QAAQ;AAC5B,eAAW,UAAU,QAAQ;AAC7B,eAAW,UAAU,QAAQ;AAC7B,eAAW,WAAW,QAAQ;AAE9B,eAAW,UAAU,iBAAiB,OAAO;AAC7C,eAAW,gBAAgB,iBAAiB,OAAO;AACnD,eAAW,iBAAiB,gBAAgB,OAAO;AAEnD,gBAAY,cAAc,UAAU;AAAA,EACtC;AAOA,WAAS,gBAAgB,SAAS;AAChC,QAAI,YAAY,UAAU;AAC1B,cAAU,KAAK,IAAI,MAAM,aAAa,GAAG,SAAS,GAAG,CAAC,CAAC;AACvD,WAAO;AAAA,EACT;AAOA,WAAS,iBAAiB,SAAS;AAEjC,QAAI,QAAQ,SAAS,WAAW;AAC9B,aAAO,UAAU;AAAA,IACnB;AACA,WAAO,gBAAgB,OAAO;AAAA,EAChC;AAKA,WAAS,gBAAgB;AACvB,WAAO,iBAAiB,aAAa,QAAQ,YAAY,GAAG,IAAI;AAChE,WAAO,iBAAiB,aAAa,QAAQ,WAAW,GAAG,IAAI;AAC/D,WAAO,iBAAiB,WAAW,QAAQ,UAAU,GAAG,IAAI;AAAA,EAC9D;AAGA,gBAAc,kBAAkB,IAAI;AAEpC,MAAI,CAAC,cAAc;AACjB,QAAI,cAAc;AAAA,EACpB;AACF,GAAG;", "names": ["Touch"]}