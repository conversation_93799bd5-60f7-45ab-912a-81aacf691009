import{_ as Q,r as w,e as H,f as J,d as r,c as d,a as l,n as _,t as a,S as P,F as m,b as f,h as U,q as $,x as W,aQ as O}from"./index-B4d74vWj.js";const X={class:"card"},Y={class:"flex flex-col"},Z={class:"mb-5"},D={class:"flex items-center justify-between"},K={class:"flex items-center"},S={class:"text-xl font-bold"},ee={class:"text-sm text-gray-600 mt-1"},le={key:1,class:"mt-3 p-4 rounded-lg shadow-md border-l-4 border-green-500 bg-gradient-to-br from-white to-green-50 transform transition-all duration-300"},te={class:"grid grid-cols-4 w-full border-b mb-5"},ne=["onClick"],ae={class:"mt-2"},se={key:0,class:"space-y-6"},oe={class:"grid grid-cols-1 gap-4"},_e=["onClick"],re={class:"flex justify-between items-start"},de={class:"mt-3 flex items-end justify-between"},ie=["onClick"],be={class:""},ce={class:"space-y-3"},ge={class:"flex justify-between items-center"},ue={class:"text-sm font-medium text-gray-900"},ve={class:"flex items-center space-x-1"},xe={class:"text-xs text-gray-500"},me={class:"w-full h-2 bg-gray-100 rounded-full mt-2 overflow-hidden"},fe={class:""},ke={class:"grid grid-cols-2 gap-3"},he={class:"flex items-center mb-2"},pe={class:"flex flex-col"},we={class:"text-xs font-medium text-gray-900 truncate max-w-[100px]"},ye={class:"flex items-center mt-1 text-xs text-gray-500"},Te={class:"w-full h-1.5 bg-gray-100 rounded-full"},Ce={key:1},Le={class:"mb-3"},je={class:"text-xs text-gray-600 mt-1"},$e={class:"mb-4"},Re={class:"text-base font-medium text-gray-700 mb-2"},Fe={class:"space-y-2"},Ie={class:"flex justify-between items-start mb-2"},Oe={class:"text-sm font-medium text-gray-900"},Be={class:"grid grid-cols-2 gap-2 text-xs"},Ee={key:0,class:"p-3 text-center text-sm text-gray-500 bg-gray-50 rounded-lg border border-gray-200"},Me={class:"text-base font-medium text-gray-700 mb-2"},Ne={class:"space-y-2"},Ve={class:"flex justify-between items-start mb-2"},qe={class:"text-sm font-medium text-gray-900"},ze={class:"grid grid-cols-2 gap-2 text-xs"},Ae={key:0,class:"p-3 text-center text-sm text-gray-500 bg-gray-50 rounded-lg border border-gray-200"},Ge={__name:"CSpecialList",props:{data:{type:Object,required:!0}},setup(B){const y=B,E={swift_number:"999333_20181029143459_23453A4E0",code:"00",flag_specialList_c:"1",sl_id_court_bad:"",sl_id_court_executed:"",sl_id_bank_bad:"",sl_id_bank_overdue:"",sl_id_bank_lost:"",sl_id_nbank_bad:"",sl_id_nbank_overdue:"",sl_id_nbank_lost:"",sl_id_nbank_nsloan_bad:"",sl_id_nbank_nsloan_overdue:"",sl_id_nbank_nsloan_lost:"",sl_id_nbank_sloan_bad:"",sl_id_nbank_sloan_overdue:"",sl_id_nbank_sloan_lost:"",sl_id_nbank_cons_bad:"",sl_id_nbank_cons_overdue:"",sl_id_nbank_cons_lost:"",sl_id_nbank_finlea_bad:"",sl_id_nbank_finlea_overdue:"",sl_id_nbank_finlea_lost:"",sl_id_nbank_autofin_bad:"",sl_id_nbank_autofin_overdue:"",sl_id_nbank_autofin_lost:"",sl_id_nbank_other_bad:"",sl_id_nbank_other_overdue:"",sl_id_nbank_other_lost:"",sl_id_court_bad_time:"0",sl_id_court_executed_time:"0",sl_id_bank_bad_time:"0",sl_id_bank_overdue_time:"0",sl_id_bank_lost_time:"0",sl_id_nbank_bad_time:"0",sl_id_nbank_overdue_time:"0",sl_id_nbank_lost_time:"0",sl_id_nbank_nsloan_bad_time:"0",sl_id_nbank_nsloan_overdue_time:"0",sl_id_nbank_nsloan_lost_time:"0",sl_id_nbank_sloan_bad_time:"0",sl_id_nbank_sloan_overdue_time:"0",sl_id_nbank_sloan_lost_time:"0",sl_id_nbank_cons_bad_time:"0",sl_id_nbank_cons_overdue_time:"0",sl_id_nbank_cons_lost_time:"0",sl_id_nbank_finlea_bad_time:"0",sl_id_nbank_finlea_overdue_time:"0",sl_id_nbank_finlea_lost_time:"0",sl_id_nbank_autofin_bad_time:"0",sl_id_nbank_autofin_overdue_time:"0",sl_id_nbank_autofin_lost_time:"0",sl_id_nbank_other_bad_time:"0",sl_id_nbank_other_overdue_time:"0",sl_id_nbank_other_lost_time:"0",sl_id_court_bad_allnum:"0",sl_id_court_executed_allnum:"0",sl_id_bank_bad_allnum:"0",sl_id_bank_overdue_allnum:"0",sl_id_bank_lost_allnum:"0",sl_id_nbank_bad_allnum:"0",sl_id_nbank_overdue_allnum:"0",sl_id_nbank_lost_allnum:"0",sl_id_nbank_nsloan_bad_allnum:"0",sl_id_nbank_nsloan_overdue_allnum:"0",sl_id_nbank_nsloan_lost_allnum:"0",sl_id_nbank_sloan_bad_allnum:"0",sl_id_nbank_sloan_overdue_allnum:"0",sl_id_nbank_sloan_lost_allnum:"0",sl_id_nbank_cons_bad_allnum:"0",sl_id_nbank_cons_overdue_allnum:"0",sl_id_nbank_cons_lost_allnum:"0",sl_id_nbank_finlea_bad_allnum:"0",sl_id_nbank_finlea_overdue_allnum:"0",sl_id_nbank_finlea_lost_allnum:"0",sl_id_nbank_autofin_bad_allnum:"0",sl_id_nbank_autofin_overdue_allnum:"0",sl_id_nbank_autofin_lost_allnum:"0",sl_id_nbank_other_bad_allnum:"0",sl_id_nbank_other_overdue_allnum:"0",sl_id_nbank_other_lost_allnum:"0",sl_cell_bank_bad:"",sl_cell_bank_overdue:"",sl_cell_bank_lost:"",sl_cell_nbank_bad:"",sl_cell_nbank_overdue:"",sl_cell_nbank_lost:"",sl_cell_nbank_nsloan_bad:"",sl_cell_nbank_nsloan_overdue:"",sl_cell_nbank_nsloan_lost:"",sl_cell_nbank_sloan_bad:"",sl_cell_nbank_sloan_overdue:"",sl_cell_nbank_sloan_lost:"",sl_cell_nbank_cons_bad:"",sl_cell_nbank_cons_overdue:"",sl_cell_nbank_cons_lost:"",sl_cell_nbank_finlea_bad:"",sl_cell_nbank_finlea_overdue:"",sl_cell_nbank_finlea_lost:"",sl_cell_nbank_autofin_bad:"",sl_cell_nbank_autofin_overdue:"",sl_cell_nbank_autofin_lost:"",sl_cell_nbank_other_bad:"",sl_cell_nbank_other_overdue:"",sl_cell_nbank_other_lost:"",sl_cell_bank_bad_time:"0",sl_cell_bank_overdue_time:"0",sl_cell_bank_lost_time:"0",sl_cell_nbank_bad_time:"0",sl_cell_nbank_overdue_time:"0",sl_cell_nbank_lost_time:"0",sl_cell_nbank_nsloan_bad_time:"0",sl_cell_nbank_nsloan_overdue_time:"0",sl_cell_nbank_nsloan_lost_time:"0",sl_cell_nbank_sloan_bad_time:"0",sl_cell_nbank_sloan_overdue_time:"0",sl_cell_nbank_sloan_lost_time:"0",sl_cell_nbank_cons_bad_time:"0",sl_cell_nbank_cons_overdue_time:"0",sl_cell_nbank_cons_lost_time:"0",sl_cell_nbank_finlea_bad_time:"0",sl_cell_nbank_finlea_overdue_time:"0",sl_cell_nbank_finlea_lost_time:"0",sl_cell_nbank_autofin_bad_time:"0",sl_cell_nbank_autofin_overdue_time:"0",sl_cell_nbank_autofin_lost_time:"0",sl_cell_nbank_other_bad_time:"0",sl_cell_nbank_other_overdue_time:"0",sl_cell_nbank_other_lost_time:"0",sl_cell_bank_bad_allnum:"0",sl_cell_bank_overdue_allnum:"0",sl_cell_bank_lost_allnum:"0",sl_cell_nbank_bad_allnum:"0",sl_cell_nbank_overdue_allnum:"0",sl_cell_nbank_lost_allnum:"0",sl_cell_nbank_nsloan_bad_allnum:"0",sl_cell_nbank_nsloan_overdue_allnum:"0",sl_cell_nbank_nsloan_lost_allnum:"0",sl_cell_nbank_sloan_bad_allnum:"0",sl_cell_nbank_sloan_overdue_allnum:"0",sl_cell_nbank_sloan_lost_allnum:"0",sl_cell_nbank_cons_bad_allnum:"0",sl_cell_nbank_cons_overdue_allnum:"0",sl_cell_nbank_cons_lost_allnum:"0",sl_cell_nbank_finlea_bad_allnum:"0",sl_cell_nbank_finlea_overdue_allnum:"0",sl_cell_nbank_finlea_lost_allnum:"0",sl_cell_nbank_autofin_bad_allnum:"0",sl_cell_nbank_autofin_overdue_allnum:"0",sl_cell_nbank_autofin_lost_allnum:"0",sl_cell_nbank_other_bad_allnum:"0",sl_cell_nbank_other_overdue_allnum:"0",sl_cell_nbank_other_lost_allnum:"0"},i=w("summary"),M=w(!y.data||Object.keys(y.data).length===0?E:y.data),T={idCard:{label:"身份证风险",prefix:"sl_id_"},mobile:{label:"手机号风险",prefix:"sl_cell_"}},C={court:{label:"法院失信人",levels:["bad"]},court_executed:{label:"法院被执行人",levels:[""]},bank:{label:"银行(含信用卡)",levels:["bad","overdue","lost"]},nbank:{label:"非银机构",levels:["bad","overdue","lost"]},nbank_nsloan:{label:"持牌网络小贷",levels:["bad","overdue","lost"]},nbank_sloan:{label:"持牌小贷",levels:["bad","overdue","lost"]},nbank_cons:{label:"持牌消费金融",levels:["bad","overdue","lost"]},nbank_finlea:{label:"持牌融资租赁",levels:["bad","overdue","lost"]},nbank_autofin:{label:"持牌汽车金融",levels:["bad","overdue","lost"]},nbank_other:{label:"其他",levels:["bad","overdue","lost"]}},h={"":{label:"被执行人",type:"medium",color:"orange"},overdue:{label:"",type:"low",color:"blue"},bad:{label:"",type:"medium",color:"orange"},lost:{label:"",type:"high",color:"red"}},L={low:{label:"短期逾期",color:"blue"},medium:{label:"严重逾期",color:"orange"},high:{label:"无法收回",color:"red"}},R={summary:{title:"风险汇总",description:"展示各类风险汇总信息"},low:{title:"短期逾期详情",description:"展示所有短期逾期相关记录，包括短期未按时还款（1-90天）产生罚息，征信记录暂未恶化的项目"},medium:{title:"严重逾期详情",description:'展示所有严重逾期相关记录，包括持续未还款（90-360天），征信标记"不良"，限制贷款/信用卡使用的项目'},high:{title:"无法收回详情",description:"展示所有无法收回相关记录，包括超360天未还且催收无效，带有法律诉讼、资产冻结、终身征信污点等严重后果的项目"}},p={idCard:{title:"身份证风险信息",emptyText:"暂无身份证风险记录"},mobile:{title:"手机号风险信息",emptyText:"暂无手机号风险记录"}};function N(o){const n={low:[],medium:[],high:[]};return Object.entries(T).forEach(([e,t])=>{Object.entries(C).forEach(([s,c])=>{c.levels.forEach(x=>{let u;s==="court_executed"?u=`${t.prefix}${s}`:u=`${t.prefix}${s}_${x}`;const j=u,A=`${u}_time`,G=`${u}_allnum`,I={id:`${e}_${s}_${x||"executed"}`,riskType:e,riskTypeLabel:t.label,institution:s,institutionLabel:c.label,level:x||"executed",levelLabel:s==="court_executed"?"被执行人":h[x].label,levelType:s==="court_executed"?"medium":h[x].type,levelColor:s==="court_executed"?"orange":h[x].color,value:o[j]||"",time:o[A]||"0",count:o[G]||"0",isTriggered:o[j]==="0",fieldName:j};s==="court_executed"?n.medium.push(I):n[h[x].type].push(I)})})}),n}function V(o){return{byRiskLevel:Object.keys(L).map(e=>{const t=o[e],s=t.filter(c=>c.isTriggered);return{id:e,label:L[e].label,color:L[e].color,total:t.length,triggered:s.length,percentage:t.length>0?(s.length/t.length*100).toFixed(1):0,items:s}}),byRiskType:Object.keys(T).map(e=>{const t=[...o.low,...o.medium,...o.high].filter(c=>c.riskType===e),s=t.filter(c=>c.isTriggered);return{id:e,label:T[e].label,total:t.length,triggered:s.length,percentage:t.length>0?(s.length/t.length*100).toFixed(1):0,items:s}}),byInstitution:Object.keys(C).map(e=>{const t=[...o.low,...o.medium,...o.high].filter(c=>c.institution===e),s=t.filter(c=>c.isTriggered);return{id:e,label:C[e].label,total:t.length,triggered:s.length,percentage:t.length>0?(s.length/t.length*100).toFixed(1):0,items:s}})}}const k=w({}),g=w({});function q(o){i.value=o}function z(o){i.value=o}function F(o){q(o)}const b=H(()=>{var s,c,x;if(!g.value||!g.value.byRiskLevel)return{level:"low",label:"暂无风险",color:"green"};const o=((s=g.value.byRiskLevel.find(u=>u.id==="high"))==null?void 0:s.triggered)||0,n=((c=g.value.byRiskLevel.find(u=>u.id==="medium"))==null?void 0:c.triggered)||0,e=((x=g.value.byRiskLevel.find(u=>u.id==="low"))==null?void 0:x.triggered)||0,t=o+n+e;return o>0?{level:"critical",label:"严重风险",color:"red",count:t}:n>0?{level:"warning",label:"中度风险",color:"orange",count:t}:e>0?{level:"notice",label:"轻微风险",color:"blue",count:t}:{level:"safe",label:"安全状态",color:"green",count:0}});function v(o,n){if(n===0)return"gray";const e=o/n;return e===0?"gray":e<.3?"blue":e<.6?"orange":"red"}return J(()=>{const o=N(M.value);k.value=o,g.value=V(o)}),(o,n)=>(r(),d("div",X,[l("div",Y,[l("div",Z,[l("div",D,[n[0]||(n[0]=l("div",{class:"text-lg font-bold text-gray-800"},"借贷违约失信风险查询结果",-1)),l("div",{class:_(["px-3 py-1 rounded-full text-white text-sm font-semibold shadow-sm transform transition-all duration-300 hover:scale-105",b.value.level==="critical"?"bg-gradient-to-r from-red-500 to-red-600":b.value.level==="warning"?"bg-gradient-to-r from-orange-400 to-orange-500":b.value.level==="notice"?"bg-gradient-to-r from-blue-400 to-blue-500":"bg-gradient-to-r from-green-400 to-green-500"])},a(b.value.label),3)]),b.value.count>0?(r(),d("div",{key:0,class:_(["mt-3 p-4 rounded-lg shadow-md border-l-4 transform transition-all duration-300",[b.value.level==="critical"?"border-red-500 bg-gradient-to-br from-white to-red-50":b.value.level==="warning"?"border-orange-500 bg-gradient-to-br from-white to-orange-50":"border-blue-500 bg-gradient-to-br from-white to-blue-50"]])},[l("div",K,[l("div",{class:_(["w-12 h-12 flex-shrink-0 mr-4 flex items-center justify-center rounded-full shadow-inner transform transition-all duration-300 hover:scale-110",[b.value.level==="critical"?"bg-red-100 text-red-700":b.value.level==="warning"?"bg-orange-100 text-orange-700":"bg-blue-100 text-blue-700"]])},[l("span",S,a(b.value.count),1)],2),l("div",null,[l("div",{class:_(["text-base font-medium",[b.value.level==="critical"?"text-red-700":b.value.level==="warning"?"text-orange-700":"text-blue-700"]])}," 检测到 "+a(b.value.count)+" 项风险 ",3),l("p",ee,a(b.value.level==="critical"?"存在无法收回风险，请立即处理":b.value.level==="warning"?"存在严重逾期风险，建议尽快处理":"存在短期逾期风险，请注意处理"),1)])])],2)):(r(),d("div",le,n[1]||(n[1]=[P('<div class="flex items-center" data-v-292c8a1d><div class="w-12 h-12 flex-shrink-0 mr-4 bg-green-100 flex items-center justify-center rounded-full shadow-inner transform transition-all duration-300 hover:scale-110" data-v-292c8a1d><svg class="w-7 h-7 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" data-v-292c8a1d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-v-292c8a1d></path></svg></div><div data-v-292c8a1d><div class="text-base font-medium text-green-700" data-v-292c8a1d>未检测到风险</div><p class="text-sm text-gray-600 mt-1" data-v-292c8a1d>信用状况良好，无逾期或违约记录</p></div></div>',1)])))]),l("div",te,[(r(),d(m,null,f({summary:"汇总",low:"短期逾期",medium:"严重逾期",high:"无法收回"},(e,t)=>l("button",{key:t,class:_(["px-2 py-3 text-center cursor-pointer transition-all duration-300 font-medium text-xs sm:text-sm relative border-b-2",[t==="summary"||t==="low"?i.value===t?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700":t==="medium"?i.value===t?"border-orange-500 text-orange-600":"border-transparent text-gray-500 hover:text-gray-700":i.value===t?"border-red-500 text-red-600":"border-transparent text-gray-500 hover:text-gray-700"]]),onClick:s=>z(t)},[U(a(e)+" ",1),t!=="summary"&&g.value.byRiskLevel&&g.value.byRiskLevel.find(s=>s.id===t&&s.triggered>0)?(r(),d("span",{key:0,class:_(["absolute -top-1 -right-1 inline-flex items-center justify-center w-4 h-4 text-xs font-bold leading-none text-white rounded-full shadow-sm transform transition-all duration-300 hover:scale-110",t==="low"?"bg-blue-500":t==="medium"?"bg-orange-500":"bg-red-500"])},a(g.value.byRiskLevel.find(s=>s.id===t).triggered),3)):$("",!0)],10,ne)),64))]),l("div",ae,[i.value==="summary"?(r(),d("div",se,[l("div",oe,[(r(!0),d(m,null,f(g.value.byRiskLevel,e=>(r(),d("div",{key:e.id,class:_(["p-4 rounded-lg shadow-md border-l-4 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1 cursor-pointer",e.id==="low"?"border-blue-500 bg-gradient-to-br from-white to-blue-50":e.id==="medium"?"border-orange-500 bg-gradient-to-br from-white to-orange-50":"border-red-500 bg-gradient-to-br from-white to-red-50"]),onClick:t=>F(e.id)},[l("div",re,[l("h3",{class:_(["text-base font-semibold",e.id==="low"?"text-blue-700":e.id==="medium"?"text-orange-700":"text-red-700"])},a(e.label),3),l("span",{class:_(["text-xs px-2 py-1 rounded-full font-medium shadow-sm",e.triggered>0?e.id==="low"?"bg-blue-100 text-blue-800":e.id==="medium"?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800":"bg-gray-100 text-gray-600"])},a(e.triggered>0?"已命中":"未命中"),3)]),l("div",de,[l("div",null,[n[2]||(n[2]=l("p",{class:"text-xs text-gray-600"},"命中项",-1)),l("p",{class:_(["text-xl font-bold",e.triggered>0?e.id==="low"?"text-blue-600":e.id==="medium"?"text-orange-600":"text-red-600":"text-gray-500"])},a(e.triggered)+" / "+a(e.total),3)]),l("button",{class:_(["text-xs px-3 py-1.5 rounded-full focus:outline-none shadow-sm transition-all duration-300 hover:shadow-md",e.id==="low"?"bg-blue-100 text-blue-600 hover:bg-blue-200":e.id==="medium"?"bg-orange-100 text-orange-600 hover:bg-orange-200":"bg-red-100 text-red-600 hover:bg-red-200"]),onClick:W(t=>F(e.id),["stop"])}," 查看详情 ",10,ie)])],10,_e))),128))]),l("div",be,[n[5]||(n[5]=l("h3",{class:"text-base font-semibold mb-3 text-gray-700 border-l-4 border-gray-400 pl-2"},"风险主体分布",-1)),l("div",ce,[(r(!0),d(m,null,f(g.value.byRiskType,e=>(r(),d("div",{key:e.id,class:"p-3 bg-white rounded-lg border border-gray-200 shadow-sm relative overflow-hidden transition-all duration-300 hover:shadow-md"},[l("div",ge,[l("div",ue,a(e.label),1),l("div",ve,[n[3]||(n[3]=l("span",{class:"text-xs text-gray-500"},"命中项",-1)),l("span",{class:_(["text-xs font-medium px-1.5 py-0.5 rounded-full",[e.triggered>0?v(e.triggered,e.total)==="red"?"bg-red-100 text-red-700":v(e.triggered,e.total)==="orange"?"bg-orange-100 text-orange-700":"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-500"]])},a(e.triggered),3),n[4]||(n[4]=l("span",{class:"text-xs text-gray-500"},"/",-1)),l("span",xe,a(e.total),1)])]),l("div",me,[l("div",{class:_(["h-full rounded-full transition-all duration-500",[e.triggered>0?v(e.triggered,e.total)==="red"?"bg-gradient-to-r from-red-400 to-red-500":v(e.triggered,e.total)==="orange"?"bg-gradient-to-r from-orange-400 to-orange-500":"bg-gradient-to-r from-blue-400 to-blue-500":"bg-gray-200"]]),style:O({width:`${e.triggered/Math.max(1,e.total)*100}%`})},null,6)])]))),128))])]),l("div",fe,[n[6]||(n[6]=l("h3",{class:"text-base font-semibold mb-3 text-gray-700 border-l-4 border-gray-400 pl-2"},"机构风险分布",-1)),l("div",ke,[(r(!0),d(m,null,f(g.value.byInstitution,e=>(r(),d("div",{key:e.id,class:"flex flex-col p-3 rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:shadow-md"},[l("div",he,[l("div",{class:_(["mr-2 flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full shadow-inner",[e.triggered>0?v(e.triggered,e.total)==="red"?"bg-red-100":v(e.triggered,e.total)==="orange"?"bg-orange-100":"bg-blue-100":"bg-gray-100"]])},[l("span",{class:_(["text-sm font-bold",[e.triggered>0?v(e.triggered,e.total)==="red"?"text-red-600":v(e.triggered,e.total)==="orange"?"text-orange-600":"text-blue-600":"text-gray-500"]])},a(e.triggered),3)],2),l("div",pe,[l("h4",we,a(e.label),1),l("div",ye,[l("span",null,"命中项："+a(e.triggered)+"/"+a(e.total),1)])])]),l("div",Te,[l("div",{class:_(["h-full rounded-full transition-all duration-500",[e.triggered>0?v(e.triggered,e.total)==="red"?"bg-gradient-to-r from-red-400 to-red-500":v(e.triggered,e.total)==="orange"?"bg-gradient-to-r from-orange-400 to-orange-500":"bg-gradient-to-r from-blue-400 to-blue-500":"bg-gray-200"]]),style:O({width:`${e.triggered/Math.max(1,e.total)*100}%`})},null,6)])]))),128))])])])):(r(),d("div",Ce,[l("div",Le,[l("h3",{class:_(["text-lg font-semibold",i.value==="low"?"text-blue-700":i.value==="medium"?"text-orange-700":"text-red-700"])},a(R[i.value].title),3),l("p",je,a(R[i.value].description),1)]),l("div",$e,[l("h4",Re,a(p.idCard.title),1),l("div",Fe,[(r(!0),d(m,null,f(k.value[i.value].filter(e=>e.riskType==="idCard"),e=>(r(),d("div",{key:e.id,class:_(["p-3 rounded-lg border shadow-sm",e.isTriggered?"bg-red-50 border-red-200":"bg-white border-gray-200"])},[l("div",Ie,[l("div",null,[l("div",Oe,a(e.institutionLabel)+a(e.levelLabel),1)]),l("span",{class:_(["px-2 py-1 text-xs leading-5 font-semibold rounded-full",e.isTriggered?e.levelType==="low"?"bg-blue-100 text-blue-800":e.levelType==="medium"?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"])},a(e.isTriggered?"命中":"无"),3)]),l("div",Be,[l("div",null,[n[7]||(n[7]=l("span",{class:"text-gray-500"},"发生次数:",-1)),l("span",{class:_(e.isTriggered?"text-red-600 font-medium":"text-gray-500")},a(e.count==="0"?"-":e.count),3)]),l("div",null,[n[8]||(n[8]=l("span",{class:"text-gray-500"},"最近发生:",-1)),l("span",{class:_(e.isTriggered?"text-red-600 font-medium":"text-gray-500")},a(e.time!=="0"?`近${e.time}年内`:"-"),3)])])],2))),128)),k.value[i.value].filter(e=>e.riskType==="idCard").length===0?(r(),d("div",Ee,a(p.idCard.emptyText),1)):$("",!0)])]),l("div",null,[l("h4",Me,a(p.mobile.title),1),l("div",Ne,[(r(!0),d(m,null,f(k.value[i.value].filter(e=>e.riskType==="mobile"),e=>(r(),d("div",{key:e.id,class:_(["p-3 rounded-lg border shadow-sm",e.isTriggered?"bg-red-50 border-red-200":"bg-white border-gray-200"])},[l("div",Ve,[l("div",null,[l("div",qe,a(e.institutionLabel)+a(e.levelLabel),1)]),l("span",{class:_(["px-2 py-1 text-xs leading-5 font-semibold rounded-full",e.isTriggered?e.levelType==="low"?"bg-blue-100 text-blue-800":e.levelType==="medium"?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"])},a(e.isTriggered?"命中":"无"),3)]),l("div",ze,[l("div",null,[n[9]||(n[9]=l("span",{class:"text-gray-500"},"发生次数:",-1)),l("span",{class:_(e.isTriggered?"text-red-600 font-medium":"text-gray-500")},a(e.count==="0"?"-":e.count),3)]),l("div",null,[n[10]||(n[10]=l("span",{class:"text-gray-500"},"最近发生:",-1)),l("span",{class:_(e.isTriggered?"text-red-600 font-medium":"text-gray-500")},a(e.time!=="0"?`近${e.time}年内`:"-"),3)])])],2))),128)),k.value[i.value].filter(e=>e.riskType==="mobile").length===0?(r(),d("div",Ae,a(p.mobile.emptyText),1)):$("",!0)])])]))])])]))}},He=Q(Ge,[["__scopeId","data-v-292c8a1d"]]);export{He as default};
