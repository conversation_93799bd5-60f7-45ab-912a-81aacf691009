import{B as G,C as K,r as L,a1 as j,aa as re,aD as ve,e as d,a4 as q,N as A,aA as fe,aB as de,aS as he,f as J,aE as ge,aF as we,aw as me,o as ye,ab as be,g as E,D as M,U,ac as _,ah as V,Y as Q,aM as z,ae as xe,aT as B,G as Z,a3 as Se}from"./index-B4d74vWj.js";const[ee,D]=G("swipe"),Te={loop:M,width:U,height:U,vertical:Boolean,autoplay:_(0),duration:_(500),touchable:M,lazyRender:Boolean,initialSwipe:_(0),indicatorColor:String,showIndicators:M,stopPropagation:M},te=Symbol(ee);var pe=K({name:ee,props:Te,emits:["change","dragStart","dragEnd"],setup(a,{emit:b,slots:g}){const u=L(),h=L(),t=j({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let x=!1;const r=re(),{children:w,linkChildren:s}=ve(te),i=d(()=>w.length),o=d(()=>t[a.vertical?"height":"width"]),v=d(()=>a.vertical?r.deltaY.value:r.deltaX.value),y=d(()=>t.rect?(a.vertical?t.rect.height:t.rect.width)-o.value*i.value:0),I=d(()=>o.value?Math.ceil(Math.abs(y.value)/o.value):i.value),k=d(()=>i.value*o.value),S=d(()=>(t.active+i.value)%i.value),R=d(()=>{const e=a.vertical?"vertical":"horizontal";return r.direction.value===e}),ae=d(()=>{const e={transitionDuration:`${t.swiping?0:a.duration}ms`,transform:`translate${a.vertical?"Y":"X"}(${+t.offset.toFixed(2)}px)`};if(o.value){const l=a.vertical?"height":"width",n=a.vertical?"width":"height";e[l]=`${k.value}px`,e[n]=a[n]?`${a[n]}px`:""}return e}),ie=e=>{const{active:l}=t;return e?a.loop?B(l+e,-1,i.value):B(l+e,0,I.value):l},Y=(e,l=0)=>{let n=e*o.value;a.loop||(n=Math.min(n,-y.value));let f=l-n;return a.loop||(f=B(f,y.value,0)),f},m=({pace:e=0,offset:l=0,emitChange:n})=>{if(i.value<=1)return;const{active:f}=t,c=ie(e),P=Y(c,l);if(a.loop){if(w[0]&&P!==y.value){const O=P<y.value;w[0].setOffset(O?k.value:0)}if(w[i.value-1]&&P!==0){const O=P>0;w[i.value-1].setOffset(O?-k.value:0)}}t.active=c,t.offset=P,n&&c!==f&&b("change",S.value)},$=()=>{t.swiping=!0,t.active<=-1?m({pace:i.value}):t.active>=i.value&&m({pace:-i.value})},ne=()=>{$(),r.reset(),z(()=>{t.swiping=!1,m({pace:-1,emitChange:!0})})},N=()=>{$(),r.reset(),z(()=>{t.swiping=!1,m({pace:1,emitChange:!0})})};let X;const p=()=>clearTimeout(X),C=()=>{p(),+a.autoplay>0&&i.value>1&&(X=setTimeout(()=>{N(),C()},+a.autoplay))},T=(e=+a.initialSwipe)=>{if(!u.value)return;const l=()=>{var n,f;if(!V(u)){const c={width:u.value.offsetWidth,height:u.value.offsetHeight};t.rect=c,t.width=+((n=a.width)!=null?n:c.width),t.height=+((f=a.height)!=null?f:c.height)}i.value&&(e=Math.min(i.value-1,e),e===-1&&(e=i.value-1)),t.active=e,t.swiping=!0,t.offset=Y(e),w.forEach(c=>{c.setOffset(0)}),C()};V(u)?Q().then(l):l()},H=()=>T(t.active);let W;const le=e=>{!a.touchable||e.touches.length>1||(r.start(e),x=!1,W=Date.now(),p(),$())},oe=e=>{a.touchable&&t.swiping&&(r.move(e),R.value&&(!a.loop&&(t.active===0&&v.value>0||t.active===i.value-1&&v.value<0)||(xe(e,a.stopPropagation),m({offset:v.value}),x||(b("dragStart",{index:S.value}),x=!0))))},F=()=>{if(!a.touchable||!t.swiping)return;const e=Date.now()-W,l=v.value/e;if((Math.abs(l)>.25||Math.abs(v.value)>o.value/2)&&R.value){const f=a.vertical?r.offsetY.value:r.offsetX.value;let c=0;a.loop?c=f>0?v.value>0?-1:1:0:c=-Math[v.value>0?"ceil":"floor"](v.value/o.value),m({pace:c,emitChange:!0})}else v.value&&m({pace:0});x=!1,t.swiping=!1,b("dragEnd",{index:S.value}),C()},se=(e,l={})=>{$(),r.reset(),z(()=>{let n;a.loop&&e===i.value?n=t.active===0?0:e:n=e%i.value,l.immediate?z(()=>{t.swiping=!1}):t.swiping=!1,m({pace:n-t.active,emitChange:!0})})},ce=(e,l)=>{const n=l===S.value,f=n?{backgroundColor:a.indicatorColor}:void 0;return E("i",{style:f,class:D("indicator",{active:n})},null)},ue=()=>{if(g.indicator)return g.indicator({active:S.value,total:i.value});if(a.showIndicators&&i.value>1)return E("div",{class:D("indicators",{vertical:a.vertical})},[Array(i.value).fill("").map(ce)])};return q({prev:ne,next:N,state:t,resize:H,swipeTo:se}),s({size:o,props:a,count:i,activeIndicator:S}),A(()=>a.initialSwipe,e=>T(+e)),A(i,()=>T(t.active)),A(()=>a.autoplay,C),A([fe,de,()=>a.width,()=>a.height],H),A(he(),e=>{e==="visible"?C():p()}),J(T),ge(()=>T(t.active)),we(()=>T(t.active)),me(p),ye(p),be("touchmove",oe,{target:h}),()=>{var e;return E("div",{ref:u,class:D()},[E("div",{ref:h,style:ae.value,class:D("track",{vertical:a.vertical}),onTouchstartPassive:le,onTouchend:F,onTouchcancel:F},[(e=g.default)==null?void 0:e.call(g)]),ue()])}}});const $e=Z(pe),[Ce,Pe]=G("swipe-item");var Ae=K({name:Ce,setup(a,{slots:b}){let g;const u=j({offset:0,inited:!1,mounted:!1}),{parent:h,index:t}=Se(te);if(!h)return;const x=d(()=>{const s={},{vertical:i}=h.props;return h.size.value&&(s[i?"height":"width"]=`${h.size.value}px`),u.offset&&(s.transform=`translate${i?"Y":"X"}(${u.offset}px)`),s}),r=d(()=>{const{loop:s,lazyRender:i}=h.props;if(!i||g)return!0;if(!u.mounted)return!1;const o=h.activeIndicator.value,v=h.count.value-1,y=o===0&&s?v:o-1,I=o===v&&s?0:o+1;return g=t.value===o||t.value===y||t.value===I,g}),w=s=>{u.offset=s};return J(()=>{Q(()=>{u.mounted=!0})}),q({setOffset:w}),()=>{var s;return E("div",{class:Pe(),style:x.value},[r.value?(s=b.default)==null?void 0:s.call(b):null])}}});const Me=Z(Ae);export{Me as S,$e as a};
