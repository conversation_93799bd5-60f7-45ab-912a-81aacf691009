import{_ as b,e as s,d as a,c as l,a as r,n,bm as u,F as f,b as m}from"./index-B4d74vWj.js";const y={class:"l-table overflow-x-auto"},_={class:"min-w-full border-collapse table-auto text-center text-size-xs"},v={__name:"LTable",props:{data:{type:Array,required:!0},type:{type:String,default:"purple-pink"}},setup(d){const t=d,p=s(()=>{switch(t.type){case"blue-green":return"bg-teal-100/40";case"orange-yellow":return"bg-gradient-to-r from-orange-50 via-yellow-50 to-yellow-100";case"red-purple":return"bg-gradient-to-r from-red-50 via-purple-50 to-purple-100";default:return"bg-gradient-to-r from-purple-50 via-pink-50 to-red-50"}}),c=s(()=>{switch(t.type){case"blue-green":return"bg-teal-200";case"orange-yellow":return"bg-gradient-to-r from-orange-200 via-yellow-200 to-yellow-200";case"red-purple":return"bg-gradient-to-r from-red-200 via-purple-200 to-purple-200";default:return"bg-gradient-to-r from-purple-200 via-pink-200 to-red-200"}});function i(e){return e%2===1?p.value:""}return(e,w)=>(a(),l("div",y,[r("table",_,[r("thead",{class:n(c.value)},[r("tr",null,[u(e.$slots,"header",{},void 0)])],2),r("tbody",null,[(a(!0),l(f,null,m(t.data,(g,o)=>(a(),l("tr",{key:o,class:n([i(o),"border-t"])},[u(e.$slots,"default",{row:g},void 0)],2))),128))])])]))}},k=b(v,[["__scopeId","data-v-e5692a64"]]);export{k as L};
