import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api', // 通过vite代理
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'mm_token': '68202c73cde7f97443b3a5d9.92790baf7cd59b7556d2096c7c7e79e14861691c379c6cac5bf8392f90c50054'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加token等认证信息
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

/**
 * 司法涉诉查询API
 * @param {Object} params - 查询参数
 * @param {string} params.carerId - 身份证号
 * @returns {Promise<{data: CreditReportResponse, code: string, message: string}>}
 */
export const queryCreditReport = (params) => {
  return api.get('/super/admin/carer/credit/report', { params })
}

/**
 * 基本信息查询API
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export const queryBasicInfo = (params) => {
  return api.post('/basic-info/query', params)
}

export default api
