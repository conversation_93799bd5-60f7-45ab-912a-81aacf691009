import{_ as v}from"./BaseReport-ClT0Rj5U.js";import{r as a,m as d,i as y,y as D,p as t,d as q}from"./index-B4d74vWj.js";/* empty css              *//* empty css              */import"./index-DsKI3WGL.js";import"./use-id-BHlr6Txk.js";import"./use-tab-status-Co3jcf_0.js";import"./index-Z9L7R3xo.js";const T={__name:"Example",setup(g){const r=a(""),u=a([]),n=a({}),p=a(""),l=a(null),m=a(!1),c=a(!1);d(()=>{const o=new URLSearchParams(window.location.search);r.value=o.get("feature"),r.value&&i()});const i=async()=>{let o=`/query/example?feature=${r.value}`;const{data:e,error:s}=await y(o).get().json();e.value&&!s.value&&(e.value.code===200?(u.value=e.value.data.query_data.sort((f,_)=>f.feature.sort-_.feature.sort),n.value=e.value.data.query_params,p.value=e.value.data.product_name,l.value=e.value.data.create_time):e.value.code===200003&&(m.value=!0),c.value=!0)};return(o,e)=>{const s=v;return q(),D(s,{feature:t(r),reportData:t(u),reportParams:t(n),reportName:t(p),reportDateTime:t(l),isEmpty:t(m),isDone:t(c)},null,8,["feature","reportData","reportParams","reportName","reportDateTime","isEmpty","isDone"])}}};export{T as default};
