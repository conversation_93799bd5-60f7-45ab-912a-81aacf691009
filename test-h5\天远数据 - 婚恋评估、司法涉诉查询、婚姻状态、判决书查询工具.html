<!DOCTYPE html>
<!-- saved from url=(0057)https://www.tianyuandb.com/example?feature=riskassessment -->
<html lang="" class=" "><script async="" id="beacon-aplus" src="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/203467608.js.下载"></script><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        
        <link rel="icon" href="https://www.tianyuandb.com/favicon.ico">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=3, user-scalable=no">
        <title>
            天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具
        </title>
        <meta name="description" content="天远数据提供婚恋评估报告、司法涉诉查询、婚姻状态查询、判决书查询、失信人、司法涉诉、企业涉诉、车辆核验等多项服务，帮助您查询婚姻信息、名下车辆、涉诉风险等，提供全面的法律与金融风险防范工具。">
        <meta name="keywords" content="婚恋评估, 司法涉诉查询, 判决书查询, 婚姻状态查询, 失信人, 司法涉诉查询, 企业涉诉查询, 名下车辆核验, 车辆核验, 婚姻报告, 法律风险, 信用风险, 银行卡黑名单, 手机身份证核验, 学历核验, 智能助手">
        <meta name="author" content="天远数据">
        <meta name="baidu-site-verification" content="4d551d55896a88badef8dcdb14cf874c">
        <script>
            (function (w, d, s, q, i) {
                w[q] = w[q] || [];
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s);
                j.async = true;
                j.id = "beacon-aplus";
                j.src = "https://d.alicdn.com/alilog/mlog/aplus/" + i + ".js";
                f.parentNode.insertBefore(j, f);
            })(window, document, "script", "aplus_queue", "203467608");
            aplus_queue.push({
                action: "aplus.setMetaInfo",
                arguments: ["appKey", "67aeb61b8f232a05f11365e0"],
            });
            aplus_queue.push({
                action: "aplus.setMetaInfo",
                arguments: ["aplus-waiting", "MAN"],
            });
            aplus_queue.push({
                action: "aplus.setMetaInfo",
                arguments: ["DEBUG", false],
            });
            aplus_queue.push({
                action: "aplus.setMetaInfo",
                arguments: ["aplus-idtype", "uuid"], //取值参考见附表1
            });
        </script>
        <script src="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/jweixin-1.6.0.js.下载"></script>
        <script>
            window.jWeixin = window.wx;
            delete window.wx;
        </script>
        <style>
            /* 基础样式 */
            html {
                font-size: 16px;
                -webkit-text-size-adjust: 100%;
                -moz-text-size-adjust: 100%;
                     text-size-adjust: 100%;
            }

            body {
                margin: 0;
                padding: 0;
                overflow-x: hidden;
                min-width: 320px;
            }

            /* 确保所有元素在缩放时保持相对位置 */
            * {
                box-sizing: border-box;
            }

            /* 防止水平滚动 */
            #app {
                width: 100%;
                max-width: 100vw;
                overflow-x: hidden;
            }

            /* 响应式字体大小 */
            @media screen and (max-width: 480px) {
                html {
                    font-size: 14px;
                }
            }

            @media screen and (min-width: 481px) and (max-width: 768px) {
                html {
                    font-size: 15px;
                }
            }

            /* 加载页面样式 */
            #app-loading {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                background-color: #fff;
                z-index: 9999;
                font-family: Arial, sans-serif;
                color: #666;
            }

            /* 加载动画 */
            .loading-spinner {
                width: 50px;
                height: 50px;
                border: 5px solid #ccc;
                border-top: 5px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 16px;
                /* 与文字的间距 */
            }

            /* 文字样式 */
            .loading-text {
                font-size: 16px;
                color: #666;
            }

            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }


        </style>
      <script type="module" crossorigin="" src="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/index-B4d74vWj.js.下载"></script>
      <link rel="stylesheet" crossorigin="" href="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/index-ptDfl3Jv.css">
    <link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/Example-DNOHy4qv.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/BaseReport-ClT0Rj5U.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/index-DsKI3WGL.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/use-id-BHlr6Txk.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/use-tab-status-Co3jcf_0.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/index-Z9L7R3xo.js"><link rel="stylesheet" crossorigin="" href="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/BaseReport-APQ-y0C-.css"><link rel="stylesheet" crossorigin="" href="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/index-CBO6F5n_.css"><link rel="stylesheet" crossorigin="" href="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/index-vk3zi5_R.css"><style>.jj-flash-note__popper[data-v-44225974]{position:absolute;border:none;outline:0;text-align:center;width:28px;height:28px;background:#f7f8fa;border:1px solid #e5e6eb;border-radius:2px;padding:4px}.jj-flash-note__popper .icon[data-v-44225974]{pointer-events:none}.jj-flash-note__popup.vdr-container{position:absolute;box-sizing:border-box}.jj-flash-note__popup.vdr-container .vdr-handle-tl{top:-4px;left:-4px;cursor:nwse-resize}.jj-flash-note__popup.vdr-container .vdr-handle-tm{top:-2px;left:50%;margin-left:-3px;cursor:ns-resize}.jj-flash-note__popup.vdr-container .vdr-handle-tr{top:-4px;right:-4px;cursor:nesw-resize}.jj-flash-note__popup.vdr-container .vdr-handle-ml{top:50%;margin-top:-3px;left:-2px;cursor:ew-resize}.jj-flash-note__popup.vdr-container .vdr-handle-mr{top:50%;margin-top:-3px;right:-2px;cursor:ew-resize}.jj-flash-note__popup.vdr-container .vdr-handle-bl{bottom:-4px;left:-4px;cursor:nesw-resize}.jj-flash-note__popup.vdr-container .vdr-handle-bm{bottom:-2px;left:50%;margin-left:-4px;cursor:ns-resize}.jj-flash-note__popup.vdr-container .vdr-handle-br{bottom:-4px;right:-4px;cursor:nwse-resize}.jj-flash-note__popup.vdr-container .vdr-handle{box-sizing:border-box;position:absolute;width:7px;height:7px}.jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-tl.handle-tl{top:0;left:0}.jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-tr.handle-tr{top:0;right:0}.jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-bl.handle-bl{bottom:0;left:0}.jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-br.handle-br{bottom:0;right:0}.jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-bm.handle-bm,.jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-tm.handle-tm{left:10px;right:10px;width:unset;margin-left:0}.jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-ml.handle-ml,.jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-mr.handle-mr{top:10px;bottom:10px;height:unset;margin-top:0}.jj-flash-note__popup[data-v-9af3c3e4]{position:absolute;border:1px solid #e4e6eb;filter:drop-shadow(0 2px 15px rgba(0, 0, 0, .2));border-radius:4px;overflow:hidden;z-index:9999;background-color:#fff}.jj-flash-note__frame[data-v-9af3c3e4]{border:none}.jj-flash-note__app[data-v-6ad74fae]{z-index:9999;position:fixed;left:0;top:0}.jj-flash-note__app .mask[data-v-6ad74fae]{position:fixed;left:0;right:0;top:0;bottom:0;z-index:9000;background-color:rgba(0,0,0,.4);opacity:1}.jj-flash-note__app .fade-enter-active[data-v-6ad74fae],.jj-flash-note__app .fade-leave-active[data-v-6ad74fae]{transition:opacity .15s ease}.jj-flash-note__app .fade-enter-from[data-v-6ad74fae],.jj-flash-note__app .fade-leave-to[data-v-6ad74fae]{opacity:0}[data-v-2d8f708a]:root{--jjext-color-brand:#1e80ff;--jjext-color-brand-light:#e8f3ff;--jjext-color-nav-title:#86909c;--jjext-color-nav-popup-bg:#ffffff;--jjext-color-primary:#1d2129;--jjext-color-secondary-app:#4e5969;--jjext-color-thirdly:#86909c;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1e80ff;--jjext-color-divider:#e5e6eb;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#ffffff;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#e8f3ff;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#f53f3f;--jjext-color-fourthly:#c9cdd4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4e5969;--jjext-color-layout-title-active:#1e80ff;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-4:#ffffff;--jjext-color-font-brand1-normal:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-2:#515767;--jjext-color-font-3:#8a919f;--jjext-color-font-4:#c2c8d1;--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05);--jjext-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-gray-0:#fff;--jjext-color-gray-1-1:#e4e6eb;--jjext-color-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-gray-1-3:#e4e6eb;--jjext-color-gray-2:#f2f3f5;--jjext-color-gray-3:#f7f8fa;--jjext-color-background:#f2f3f5;--jjext-color-layer-1:#fff;--jjext-color-layer-2-1:#f7f8fa;--jjext-color-layer-2-2:rgba(247, 248, 250, 0.7);--jjext-color-layer-3-fill:#f2f3f5;--jjext-color-layer-3-border:#e4e6eb;--jjext-color-layer-4-dropdown:#fff;--jjext-color-layer-5:#fff;--jjext-color-brand-1-normal:#1e80ff;--jjext-color-brand-2-hover:#1171ee;--jjext-color-brand-3-click:#0060dd;--jjext-color-brand-4-disable:#abcdff;--jjext-color-brand-5-light:#eaf2ff;--jjext-color-mask-1:rgba(0, 0, 0, 0.4);--jjext-color-mask-2:#fff;--jjext-color-mask-3:none;--jjext-color-mask-6:#ffffff;--jjext-color-brand-fill1-normal:rgba(30, 128, 255, 0.05);--jjext-color-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-brand-fill3-click:rgba(30, 128, 255, 0.2);--jjext-color-brand-stroke1-normal:rgba(30, 128, 255, 0.3);--jjext-color-brand-stroke2-hover:rgba(30, 128, 255, 0.45);--jjext-color-brand-stroke3-click:rgba(30, 128, 255, 0.6);--jjext-color-font_danger:#ff5132;--jjext-color-shade-1:rgba(0, 0, 0, 0.4);--jjext-color-popup:#fff;--jjext-color-popover:rgba(0, 0, 0, 0.8)}:root .dark[data-v-2d8f708a]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352a3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand1-normal:#4495ff;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-2:rgba(255, 255, 255, 0.7);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1);--jjext-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-gray-0:#000;--jjext-color-gray-1-1:rgba(255, 255, 255, 0.2);--jjext-color-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-gray-1-3:#464646;--jjext-color-gray-2:rgba(255, 255, 255, 0.12);--jjext-color-gray-3:rgba(255, 255, 255, 0.08);--jjext-color-background:#000;--jjext-color-layer-1:#181818;--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-2-2:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-fill:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-border:rgba(255, 255, 255, 0.2);--jjext-color-layer-4-dropdown:#2f2f2f;--jjext-color-layer-5:rgba(255, 255, 255, 0.12);--jjext-color-brand-1-normal:#2986ff;--jjext-color-brand-2-hover:#1473ed;--jjext-color-brand-3-click:#0563dd;--jjext-color-brand-4-disable:rgba(41, 134, 255, 0.4);--jjext-color-brand-5-light:rgba(30, 128, 255, 0.2);--jjext-color-mask-1:rgba(255, 255, 255, 0.4);--jjext-color-mask-2:#282828;--jjext-color-mask-3:rgba(0, 0, 0, 0.05);--jjext-color-mask-6:#181818;--jjext-color-brand-fill1-normal:rgba(41, 134, 255, 0.15);--jjext-color-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-brand-fill3-click:rgba(5, 99, 221, 0.35);--jjext-color-brand-stroke1-normal:rgba(41, 134, 255, 0.4);--jjext-color-brand-stroke2-hover:rgba(20, 115, 237, 0.6);--jjext-color-brand-stroke3-click:rgba(5, 99, 221, 0.6);--jjext-color-font_danger:#f85959;--jjext-color-shade-1:rgba(0, 0, 0, 0.6);--jjext-color-popup:#282828;--jjext-color-popover:#323232}.juejin-search[data-v-2d8f708a]{display:flex;width:682px;height:46px;border-radius:2px;flex-direction:row;align-items:center;justify-content:center;position:relative}.juejin-search .search-anim[data-v-2d8f708a]{position:absolute;left:8px;width:28px;height:28px;object-fit:contain;animation-play-state:paused}.juejin-search .search-anim.slide-right-enter-active[data-v-2d8f708a],.juejin-search .search-anim.slide-right-leave-active[data-v-2d8f708a]{transition:width .3s linear}.juejin-search .search-anim.slide-right-enter-from[data-v-2d8f708a],.juejin-search .search-anim.slide-right-leave-to[data-v-2d8f708a]{width:0}.juejin-search .juejin-search-logo[data-v-2d8f708a]{right:16px;position:absolute;width:23px;height:18px;object-fit:contain}.juejin-search .juejin-search-logo path[data-v-2d8f708a]{transition:all .3s linear}.juejin-search #juejin-search-input-global[data-v-2d8f708a]{height:100%;width:100%}.juejin-search #juejin-search-input-global .input[data-v-2d8f708a]{padding:0 39px 0 33px;width:100%;height:100%;outline:0;border:none;border-radius:2px;color:var(--jjext-color-font-1);font-size:18px;line-height:22px;font-weight:500;caret-color:transparent;box-sizing:border-box;background-color:var(--jjext-color-layer-4-plugin)}.juejin-search #juejin-search-input-global .input.active[data-v-2d8f708a]{border:2px solid var(--jjext-color-font-brand-4)}.juejin-search #juejin-search-input-global .input.animation-stopped[data-v-2d8f708a]{caret-color:#1e80ff;padding-left:16px}.juejin-search #juejin-search-input-global .input[data-v-2d8f708a]::placeholder{font-weight:400;color:#86909c}.calculator[data-v-4faf9c0e]{display:flex;align-items:center;height:36px;padding:0 16px;cursor:pointer}.calculator .result[data-v-4faf9c0e]{font-size:14px;text-align:start;font-weight:500;line-height:22px;color:#1d2129;margin:0 12px;text-overflow:ellipsis;flex:1 0 auto;overflow:hidden;white-space:nowrap;max-width:494px}.calculator .hint[data-v-4faf9c0e]{font-size:14px;line-height:22px;color:#8a919f}[data-v-ef0f272e]:root{--jjext-color-brand:#1e80ff;--jjext-color-brand-light:#e8f3ff;--jjext-color-nav-title:#86909c;--jjext-color-nav-popup-bg:#ffffff;--jjext-color-primary:#1d2129;--jjext-color-secondary-app:#4e5969;--jjext-color-thirdly:#86909c;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1e80ff;--jjext-color-divider:#e5e6eb;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#ffffff;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#e8f3ff;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#f53f3f;--jjext-color-fourthly:#c9cdd4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4e5969;--jjext-color-layout-title-active:#1e80ff;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-4:#ffffff;--jjext-color-font-brand1-normal:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-2:#515767;--jjext-color-font-3:#8a919f;--jjext-color-font-4:#c2c8d1;--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05);--jjext-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-gray-0:#fff;--jjext-color-gray-1-1:#e4e6eb;--jjext-color-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-gray-1-3:#e4e6eb;--jjext-color-gray-2:#f2f3f5;--jjext-color-gray-3:#f7f8fa;--jjext-color-background:#f2f3f5;--jjext-color-layer-1:#fff;--jjext-color-layer-2-1:#f7f8fa;--jjext-color-layer-2-2:rgba(247, 248, 250, 0.7);--jjext-color-layer-3-fill:#f2f3f5;--jjext-color-layer-3-border:#e4e6eb;--jjext-color-layer-4-dropdown:#fff;--jjext-color-layer-5:#fff;--jjext-color-brand-1-normal:#1e80ff;--jjext-color-brand-2-hover:#1171ee;--jjext-color-brand-3-click:#0060dd;--jjext-color-brand-4-disable:#abcdff;--jjext-color-brand-5-light:#eaf2ff;--jjext-color-mask-1:rgba(0, 0, 0, 0.4);--jjext-color-mask-2:#fff;--jjext-color-mask-3:none;--jjext-color-mask-6:#ffffff;--jjext-color-brand-fill1-normal:rgba(30, 128, 255, 0.05);--jjext-color-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-brand-fill3-click:rgba(30, 128, 255, 0.2);--jjext-color-brand-stroke1-normal:rgba(30, 128, 255, 0.3);--jjext-color-brand-stroke2-hover:rgba(30, 128, 255, 0.45);--jjext-color-brand-stroke3-click:rgba(30, 128, 255, 0.6);--jjext-color-font_danger:#ff5132;--jjext-color-shade-1:rgba(0, 0, 0, 0.4);--jjext-color-popup:#fff;--jjext-color-popover:rgba(0, 0, 0, 0.8)}:root .dark[data-v-ef0f272e]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352a3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand1-normal:#4495ff;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-2:rgba(255, 255, 255, 0.7);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1);--jjext-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-gray-0:#000;--jjext-color-gray-1-1:rgba(255, 255, 255, 0.2);--jjext-color-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-gray-1-3:#464646;--jjext-color-gray-2:rgba(255, 255, 255, 0.12);--jjext-color-gray-3:rgba(255, 255, 255, 0.08);--jjext-color-background:#000;--jjext-color-layer-1:#181818;--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-2-2:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-fill:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-border:rgba(255, 255, 255, 0.2);--jjext-color-layer-4-dropdown:#2f2f2f;--jjext-color-layer-5:rgba(255, 255, 255, 0.12);--jjext-color-brand-1-normal:#2986ff;--jjext-color-brand-2-hover:#1473ed;--jjext-color-brand-3-click:#0563dd;--jjext-color-brand-4-disable:rgba(41, 134, 255, 0.4);--jjext-color-brand-5-light:rgba(30, 128, 255, 0.2);--jjext-color-mask-1:rgba(255, 255, 255, 0.4);--jjext-color-mask-2:#282828;--jjext-color-mask-3:rgba(0, 0, 0, 0.05);--jjext-color-mask-6:#181818;--jjext-color-brand-fill1-normal:rgba(41, 134, 255, 0.15);--jjext-color-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-brand-fill3-click:rgba(5, 99, 221, 0.35);--jjext-color-brand-stroke1-normal:rgba(41, 134, 255, 0.4);--jjext-color-brand-stroke2-hover:rgba(20, 115, 237, 0.6);--jjext-color-brand-stroke3-click:rgba(5, 99, 221, 0.6);--jjext-color-font_danger:#f85959;--jjext-color-shade-1:rgba(0, 0, 0, 0.6);--jjext-color-popup:#282828;--jjext-color-popover:#323232}.search-action[data-v-ef0f272e]{display:flex;align-items:center;box-sizing:border-box;user-select:none;cursor:pointer;height:36px;border-left:4px solid transparent;border-top:4px solid transparent;border-bottom:4px solid transparent;transition:all .15s linear;padding:0 16px 0 12px}.search-action.active[data-v-ef0f272e]{border-left-color:var(--jjext-color-font-brand1-normal);background-color:#f4f5f5}.search-action .search-content[data-v-ef0f272e]{display:flex;align-items:center;flex:1 0 auto;margin-right:16px}.search-action .search-content .search-content__logo[data-v-ef0f272e]{width:28px;height:28px;margin:0}.search-action .search-content .search-content__engine[data-v-ef0f272e],.search-action .search-content .search-content__keyword[data-v-ef0f272e]{font-size:14px;font-weight:500;line-height:22px}.search-action .search-content .search-content__keyword[data-v-ef0f272e]{color:var(--jjext-color-font-1);margin:0 4px 0 12px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;max-width:396px}.search-action .search-content .search-content__engine[data-v-ef0f272e]{color:var(--jjext-color-font-brand1-normal)}.search-action .hint[data-v-ef0f272e]{font-size:14px;line-height:22px;color:var(--jjext-color-font-brand1-normal)}em[data-v-b1604592]{font-style:normal;color:#f53f3f}[data-v-7fa14ab5]:root{--jjext-color-brand:#1e80ff;--jjext-color-brand-light:#e8f3ff;--jjext-color-nav-title:#86909c;--jjext-color-nav-popup-bg:#ffffff;--jjext-color-primary:#1d2129;--jjext-color-secondary-app:#4e5969;--jjext-color-thirdly:#86909c;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1e80ff;--jjext-color-divider:#e5e6eb;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#ffffff;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#e8f3ff;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#f53f3f;--jjext-color-fourthly:#c9cdd4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4e5969;--jjext-color-layout-title-active:#1e80ff;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-4:#ffffff;--jjext-color-font-brand1-normal:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-2:#515767;--jjext-color-font-3:#8a919f;--jjext-color-font-4:#c2c8d1;--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05);--jjext-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-gray-0:#fff;--jjext-color-gray-1-1:#e4e6eb;--jjext-color-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-gray-1-3:#e4e6eb;--jjext-color-gray-2:#f2f3f5;--jjext-color-gray-3:#f7f8fa;--jjext-color-background:#f2f3f5;--jjext-color-layer-1:#fff;--jjext-color-layer-2-1:#f7f8fa;--jjext-color-layer-2-2:rgba(247, 248, 250, 0.7);--jjext-color-layer-3-fill:#f2f3f5;--jjext-color-layer-3-border:#e4e6eb;--jjext-color-layer-4-dropdown:#fff;--jjext-color-layer-5:#fff;--jjext-color-brand-1-normal:#1e80ff;--jjext-color-brand-2-hover:#1171ee;--jjext-color-brand-3-click:#0060dd;--jjext-color-brand-4-disable:#abcdff;--jjext-color-brand-5-light:#eaf2ff;--jjext-color-mask-1:rgba(0, 0, 0, 0.4);--jjext-color-mask-2:#fff;--jjext-color-mask-3:none;--jjext-color-mask-6:#ffffff;--jjext-color-brand-fill1-normal:rgba(30, 128, 255, 0.05);--jjext-color-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-brand-fill3-click:rgba(30, 128, 255, 0.2);--jjext-color-brand-stroke1-normal:rgba(30, 128, 255, 0.3);--jjext-color-brand-stroke2-hover:rgba(30, 128, 255, 0.45);--jjext-color-brand-stroke3-click:rgba(30, 128, 255, 0.6);--jjext-color-font_danger:#ff5132;--jjext-color-shade-1:rgba(0, 0, 0, 0.4);--jjext-color-popup:#fff;--jjext-color-popover:rgba(0, 0, 0, 0.8)}:root .dark[data-v-7fa14ab5]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352a3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand1-normal:#4495ff;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-2:rgba(255, 255, 255, 0.7);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1);--jjext-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-gray-0:#000;--jjext-color-gray-1-1:rgba(255, 255, 255, 0.2);--jjext-color-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-gray-1-3:#464646;--jjext-color-gray-2:rgba(255, 255, 255, 0.12);--jjext-color-gray-3:rgba(255, 255, 255, 0.08);--jjext-color-background:#000;--jjext-color-layer-1:#181818;--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-2-2:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-fill:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-border:rgba(255, 255, 255, 0.2);--jjext-color-layer-4-dropdown:#2f2f2f;--jjext-color-layer-5:rgba(255, 255, 255, 0.12);--jjext-color-brand-1-normal:#2986ff;--jjext-color-brand-2-hover:#1473ed;--jjext-color-brand-3-click:#0563dd;--jjext-color-brand-4-disable:rgba(41, 134, 255, 0.4);--jjext-color-brand-5-light:rgba(30, 128, 255, 0.2);--jjext-color-mask-1:rgba(255, 255, 255, 0.4);--jjext-color-mask-2:#282828;--jjext-color-mask-3:rgba(0, 0, 0, 0.05);--jjext-color-mask-6:#181818;--jjext-color-brand-fill1-normal:rgba(41, 134, 255, 0.15);--jjext-color-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-brand-fill3-click:rgba(5, 99, 221, 0.35);--jjext-color-brand-stroke1-normal:rgba(41, 134, 255, 0.4);--jjext-color-brand-stroke2-hover:rgba(20, 115, 237, 0.6);--jjext-color-brand-stroke3-click:rgba(5, 99, 221, 0.6);--jjext-color-font_danger:#f85959;--jjext-color-shade-1:rgba(0, 0, 0, 0.6);--jjext-color-popup:#282828;--jjext-color-popover:#323232}.search-suggest[data-v-7fa14ab5]{background:var(--jjext-color-layer-4);padding:0 4px 8px 4px}.search-suggest .calculator.active[data-v-7fa14ab5],.search-suggest .search-action.active[data-v-7fa14ab5]{background:var(--jjext-color-layer-2-1)}.search-suggest .calculator[data-v-7fa14ab5]{transition:background-color .15s linear}.search-suggest .list[data-v-7fa14ab5]{display:flex;border-top:1px solid var(--jjext-color-layer-gray-1-2);flex-direction:column;padding-top:4px}.search-suggest .list .item[data-v-7fa14ab5]{display:flex;flex-direction:row;align-items:center;height:36px;cursor:pointer}.search-suggest .list .item .content[data-v-7fa14ab5]{color:var(--jjext-color-font-1);font-size:14px}.search-suggest .list .item.active[data-v-7fa14ab5],.search-suggest .list .item[data-v-7fa14ab5]:hover{background:var(--jjext-color-layer-2-1)}.search-suggest .list .tool-item[data-v-7fa14ab5]{position:relative;padding:0 9px 0 4px}.search-suggest .list .tool-item .tool-icon[data-v-7fa14ab5]{width:24px;height:24px;background-size:100% 100%;background-position:0 0;background-repeat:no-repeat}.search-suggest .list .tool-item .content[data-v-7fa14ab5]{margin-left:8px}.search-suggest .list .tool-item .icon-tool-arrow[data-v-7fa14ab5]{opacity:0;transition:all .15s linear;position:absolute;stroke:var(--jjext-color-font-brand1-normal);top:50%;transform:translateY(-50%);right:9px}.search-suggest .list .tool-item.active .icon-tool-arrow[data-v-7fa14ab5],.search-suggest .list .tool-item:hover .icon-tool-arrow[data-v-7fa14ab5]{opacity:1}.search-suggest .list .suggest-item[data-v-7fa14ab5]{padding:0 7px;transition:background-color .15s linear}.search-suggest .list .suggest-item .icon-search[data-v-7fa14ab5]{stroke:var(--jjext-color-font-4)}.search-suggest .list .suggest-item .content[data-v-7fa14ab5]{margin:0 0 0 12px}.search-suggest .list .suggest-item[data-v-7fa14ab5] .highlight-keyword{color:var(--jjext-color-font-3)}.search-suggest .setting-hint[data-v-7fa14ab5]{display:flex;align-items:center;justify-content:flex-end;margin:8px 16px 0 16px}.search-suggest .setting-hint .text[data-v-7fa14ab5]{color:#8a919f;line-height:22px;cursor:pointer;user-select:none}.search-suggest .setting-hint .text[data-v-7fa14ab5]:hover:not(.disabled){color:#1e80ff;transition:all .15s linear}.search-suggest .setting-hint .text.disabled[data-v-7fa14ab5]{cursor:initial}:root{--jjext-color-input-bg:#f4f5f5;--jjext-color-input-error-bg:#ffece8;--jjext-color-input-placeholder:#86909c;--jjext-color-input-text:#4e5969;--jjext-color-input-icon:#f53f3f}:root .dark{--jjext-color-input-bg:rgba(255, 255, 255, 0.12);--jjext-color-input-error-bg:rgba(255, 81, 50, 0.15);--jjext-color-input-placeholder:#e3e3e3;--jjext-color-input-text:#e3e3e3;--jjext-color-input-icon:#ff6247}[data-v-341e7439]:root{--jjext-color-brand:#1e80ff;--jjext-color-brand-light:#e8f3ff;--jjext-color-nav-title:#86909c;--jjext-color-nav-popup-bg:#ffffff;--jjext-color-primary:#1d2129;--jjext-color-secondary-app:#4e5969;--jjext-color-thirdly:#86909c;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1e80ff;--jjext-color-divider:#e5e6eb;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#ffffff;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#e8f3ff;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#f53f3f;--jjext-color-fourthly:#c9cdd4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4e5969;--jjext-color-layout-title-active:#1e80ff;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-4:#ffffff;--jjext-color-font-brand1-normal:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-2:#515767;--jjext-color-font-3:#8a919f;--jjext-color-font-4:#c2c8d1;--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05);--jjext-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-gray-0:#fff;--jjext-color-gray-1-1:#e4e6eb;--jjext-color-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-gray-1-3:#e4e6eb;--jjext-color-gray-2:#f2f3f5;--jjext-color-gray-3:#f7f8fa;--jjext-color-background:#f2f3f5;--jjext-color-layer-1:#fff;--jjext-color-layer-2-1:#f7f8fa;--jjext-color-layer-2-2:rgba(247, 248, 250, 0.7);--jjext-color-layer-3-fill:#f2f3f5;--jjext-color-layer-3-border:#e4e6eb;--jjext-color-layer-4-dropdown:#fff;--jjext-color-layer-5:#fff;--jjext-color-brand-1-normal:#1e80ff;--jjext-color-brand-2-hover:#1171ee;--jjext-color-brand-3-click:#0060dd;--jjext-color-brand-4-disable:#abcdff;--jjext-color-brand-5-light:#eaf2ff;--jjext-color-mask-1:rgba(0, 0, 0, 0.4);--jjext-color-mask-2:#fff;--jjext-color-mask-3:none;--jjext-color-mask-6:#ffffff;--jjext-color-brand-fill1-normal:rgba(30, 128, 255, 0.05);--jjext-color-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-brand-fill3-click:rgba(30, 128, 255, 0.2);--jjext-color-brand-stroke1-normal:rgba(30, 128, 255, 0.3);--jjext-color-brand-stroke2-hover:rgba(30, 128, 255, 0.45);--jjext-color-brand-stroke3-click:rgba(30, 128, 255, 0.6);--jjext-color-font_danger:#ff5132;--jjext-color-shade-1:rgba(0, 0, 0, 0.4);--jjext-color-popup:#fff;--jjext-color-popover:rgba(0, 0, 0, 0.8)}:root .dark[data-v-341e7439]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352a3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand1-normal:#4495ff;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-2:rgba(255, 255, 255, 0.7);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1);--jjext-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-gray-0:#000;--jjext-color-gray-1-1:rgba(255, 255, 255, 0.2);--jjext-color-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-gray-1-3:#464646;--jjext-color-gray-2:rgba(255, 255, 255, 0.12);--jjext-color-gray-3:rgba(255, 255, 255, 0.08);--jjext-color-background:#000;--jjext-color-layer-1:#181818;--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-2-2:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-fill:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-border:rgba(255, 255, 255, 0.2);--jjext-color-layer-4-dropdown:#2f2f2f;--jjext-color-layer-5:rgba(255, 255, 255, 0.12);--jjext-color-brand-1-normal:#2986ff;--jjext-color-brand-2-hover:#1473ed;--jjext-color-brand-3-click:#0563dd;--jjext-color-brand-4-disable:rgba(41, 134, 255, 0.4);--jjext-color-brand-5-light:rgba(30, 128, 255, 0.2);--jjext-color-mask-1:rgba(255, 255, 255, 0.4);--jjext-color-mask-2:#282828;--jjext-color-mask-3:rgba(0, 0, 0, 0.05);--jjext-color-mask-6:#181818;--jjext-color-brand-fill1-normal:rgba(41, 134, 255, 0.15);--jjext-color-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-brand-fill3-click:rgba(5, 99, 221, 0.35);--jjext-color-brand-stroke1-normal:rgba(41, 134, 255, 0.4);--jjext-color-brand-stroke2-hover:rgba(20, 115, 237, 0.6);--jjext-color-brand-stroke3-click:rgba(5, 99, 221, 0.6);--jjext-color-font_danger:#f85959;--jjext-color-shade-1:rgba(0, 0, 0, 0.6);--jjext-color-popup:#282828;--jjext-color-popover:#323232}.input-option[data-v-341e7439]{display:flex;flex-direction:column}.input-option span.error[data-v-341e7439]{margin-left:6.6666666667rem;font-size:1rem;line-height:20px;display:inline-block;height:20px;color:var(--jjext-color-tips)}.input-wrapper[data-v-341e7439]{display:flex;flex-direction:row;align-items:center;width:100%}.input-wrapper label[data-v-341e7439]{width:4em;font-size:1.1666666667rem;line-height:1.8333333333rem;color:var(--jjext-color-thirdly);margin-right:1rem}.input-wrapper .input[data-v-341e7439]{flex:1 0 auto;position:relative}.input-wrapper .input.error .input-inner[data-v-341e7439]{background-color:var(--jjext-color-input-error-bg)}.input-wrapper .input.error .btn-clear[data-v-341e7439]{color:var(--jjext-color-input-icon)}.input-wrapper .input .input-inner[data-v-341e7439]{background:var(--jjext-color-input-bg);border-radius:2px;color:var(--jjext-color-input-text);font-size:1.0833333333rem;line-height:1.8333333333rem;height:2.3333333333rem;padding:0 8px;outline:0;border:none;width:100%}.input-wrapper .input .input-inner[data-v-341e7439]::placeholder{color:var(--jjext-color-input-placeholder)}.input-wrapper .btn-clear[data-v-341e7439]{position:absolute;top:50%;right:0;transform:translateY(-50%);background:0 0;border:none;outline:0;color:var(--jjext-color-fourthly)}.input-wrapper .btn-clear[data-v-341e7439]::before{font-size:10px;line-height:10px}[data-v-5a92de1e]:root{--jjext-color-brand:#1e80ff;--jjext-color-brand-light:#e8f3ff;--jjext-color-nav-title:#86909c;--jjext-color-nav-popup-bg:#ffffff;--jjext-color-primary:#1d2129;--jjext-color-secondary-app:#4e5969;--jjext-color-thirdly:#86909c;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1e80ff;--jjext-color-divider:#e5e6eb;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#ffffff;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#e8f3ff;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#f53f3f;--jjext-color-fourthly:#c9cdd4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4e5969;--jjext-color-layout-title-active:#1e80ff;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-4:#ffffff;--jjext-color-font-brand1-normal:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-2:#515767;--jjext-color-font-3:#8a919f;--jjext-color-font-4:#c2c8d1;--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05);--jjext-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-gray-0:#fff;--jjext-color-gray-1-1:#e4e6eb;--jjext-color-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-gray-1-3:#e4e6eb;--jjext-color-gray-2:#f2f3f5;--jjext-color-gray-3:#f7f8fa;--jjext-color-background:#f2f3f5;--jjext-color-layer-1:#fff;--jjext-color-layer-2-1:#f7f8fa;--jjext-color-layer-2-2:rgba(247, 248, 250, 0.7);--jjext-color-layer-3-fill:#f2f3f5;--jjext-color-layer-3-border:#e4e6eb;--jjext-color-layer-4-dropdown:#fff;--jjext-color-layer-5:#fff;--jjext-color-brand-1-normal:#1e80ff;--jjext-color-brand-2-hover:#1171ee;--jjext-color-brand-3-click:#0060dd;--jjext-color-brand-4-disable:#abcdff;--jjext-color-brand-5-light:#eaf2ff;--jjext-color-mask-1:rgba(0, 0, 0, 0.4);--jjext-color-mask-2:#fff;--jjext-color-mask-3:none;--jjext-color-mask-6:#ffffff;--jjext-color-brand-fill1-normal:rgba(30, 128, 255, 0.05);--jjext-color-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-brand-fill3-click:rgba(30, 128, 255, 0.2);--jjext-color-brand-stroke1-normal:rgba(30, 128, 255, 0.3);--jjext-color-brand-stroke2-hover:rgba(30, 128, 255, 0.45);--jjext-color-brand-stroke3-click:rgba(30, 128, 255, 0.6);--jjext-color-font_danger:#ff5132;--jjext-color-shade-1:rgba(0, 0, 0, 0.4);--jjext-color-popup:#fff;--jjext-color-popover:rgba(0, 0, 0, 0.8)}:root .dark[data-v-5a92de1e]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352a3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand1-normal:#4495ff;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-2:rgba(255, 255, 255, 0.7);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1);--jjext-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-gray-0:#000;--jjext-color-gray-1-1:rgba(255, 255, 255, 0.2);--jjext-color-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-gray-1-3:#464646;--jjext-color-gray-2:rgba(255, 255, 255, 0.12);--jjext-color-gray-3:rgba(255, 255, 255, 0.08);--jjext-color-background:#000;--jjext-color-layer-1:#181818;--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-2-2:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-fill:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-border:rgba(255, 255, 255, 0.2);--jjext-color-layer-4-dropdown:#2f2f2f;--jjext-color-layer-5:rgba(255, 255, 255, 0.12);--jjext-color-brand-1-normal:#2986ff;--jjext-color-brand-2-hover:#1473ed;--jjext-color-brand-3-click:#0563dd;--jjext-color-brand-4-disable:rgba(41, 134, 255, 0.4);--jjext-color-brand-5-light:rgba(30, 128, 255, 0.2);--jjext-color-mask-1:rgba(255, 255, 255, 0.4);--jjext-color-mask-2:#282828;--jjext-color-mask-3:rgba(0, 0, 0, 0.05);--jjext-color-mask-6:#181818;--jjext-color-brand-fill1-normal:rgba(41, 134, 255, 0.15);--jjext-color-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-brand-fill3-click:rgba(5, 99, 221, 0.35);--jjext-color-brand-stroke1-normal:rgba(41, 134, 255, 0.4);--jjext-color-brand-stroke2-hover:rgba(20, 115, 237, 0.6);--jjext-color-brand-stroke3-click:rgba(5, 99, 221, 0.6);--jjext-color-font_danger:#f85959;--jjext-color-shade-1:rgba(0, 0, 0, 0.6);--jjext-color-popup:#282828;--jjext-color-popover:#323232}[data-v-5a92de1e]{box-sizing:border-box}.color-tool[data-v-5a92de1e]{padding:0 16px!important}.color-tool .row[data-v-5a92de1e]{display:flex;align-items:center}.color-tool .color-picker[data-v-5a92de1e]{cursor:pointer;outline:0;border:none;padding:0;margin:0;border-radius:2px;background-color:transparent;width:92px;height:40px}.color-tool .color-picker[data-v-5a92de1e]::-webkit-color-swatch-wrapper{padding:3px;border:1px solid transparent;border-radius:4px;transition:all .15s linear}.color-tool .color-picker[data-v-5a92de1e]::-webkit-color-swatch-wrapper:hover{border:1px solid #bedaff}.color-tool .color-picker[data-v-5a92de1e]::-webkit-color-swatch{border-radius:2px;border:none}.color-tool .input[data-v-5a92de1e]{transform:translateY(10px);flex:1 1 auto;margin:0 12px}.color-tool .input[data-v-5a92de1e] input.input-inner{height:40px;padding-left:16px;font-size:14px;color:var(--jjext-color-primary);box-sizing:border-box;background:var(--jjext-color-main-bg)}.color-tool .input[data-v-5a92de1e] label{display:none}.color-tool .input[data-v-5a92de1e] span.error{margin-left:16px}.color-tool .input[data-v-5a92de1e] .input-wrapper .btn-clear{right:8px}.color-tool .input[data-v-5a92de1e] .input-wrapper .btn-clear::before{font-size:14px;color:#c9cdd4}.color-tool button[data-v-5a92de1e]{outline:0;border:none;background-color:unset;width:93px;height:40px;font-size:14px}.color-tool .btn-convert[data-v-5a92de1e]{background:var(--jjext-color-brand);border-radius:2px;color:#fff;transition:all .15s linear}.color-tool .btn-convert[data-v-5a92de1e]:hover{background:#5399ff}.color-tool .btn-convert[data-v-5a92de1e]:active{background:#0060dd}.color-tool .btn-copy[data-v-5a92de1e]{background:rgba(30,128,255,.05);border:1px solid rgba(30,128,255,.3);border-radius:2px;color:var(--jjext-color-brand);transition:all .15s linear}.color-tool .btn-copy[data-v-5a92de1e]:hover{background:rgba(30,128,255,.1);border-color:rgba(30,128,255,.45)}.color-tool .btn-copy[data-v-5a92de1e]:active{background:rgba(30,128,255,.2);border-color:rgba(30,128,255,.6)}.color-tool .display[data-v-5a92de1e]{flex:1;text-align:start;background-color:var(--jjext-color-main-bg);height:40px;margin:0 12px;border-radius:2px;line-height:40px;padding-left:16px;font-size:14px;color:var(--jjext-color-primary)}.color-tool .label[data-v-5a92de1e]{width:92px;font-size:16px;font-weight:500;color:var(--jjext-color-primary);text-align:end}.color-tool .row[data-v-5a92de1e]:not(:first-of-type){margin-top:16px}[data-v-6b3fcf66]:root{--jjext-color-brand:#1e80ff;--jjext-color-brand-light:#e8f3ff;--jjext-color-nav-title:#86909c;--jjext-color-nav-popup-bg:#ffffff;--jjext-color-primary:#1d2129;--jjext-color-secondary-app:#4e5969;--jjext-color-thirdly:#86909c;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1e80ff;--jjext-color-divider:#e5e6eb;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#ffffff;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#e8f3ff;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#f53f3f;--jjext-color-fourthly:#c9cdd4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4e5969;--jjext-color-layout-title-active:#1e80ff;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-4:#ffffff;--jjext-color-font-brand1-normal:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-2:#515767;--jjext-color-font-3:#8a919f;--jjext-color-font-4:#c2c8d1;--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05);--jjext-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-gray-0:#fff;--jjext-color-gray-1-1:#e4e6eb;--jjext-color-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-gray-1-3:#e4e6eb;--jjext-color-gray-2:#f2f3f5;--jjext-color-gray-3:#f7f8fa;--jjext-color-background:#f2f3f5;--jjext-color-layer-1:#fff;--jjext-color-layer-2-1:#f7f8fa;--jjext-color-layer-2-2:rgba(247, 248, 250, 0.7);--jjext-color-layer-3-fill:#f2f3f5;--jjext-color-layer-3-border:#e4e6eb;--jjext-color-layer-4-dropdown:#fff;--jjext-color-layer-5:#fff;--jjext-color-brand-1-normal:#1e80ff;--jjext-color-brand-2-hover:#1171ee;--jjext-color-brand-3-click:#0060dd;--jjext-color-brand-4-disable:#abcdff;--jjext-color-brand-5-light:#eaf2ff;--jjext-color-mask-1:rgba(0, 0, 0, 0.4);--jjext-color-mask-2:#fff;--jjext-color-mask-3:none;--jjext-color-mask-6:#ffffff;--jjext-color-brand-fill1-normal:rgba(30, 128, 255, 0.05);--jjext-color-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-brand-fill3-click:rgba(30, 128, 255, 0.2);--jjext-color-brand-stroke1-normal:rgba(30, 128, 255, 0.3);--jjext-color-brand-stroke2-hover:rgba(30, 128, 255, 0.45);--jjext-color-brand-stroke3-click:rgba(30, 128, 255, 0.6);--jjext-color-font_danger:#ff5132;--jjext-color-shade-1:rgba(0, 0, 0, 0.4);--jjext-color-popup:#fff;--jjext-color-popover:rgba(0, 0, 0, 0.8)}:root .dark[data-v-6b3fcf66]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352a3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand1-normal:#4495ff;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-2:rgba(255, 255, 255, 0.7);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1);--jjext-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-gray-0:#000;--jjext-color-gray-1-1:rgba(255, 255, 255, 0.2);--jjext-color-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-gray-1-3:#464646;--jjext-color-gray-2:rgba(255, 255, 255, 0.12);--jjext-color-gray-3:rgba(255, 255, 255, 0.08);--jjext-color-background:#000;--jjext-color-layer-1:#181818;--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-2-2:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-fill:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-border:rgba(255, 255, 255, 0.2);--jjext-color-layer-4-dropdown:#2f2f2f;--jjext-color-layer-5:rgba(255, 255, 255, 0.12);--jjext-color-brand-1-normal:#2986ff;--jjext-color-brand-2-hover:#1473ed;--jjext-color-brand-3-click:#0563dd;--jjext-color-brand-4-disable:rgba(41, 134, 255, 0.4);--jjext-color-brand-5-light:rgba(30, 128, 255, 0.2);--jjext-color-mask-1:rgba(255, 255, 255, 0.4);--jjext-color-mask-2:#282828;--jjext-color-mask-3:rgba(0, 0, 0, 0.05);--jjext-color-mask-6:#181818;--jjext-color-brand-fill1-normal:rgba(41, 134, 255, 0.15);--jjext-color-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-brand-fill3-click:rgba(5, 99, 221, 0.35);--jjext-color-brand-stroke1-normal:rgba(41, 134, 255, 0.4);--jjext-color-brand-stroke2-hover:rgba(20, 115, 237, 0.6);--jjext-color-brand-stroke3-click:rgba(5, 99, 221, 0.6);--jjext-color-font_danger:#f85959;--jjext-color-shade-1:rgba(0, 0, 0, 0.6);--jjext-color-popup:#282828;--jjext-color-popover:#323232}.quick-tool-drawer[data-v-6b3fcf66]{z-index:750;position:fixed;right:0;top:0;bottom:0;width:60%;background:var(--jjext-color-thirdly-bg)}.quick-tool-drawer.dark .header .title[data-v-6b3fcf66]{color:#e3e3e3}.quick-tool-drawer .quick-tool-drawer__header__[data-v-6b3fcf66]{position:relative;height:64px;padding:0 16px;display:flex;flex-direction:row;align-items:center}.quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__icon__[data-v-6b3fcf66]{width:40px;height:40px}.quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__title__[data-v-6b3fcf66]{margin:0 0 0 9px;padding:0;font-size:16px;font-weight:500;line-height:22px;color:var(--jjext-color-brand)}.quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__btn-close__[data-v-6b3fcf66]{cursor:pointer;position:absolute;right:16px;top:50%;font-size:18px;transform:translateY(-50%)}.quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__btn-close__[data-v-6b3fcf66]::after{display:block;content:" ";position:absolute;padding:10px;width:100%;height:100%;top:-50%;left:-50%}.quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__btn-close__ svg[data-v-6b3fcf66]{fill:var(--jjext-color-thirdly)}.quick-tool-drawer .quick-tool-drawer__tool__[data-v-6b3fcf66]{width:100%;height:100%;box-sizing:border-box}[data-v-19f1e2c8]:root{--jjext-color-brand:#1e80ff;--jjext-color-brand-light:#e8f3ff;--jjext-color-nav-title:#86909c;--jjext-color-nav-popup-bg:#ffffff;--jjext-color-primary:#1d2129;--jjext-color-secondary-app:#4e5969;--jjext-color-thirdly:#86909c;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1e80ff;--jjext-color-divider:#e5e6eb;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#ffffff;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#e8f3ff;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#f53f3f;--jjext-color-fourthly:#c9cdd4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4e5969;--jjext-color-layout-title-active:#1e80ff;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-4:#ffffff;--jjext-color-font-brand1-normal:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-2:#515767;--jjext-color-font-3:#8a919f;--jjext-color-font-4:#c2c8d1;--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05);--jjext-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-gray-0:#fff;--jjext-color-gray-1-1:#e4e6eb;--jjext-color-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-gray-1-3:#e4e6eb;--jjext-color-gray-2:#f2f3f5;--jjext-color-gray-3:#f7f8fa;--jjext-color-background:#f2f3f5;--jjext-color-layer-1:#fff;--jjext-color-layer-2-1:#f7f8fa;--jjext-color-layer-2-2:rgba(247, 248, 250, 0.7);--jjext-color-layer-3-fill:#f2f3f5;--jjext-color-layer-3-border:#e4e6eb;--jjext-color-layer-4-dropdown:#fff;--jjext-color-layer-5:#fff;--jjext-color-brand-1-normal:#1e80ff;--jjext-color-brand-2-hover:#1171ee;--jjext-color-brand-3-click:#0060dd;--jjext-color-brand-4-disable:#abcdff;--jjext-color-brand-5-light:#eaf2ff;--jjext-color-mask-1:rgba(0, 0, 0, 0.4);--jjext-color-mask-2:#fff;--jjext-color-mask-3:none;--jjext-color-mask-6:#ffffff;--jjext-color-brand-fill1-normal:rgba(30, 128, 255, 0.05);--jjext-color-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-brand-fill3-click:rgba(30, 128, 255, 0.2);--jjext-color-brand-stroke1-normal:rgba(30, 128, 255, 0.3);--jjext-color-brand-stroke2-hover:rgba(30, 128, 255, 0.45);--jjext-color-brand-stroke3-click:rgba(30, 128, 255, 0.6);--jjext-color-font_danger:#ff5132;--jjext-color-shade-1:rgba(0, 0, 0, 0.4);--jjext-color-popup:#fff;--jjext-color-popover:rgba(0, 0, 0, 0.8)}:root .dark[data-v-19f1e2c8]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352a3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand1-normal:#4495ff;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-2:rgba(255, 255, 255, 0.7);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1);--jjext-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-gray-0:#000;--jjext-color-gray-1-1:rgba(255, 255, 255, 0.2);--jjext-color-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-gray-1-3:#464646;--jjext-color-gray-2:rgba(255, 255, 255, 0.12);--jjext-color-gray-3:rgba(255, 255, 255, 0.08);--jjext-color-background:#000;--jjext-color-layer-1:#181818;--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-2-2:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-fill:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-border:rgba(255, 255, 255, 0.2);--jjext-color-layer-4-dropdown:#2f2f2f;--jjext-color-layer-5:rgba(255, 255, 255, 0.12);--jjext-color-brand-1-normal:#2986ff;--jjext-color-brand-2-hover:#1473ed;--jjext-color-brand-3-click:#0563dd;--jjext-color-brand-4-disable:rgba(41, 134, 255, 0.4);--jjext-color-brand-5-light:rgba(30, 128, 255, 0.2);--jjext-color-mask-1:rgba(255, 255, 255, 0.4);--jjext-color-mask-2:#282828;--jjext-color-mask-3:rgba(0, 0, 0, 0.05);--jjext-color-mask-6:#181818;--jjext-color-brand-fill1-normal:rgba(41, 134, 255, 0.15);--jjext-color-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-brand-fill3-click:rgba(5, 99, 221, 0.35);--jjext-color-brand-stroke1-normal:rgba(41, 134, 255, 0.4);--jjext-color-brand-stroke2-hover:rgba(20, 115, 237, 0.6);--jjext-color-brand-stroke3-click:rgba(5, 99, 221, 0.6);--jjext-color-font_danger:#f85959;--jjext-color-shade-1:rgba(0, 0, 0, 0.6);--jjext-color-popup:#282828;--jjext-color-popover:#323232}.mask[data-v-19f1e2c8]{position:fixed;left:0;right:0;top:0;bottom:0;z-index:600;background-color:var(--jjext-color-mask)}.slide-left-enter-active,.slide-left-leave-active{transition:transform .3s linear}.slide-left-enter-from,.slide-left-leave-to{transform:translateX(100%)}[data-v-635a5c8c]:root{--jjext-color-brand:#1e80ff;--jjext-color-brand-light:#e8f3ff;--jjext-color-nav-title:#86909c;--jjext-color-nav-popup-bg:#ffffff;--jjext-color-primary:#1d2129;--jjext-color-secondary-app:#4e5969;--jjext-color-thirdly:#86909c;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1e80ff;--jjext-color-divider:#e5e6eb;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#ffffff;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#e8f3ff;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#f53f3f;--jjext-color-fourthly:#c9cdd4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4e5969;--jjext-color-layout-title-active:#1e80ff;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-4:#ffffff;--jjext-color-font-brand1-normal:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-2:#515767;--jjext-color-font-3:#8a919f;--jjext-color-font-4:#c2c8d1;--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05);--jjext-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-gray-0:#fff;--jjext-color-gray-1-1:#e4e6eb;--jjext-color-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-gray-1-3:#e4e6eb;--jjext-color-gray-2:#f2f3f5;--jjext-color-gray-3:#f7f8fa;--jjext-color-background:#f2f3f5;--jjext-color-layer-1:#fff;--jjext-color-layer-2-1:#f7f8fa;--jjext-color-layer-2-2:rgba(247, 248, 250, 0.7);--jjext-color-layer-3-fill:#f2f3f5;--jjext-color-layer-3-border:#e4e6eb;--jjext-color-layer-4-dropdown:#fff;--jjext-color-layer-5:#fff;--jjext-color-brand-1-normal:#1e80ff;--jjext-color-brand-2-hover:#1171ee;--jjext-color-brand-3-click:#0060dd;--jjext-color-brand-4-disable:#abcdff;--jjext-color-brand-5-light:#eaf2ff;--jjext-color-mask-1:rgba(0, 0, 0, 0.4);--jjext-color-mask-2:#fff;--jjext-color-mask-3:none;--jjext-color-mask-6:#ffffff;--jjext-color-brand-fill1-normal:rgba(30, 128, 255, 0.05);--jjext-color-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-brand-fill3-click:rgba(30, 128, 255, 0.2);--jjext-color-brand-stroke1-normal:rgba(30, 128, 255, 0.3);--jjext-color-brand-stroke2-hover:rgba(30, 128, 255, 0.45);--jjext-color-brand-stroke3-click:rgba(30, 128, 255, 0.6);--jjext-color-font_danger:#ff5132;--jjext-color-shade-1:rgba(0, 0, 0, 0.4);--jjext-color-popup:#fff;--jjext-color-popover:rgba(0, 0, 0, 0.8)}:root .dark[data-v-635a5c8c]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352a3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand1-normal:#4495ff;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-2:rgba(255, 255, 255, 0.7);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1);--jjext-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-gray-0:#000;--jjext-color-gray-1-1:rgba(255, 255, 255, 0.2);--jjext-color-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-gray-1-3:#464646;--jjext-color-gray-2:rgba(255, 255, 255, 0.12);--jjext-color-gray-3:rgba(255, 255, 255, 0.08);--jjext-color-background:#000;--jjext-color-layer-1:#181818;--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-2-2:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-fill:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-border:rgba(255, 255, 255, 0.2);--jjext-color-layer-4-dropdown:#2f2f2f;--jjext-color-layer-5:rgba(255, 255, 255, 0.12);--jjext-color-brand-1-normal:#2986ff;--jjext-color-brand-2-hover:#1473ed;--jjext-color-brand-3-click:#0563dd;--jjext-color-brand-4-disable:rgba(41, 134, 255, 0.4);--jjext-color-brand-5-light:rgba(30, 128, 255, 0.2);--jjext-color-mask-1:rgba(255, 255, 255, 0.4);--jjext-color-mask-2:#282828;--jjext-color-mask-3:rgba(0, 0, 0, 0.05);--jjext-color-mask-6:#181818;--jjext-color-brand-fill1-normal:rgba(41, 134, 255, 0.15);--jjext-color-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-brand-fill3-click:rgba(5, 99, 221, 0.35);--jjext-color-brand-stroke1-normal:rgba(41, 134, 255, 0.4);--jjext-color-brand-stroke2-hover:rgba(20, 115, 237, 0.6);--jjext-color-brand-stroke3-click:rgba(5, 99, 221, 0.6);--jjext-color-font_danger:#f85959;--jjext-color-shade-1:rgba(0, 0, 0, 0.6);--jjext-color-popup:#282828;--jjext-color-popover:#323232}.search-app[data-v-635a5c8c]{z-index:9999;padding-top:160px;position:fixed;left:0;right:0;top:0;bottom:0;display:flex;align-items:flex-start;justify-content:center}.search-app.extension[data-v-635a5c8c]{z-index:500}@media (max-height:720px){.search-app.tool-active[data-v-635a5c8c]{padding-top:80px}}@media (max-height:640px){.search-app.tool-active[data-v-635a5c8c]{padding-top:30px}}.search-app .search-app__wrapper__[data-v-635a5c8c]{overflow:hidden;border-radius:4px;border:1px solid var(--jjext-color-font-brand1-normal);background:var(--jjext-color-layer-4);box-shadow:0 0 0 4px rgba(30,128,255,.2),0 0 20px rgba(0,0,0,.15);backdrop-filter:blur(15px)}.search-app .search-app__wrapper__ .search-result[data-v-635a5c8c]{margin-top:8px}.search-app .search-app__wrapper__ .search-result .tool[data-v-635a5c8c]{padding:0 8px}.search-app .search-app__wrapper__[data-v-635a5c8c] .search-suggest{padding:0 0 8px 0}.search-app .search-app__wrapper__[data-v-635a5c8c] .search-suggest .list{border-top:none;padding-left:8px;padding-right:8px}.search-app .search-app__wrapper__[data-v-635a5c8c] .search-suggest .list .suggest-item{padding:0 13px}.search-app .search-app__wrapper__[data-v-635a5c8c] .search-suggest .list .suggest-item .content{margin:0 0 0 17px}.search-app .search-app__wrapper__[data-v-635a5c8c] .search-suggest .list .tool-item{padding:0 9px 0 10px}.search-app .search-app__wrapper__[data-v-635a5c8c] .search-suggest .list .tool-item .content{margin-left:12px}.search-app .juejin-search[data-v-635a5c8c]{margin:8px}:root{--jjext-color-brand:#1e80ff;--jjext-color-brand-light:#e8f3ff;--jjext-color-nav-title:#86909c;--jjext-color-nav-popup-bg:#ffffff;--jjext-color-primary:#1d2129;--jjext-color-secondary-app:#4e5969;--jjext-color-thirdly:#86909c;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1e80ff;--jjext-color-divider:#e5e6eb;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#ffffff;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#e8f3ff;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#f53f3f;--jjext-color-fourthly:#c9cdd4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4e5969;--jjext-color-layout-title-active:#1e80ff;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-4:#ffffff;--jjext-color-font-brand1-normal:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-2:#515767;--jjext-color-font-3:#8a919f;--jjext-color-font-4:#c2c8d1;--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05);--jjext-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-gray-0:#fff;--jjext-color-gray-1-1:#e4e6eb;--jjext-color-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-gray-1-3:#e4e6eb;--jjext-color-gray-2:#f2f3f5;--jjext-color-gray-3:#f7f8fa;--jjext-color-background:#f2f3f5;--jjext-color-layer-1:#fff;--jjext-color-layer-2-1:#f7f8fa;--jjext-color-layer-2-2:rgba(247, 248, 250, 0.7);--jjext-color-layer-3-fill:#f2f3f5;--jjext-color-layer-3-border:#e4e6eb;--jjext-color-layer-4-dropdown:#fff;--jjext-color-layer-5:#fff;--jjext-color-brand-1-normal:#1e80ff;--jjext-color-brand-2-hover:#1171ee;--jjext-color-brand-3-click:#0060dd;--jjext-color-brand-4-disable:#abcdff;--jjext-color-brand-5-light:#eaf2ff;--jjext-color-mask-1:rgba(0, 0, 0, 0.4);--jjext-color-mask-2:#fff;--jjext-color-mask-3:none;--jjext-color-mask-6:#ffffff;--jjext-color-brand-fill1-normal:rgba(30, 128, 255, 0.05);--jjext-color-brand-fill2-hover:rgba(30, 128, 255, 0.1);--jjext-color-brand-fill3-click:rgba(30, 128, 255, 0.2);--jjext-color-brand-stroke1-normal:rgba(30, 128, 255, 0.3);--jjext-color-brand-stroke2-hover:rgba(30, 128, 255, 0.45);--jjext-color-brand-stroke3-click:rgba(30, 128, 255, 0.6);--jjext-color-font_danger:#ff5132;--jjext-color-shade-1:rgba(0, 0, 0, 0.4);--jjext-color-popup:#fff;--jjext-color-popover:rgba(0, 0, 0, 0.8)}:root .dark{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352a3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand1-normal:#4495ff;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-2:rgba(255, 255, 255, 0.7);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-font-white:#fff;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1);--jjext-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-gray-0:#000;--jjext-color-gray-1-1:rgba(255, 255, 255, 0.2);--jjext-color-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-gray-1-3:#464646;--jjext-color-gray-2:rgba(255, 255, 255, 0.12);--jjext-color-gray-3:rgba(255, 255, 255, 0.08);--jjext-color-background:#000;--jjext-color-layer-1:#181818;--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-2-2:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-fill:rgba(255, 255, 255, 0.08);--jjext-color-layer-3-border:rgba(255, 255, 255, 0.2);--jjext-color-layer-4-dropdown:#2f2f2f;--jjext-color-layer-5:rgba(255, 255, 255, 0.12);--jjext-color-brand-1-normal:#2986ff;--jjext-color-brand-2-hover:#1473ed;--jjext-color-brand-3-click:#0563dd;--jjext-color-brand-4-disable:rgba(41, 134, 255, 0.4);--jjext-color-brand-5-light:rgba(30, 128, 255, 0.2);--jjext-color-mask-1:rgba(255, 255, 255, 0.4);--jjext-color-mask-2:#282828;--jjext-color-mask-3:rgba(0, 0, 0, 0.05);--jjext-color-mask-6:#181818;--jjext-color-brand-fill1-normal:rgba(41, 134, 255, 0.15);--jjext-color-brand-fill2-hover:rgba(20, 115, 237, 0.25);--jjext-color-brand-fill3-click:rgba(5, 99, 221, 0.35);--jjext-color-brand-stroke1-normal:rgba(41, 134, 255, 0.4);--jjext-color-brand-stroke2-hover:rgba(20, 115, 237, 0.6);--jjext-color-brand-stroke3-click:rgba(5, 99, 221, 0.6);--jjext-color-font_danger:#f85959;--jjext-color-shade-1:rgba(0, 0, 0, 0.6);--jjext-color-popup:#282828;--jjext-color-popover:#323232}</style><link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/CBehaviorRiskScan-sHA8uvNE.js"><link rel="modulepreload" as="script" crossorigin="" href="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/index-B4d74vWj.js.下载"><link rel="stylesheet" crossorigin="" href="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/CBehaviorRiskScan-DZ11Kx1x.css"><link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/CSpecialList-BJHJIhZS.js"><link rel="stylesheet" crossorigin="" href="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/CSpecialList-DoJX00zI.css"><link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/index-6XLcnucb.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/index-DWaepPRf.js"><link rel="stylesheet" crossorigin="" href="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/index-B5COpqMx.css"><link rel="stylesheet" crossorigin="" href="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/index-3CyoI4Ip.css"><link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/CG03HZ01-DxtT7n_o.js"><link rel="stylesheet" crossorigin="" href="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/Promote-tn0RQdqM.css"><link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/CBankLoanApplication-3OKEvaq2.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/LTable-jtVDM3iJ.js"><link rel="stylesheet" crossorigin="" href="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/LTable-B_VK4T8L.css"><link rel="stylesheet" crossorigin="" href="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/CBankLoanApplication-4GW9Jg0X.css"><link rel="modulepreload" as="script" crossorigin="" href="https://www.tianyuandb.com/assets/CBankLoanBehavior-BD0jokFv.js"><link rel="stylesheet" crossorigin="" href="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/CBankLoanBehavior-Bo_SQsYR.css"><style>x-vue-echarts{display:block;width:100%;height:100%;min-width:0}
</style><style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: **********;
  display: flex;
  justify-content: center;
  align-items: center;
}
.immersive-translate-attach-loading::after {
  content: " ";

  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;

  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-2000%, -50%);
  z-index: 100;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.immersive-translate-input-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;
}

@keyframes immersiveTranslateShadowRolling {
  0% {
    box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  12% {
    box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
      100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
  }

  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
      110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }

  62% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
      120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }

  75% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }

  87% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
  }

  100% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
  }
}

.immersive-translate-toast {
  display: flex;
  position: fixed;
  z-index: **********;
  left: 0;
  right: 0;
  top: 1%;
  width: fit-content;
  padding: 12px 20px;
  margin: auto;
  overflow: auto;
  background: #fef6f9;
  box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
  font-size: 15px;
  border-radius: 8px;
  color: #333;
}

.immersive-translate-toast-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.immersive-translate-toast-hidden {
  margin: 0 20px 0 72px;
  text-decoration: underline;
  cursor: pointer;
}

.immersive-translate-toast-close {
  color: #666666;
  font-size: 20px;
  font-weight: bold;
  padding: 0 10px;
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .immersive-translate-toast {
    top: 0;
    padding: 12px 0px 0 10px;
  }
  .immersive-translate-toast-content {
    flex-direction: column;
    text-align: center;
  }
  .immersive-translate-toast-hidden {
    margin: 10px auto;
  }
}

.immersive-translate-modal {
  display: none;
  position: fixed;
  z-index: **********;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
  font-size: 15px;
}

.immersive-translate-modal-content {
  background-color: #fefefe;
  margin: 10% auto;
  padding: 40px 24px 24px;
  border-radius: 12px;
  width: 350px;
  font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  position: relative;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-content {
    margin: 50% auto !important;
  }
}

.immersive-translate-modal .immersive-translate-modal-content-in-input {
  max-width: 500px;
}
.immersive-translate-modal-content-in-input .immersive-translate-modal-body {
  text-align: left;
  max-height: unset;
}

.immersive-translate-modal-title {
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  color: #333333;
}

.immersive-translate-modal-body {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  margin-top: 24px;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-body {
    max-height: 250px;
    overflow-y: auto;
  }
}

.immersive-translate-close {
  color: #666666;
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 20px;
  font-weight: bold;
}

.immersive-translate-close:hover,
.immersive-translate-close:focus {
  text-decoration: none;
  cursor: pointer;
}

.immersive-translate-modal-footer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24px;
}

.immersive-translate-btn {
  width: fit-content;
  color: #fff;
  background-color: #ea4c89;
  border: none;
  font-size: 14px;
  margin: 0 8px;
  padding: 9px 30px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.immersive-translate-btn-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.immersive-translate-btn:hover {
  background-color: #f082ac;
}
.immersive-translate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.immersive-translate-btn:disabled:hover {
  background-color: #ea4c89;
}

.immersive-translate-link-btn {
  background-color: transparent;
  color: #ea4c89;
  border: none;
  cursor: pointer;
  height: 30px;
  line-height: 30px;
}

.immersive-translate-cancel-btn {
  /* gray color */
  background-color: rgb(89, 107, 120);
}

.immersive-translate-cancel-btn:hover {
  background-color: hsl(205, 20%, 32%);
}

.immersive-translate-action-btn {
  background-color: transparent;
  color: #ea4c89;
  border: 1px solid #ea4c89;
}

.immersive-translate-btn svg {
  margin-right: 5px;
}

.immersive-translate-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-primary-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-modal input[type="radio"] {
  margin: 0 6px;
  cursor: pointer;
}

.immersive-translate-modal label {
  cursor: pointer;
}

.immersive-translate-close-action {
  position: absolute;
  top: 2px;
  right: 0px;
  cursor: pointer;
}

.imt-image-status {
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 16px !important;
}
.imt-image-status img,
.imt-image-status svg,
.imt-img-loading {
  width: 28px !important;
  height: 28px !important;
  margin: 0 0 8px 0 !important;
  min-height: 28px !important;
  min-width: 28px !important;
  position: relative !important;
}
.imt-img-loading {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
  background-size: 28px 28px;
  animation: image-loading-rotate 1s linear infinite !important;
}

.imt-image-status span {
  color: var(--bg-2, #fff) !important;
  font-size: 14px !important;
  line-height: 14px !important;
  font-weight: 500 !important;
  font-family: "PingFang SC", Arial, sans-serif !important;
}

@keyframes image-loading-rotate {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
</style></head>

    <body class="">
        
        <div id="app" data-v-app=""><div class="van-nav-bar__placeholder" style="height: 46px;"><div class="van-nav-bar van-nav-bar--fixed"><div class="van-nav-bar__content"><div class="van-nav-bar__left van-haptics-feedback"><i class="van-badge__wrapper van-icon van-icon-arrow-left van-nav-bar__arrow"><!----><!----><!----></i><span class="van-nav-bar__text">返回</span></div><div class="van-nav-bar__title van-ellipsis">示例报告</div><!----></div></div></div><div data-v-622108cb="" class="min-h-full from-blue-100 to-white bg-gradient-to-b"><div data-v-622108cb="" class="van-tabs van-tabs--line"><div><div class="van-sticky"><div><div class="van-tabs__wrap"><div role="tablist" class="van-tabs__nav van-tabs__nav--line van-tabs__nav--shrink van-tabs__nav--complete" aria-orientation="horizontal"><!----><div id="van-tabs-1-0" role="tab" class="van-tab van-tab--line van-tab--shrink" tabindex="-1" aria-selected="false" aria-controls="van-tab-2" data-allow-mismatch="attribute"><span class="van-tab__text">分析指数</span></div><div id="van-tabs-1-1" role="tab" class="van-tab van-tab--line van-tab--shrink" tabindex="-1" aria-selected="false" aria-controls="van-tab-3" data-allow-mismatch="attribute"><span class="van-tab__text">基本信息</span></div><div id="van-tabs-1-2" role="tab" class="van-tab van-tab--line van-tab--shrink" tabindex="-1" aria-selected="false" aria-controls="van-tab-4" data-allow-mismatch="attribute"><span class="van-tab__text">风险行为扫描</span></div><div id="van-tabs-1-3" role="tab" class="van-tab van-tab--line van-tab--shrink" tabindex="-1" aria-selected="false" aria-controls="van-tab-5" data-allow-mismatch="attribute"><span class="van-tab__text">违约失信</span></div><div id="van-tabs-1-4" role="tab" class="van-tab van-tab--line van-tab--shrink van-tab--active" tabindex="0" aria-selected="true" aria-controls="van-tab-6" data-allow-mismatch="attribute"><span class="van-tab__text">司法涉诉</span></div><div id="van-tabs-1-5" role="tab" class="van-tab van-tab--line van-tab--shrink" tabindex="-1" aria-selected="false" aria-controls="van-tab-7" data-allow-mismatch="attribute"><span class="van-tab__text">手机号码风险</span></div><div id="van-tabs-1-6" role="tab" class="van-tab van-tab--line van-tab--shrink" tabindex="-1" aria-selected="false" aria-controls="van-tab-8" data-allow-mismatch="attribute"><span class="van-tab__text">借贷申请记录</span></div><div id="van-tabs-1-7" role="tab" class="van-tab van-tab--line van-tab--shrink" tabindex="-1" aria-selected="false" aria-controls="van-tab-9" data-allow-mismatch="attribute"><span class="van-tab__text">借贷行为记录</span></div><div class="van-tabs__line" style="transform: translateX(360px) translateX(-50%); transition-duration: 0.3s;"></div><!----></div></div><!----></div></div></div><div class="van-tabs__content"><div data-v-622108cb="" class="flex flex-col gap-y-4 p-4"><!----><div data-v-622108cb="" id="van-tab-2" role="tabpanel" class="van-tab__panel" tabindex="0" aria-labelledby="van-tabs-1-0" data-allow-mismatch="attribute"><div data-v-622108cb="" id="overdiv" class="title mb-4">分析指数</div><div data-v-622108cb="" class="card mb-4"><div data-v-622108cb="" class="my-4"><div data-v-f5b10012="" data-v-622108cb=""><div data-v-f5b10012="" style="width: 100%; height: 200px; user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);" _echarts_instance_="ec_1753582256499"><div style="position: relative; width: 456px; height: 200px; padding: 0px; margin: 0px; border-width: 0px; cursor: default;"><canvas data-zr-dom-id="zr_0" width="570" height="250" style="position: absolute; left: 0px; top: 0px; width: 456px; height: 200px; user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 0px; margin: 0px; border-width: 0px;"></canvas></div></div><div data-v-f5b10012="" class="risk-description">根据综合分析，当前报告显示高度风险状态，多项重要指标严重异常，请立即采取相应措施。</div></div></div></div></div><div data-v-622108cb="" id="van-tab-3" role="tabpanel" class="van-tab__panel" tabindex="0" aria-labelledby="van-tabs-1-1" data-allow-mismatch="attribute"><div data-v-622108cb="" id="overdiv" class="title mb-4">基本信息</div><div data-v-622108cb="" class="card"><div data-v-622108cb="" class="flex flex-col gap-y-2"><div data-v-622108cb="" class="relative"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 inline-block rounded-lg px-2 py-1 text-white font-bold shadow-md">报告信息</div><div class="absolute left-0 top-0 h-4 w-4 transform rounded-full bg-white shadow-md -translate-x-2 -translate-y-2"></div><div class="relative mt-1.5"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 h-[2px] w-full rounded"></div></div></div><div data-v-622108cb="" class="flex flex-col gap-2 my-2"><div data-v-622108cb="" class="flex justify-between border-b pb-2 pl-2"><span data-v-622108cb="" class="text-gray-700 font-bold">报告时间：</span><span data-v-622108cb="" class="text-gray-600">2025-1-1 12:00:00</span></div><div data-v-622108cb="" class="flex justify-between border-b pb-2 pl-2"><span data-v-622108cb="" class="text-gray-700 font-bold">报告项目：</span><span data-v-622108cb="" class="text-gray-600">个人风险</span></div></div><div data-v-622108cb="" class="relative"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 inline-block rounded-lg px-2 py-1 text-white font-bold shadow-md">报告对象</div><div class="absolute left-0 top-0 h-4 w-4 transform rounded-full bg-white shadow-md -translate-x-2 -translate-y-2"></div><div class="relative mt-1.5"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 h-[2px] w-full rounded"></div></div></div><div data-v-622108cb="" class="flex flex-col gap-2 my-2"><div data-v-622108cb="" class="flex justify-between border-b pb-2 pl-2"><span data-v-622108cb="" class="text-gray-700 font-bold">姓名</span><span data-v-622108cb="" class="text-gray-600">张*三</span></div><div data-v-622108cb="" class="flex justify-between border-b pb-2 pl-2"><span data-v-622108cb="" class="text-gray-700 font-bold">身份证号</span><span data-v-622108cb="" class="text-gray-600">450000****0000</span></div><!----><!----><!----><!----><!----><div data-v-622108cb="" class="flex justify-between border-b pb-2 pl-2"><span data-v-622108cb="" class="text-gray-700 font-bold">手机号</span><span data-v-622108cb="" class="text-gray-600">137****0000</span></div><!----><!----><!----><!----><div data-v-622108cb="" class="flex flex-col gap-4"><div data-v-622108cb="" class="flex items-center bg-blue-100 rounded-xl px-4 py-2 flex-1"><div data-v-622108cb="" class="bg-green-500 w-12 h-12 text-white text-xl flex items-center justify-center rounded-full mr-4"> ✔ </div><div data-v-622108cb=""><div data-v-622108cb="" class="font-bold text-lg"> 身份证检查结果 </div><div data-v-622108cb="" class="text-sm text-gray-600"> 身份证信息核验通过 </div></div></div><div data-v-622108cb="" class="flex items-center bg-blue-100 rounded-xl px-4 py-2 flex-1"><div data-v-622108cb="" class="bg-green-500 w-12 h-12 text-white text-xl flex items-center justify-center rounded-full mr-4"> ✔ </div><div data-v-622108cb=""><div data-v-622108cb="" class="font-bold text-lg"> 手机号检测结果 </div><div data-v-622108cb="" class="text-sm text-gray-600"> 被查询人姓名与运营商提供的一致 </div><div data-v-622108cb="" class="text-sm text-gray-600"> 被查询人身份证与运营商提供的一致 </div></div></div></div></div></div><div data-v-f2fd578f="" data-v-622108cb="" class="mt-4 flex flex-col items-center gap-2"><button data-v-f2fd578f="" type="button" class="van-button van-button--primary van-button--small !bg-blue-500 !border-blue-500"><div class="van-button__content"><div class="van-button__icon"><i data-v-f2fd578f="" class="van-badge__wrapper van-icon van-icon-share-o"><!----><!----><!----></i></div><span class="van-button__text"> 分享报告</span><!----></div></button><div data-v-f2fd578f="" class="text-xs text-gray-500">分享链接将在7天后过期</div></div></div><div data-v-3b2e7db6="" data-v-622108cb="" class="card my-2"><div data-v-3b2e7db6=""><i data-v-3b2e7db6="" class="van-badge__wrapper van-icon van-icon-info-o tips-icon"><!----><!----><!----></i><span data-v-3b2e7db6="" class="tips-title">温馨提示</span></div><div data-v-3b2e7db6=""><div data-v-3b2e7db6="" class="van-text-ellipsis">如查询的姓名/身份证与运营商提供的不一致，可能会存在报告内容不匹配的情况<!----></div></div></div></div><div data-v-622108cb="" id="van-tab-4" role="tabpanel" class="van-tab__panel" tabindex="0" aria-labelledby="van-tabs-1-2" data-allow-mismatch="attribute"><div data-v-622108cb="" id="lawsuit" class="title mb-4">风险行为扫描</div><div data-v-b16ed4af="" data-v-622108cb="" class="card main-card" params="[object Object]"><div data-v-b16ed4af="" class="risk-content"><div data-v-b16ed4af="" class="summary-card border-red-500 glow-red"><div data-v-b16ed4af="" class="flex items-center"><div data-v-b16ed4af="" class="summary-icon text-red-500"><i data-v-b16ed4af="" class="fas fa-exclamation-triangle"></i></div><div data-v-b16ed4af="" class="font-bold text-lg text-red-500">风险评估总结</div></div><div data-v-b16ed4af="" class="mt-1 text-gray-700">该用户存在较高风险行为，建议进行进一步核实和监控</div></div><div data-v-b16ed4af="" class="grid-container"><div data-v-b16ed4af="" class="grid-left"><div data-v-b16ed4af="" class="risk-section hover-lift"><div data-v-b16ed4af="" class="section-title flex items-center"><div data-v-b16ed4af="" class="title-icon bg-indigo-100 text-indigo-600"><i data-v-b16ed4af="" class="fas fa-user-secret"></i></div><span data-v-b16ed4af="">黑灰产等级</span></div><div data-v-b16ed4af="" class="section-content"><div data-v-b16ed4af="" class="risk-level-indicator"><div data-v-b16ed4af="" class="indicator-label">风险等级</div><div data-v-b16ed4af="" class="indicator-bar"><div data-v-b16ed4af="" class="indicator-value bg-gradient-to-r from-orange-400 to-amber-600" style="width: 75%;"></div></div><div data-v-b16ed4af="" class="indicator-text text-orange-500">高风险</div></div><div data-v-b16ed4af="" class="description">黑灰产等级评估用户是否参与非法活动，等级越高风险越大</div></div></div><div data-v-b16ed4af="" class="risk-section hover-lift"><div data-v-b16ed4af="" class="section-title flex items-center"><div data-v-b16ed4af="" class="title-icon bg-red-100 text-red-600"><i data-v-b16ed4af="" class="fas fa-phone-slash"></i></div><span data-v-b16ed4af="">电诈风险预警</span></div><div data-v-b16ed4af="" class="section-content"><div data-v-b16ed4af="" class="risk-level-indicator"><div data-v-b16ed4af="" class="indicator-label">风险等级</div><div data-v-b16ed4af="" class="indicator-bar"><div data-v-b16ed4af="" class="indicator-value bg-gradient-to-r from-amber-400 to-yellow-500" style="width: 49.8%;"></div></div><div data-v-b16ed4af="" class="indicator-text text-yellow-500">中低风险</div></div><div data-v-b16ed4af="" class="description">电诈风险预警评估用户是否涉及电信诈骗活动，值越大风险越高</div></div></div></div><div data-v-b16ed4af="" class="grid-right"><div data-v-b16ed4af="" class="risk-section hover-lift"><div data-v-b16ed4af="" class="section-title flex items-center"><div data-v-b16ed4af="" class="title-icon bg-amber-100 text-amber-600"><i data-v-b16ed4af="" class="fas fa-users-slash"></i></div><span data-v-b16ed4af="">团伙欺诈排查</span></div><div data-v-b16ed4af="" class="section-content"><div data-v-b16ed4af="" class="flex flex-col md:flex-row gap-3"><div data-v-b16ed4af="" class="risk-level-indicator flex-1"><div data-v-b16ed4af="" class="indicator-label">团伙风险等级</div><div data-v-b16ed4af="" class="indicator-bar"><div data-v-b16ed4af="" class="indicator-value bg-gradient-to-r from-amber-400 to-yellow-500" style="width: 50%;"></div></div><div data-v-b16ed4af="" class="indicator-text text-yellow-500">中风险团伙</div></div><div data-v-b16ed4af="" class="group-size flex-1"><div data-v-b16ed4af="" class="font-medium text-gray-700">团伙规模</div><div data-v-b16ed4af="" class="mt-2 flex items-center"><i data-v-b16ed4af="" class="fas fa-users text-blue-500 mr-2 text-xl"></i><span data-v-b16ed4af="">小规模(少于50人)</span></div></div></div><div data-v-b16ed4af="" class="description mt-1">团伙欺诈排查评估用户是否属于欺诈团伙及团伙规模大小</div></div></div><div data-v-b16ed4af="" class="risk-section hover-lift"><div data-v-b16ed4af="" class="section-title flex items-center"><div data-v-b16ed4af="" class="title-icon bg-purple-100 text-purple-600"><i data-v-b16ed4af="" class="fas fa-dice-slash"></i></div><span data-v-b16ed4af="">反诈反赌核验</span></div><div data-v-b16ed4af="" class="section-content"><div data-v-b16ed4af="" class="grid grid-cols-1 gap-3"><div data-v-b16ed4af="" class="gaming-item border-green-500"><div data-v-b16ed4af="" class="gaming-icon bg-green-100 text-green-500"><i data-v-b16ed4af="" class="fas fa-dice"></i></div><div data-v-b16ed4af="" class="flex-1"><div data-v-b16ed4af="" class="font-medium text-sm">疑似赌博庄家</div><div data-v-b16ed4af="" class="flex items-center mt-2"><div data-v-b16ed4af="" class="progress-container"><div data-v-b16ed4af="" class="progress-bar bg-gradient-to-r from-emerald-400 to-teal-500" style="width: 0%;"></div></div><span data-v-b16ed4af="" class="risk-level-text text-green-500">无风险</span></div></div></div><div data-v-b16ed4af="" class="gaming-item border-red-500"><div data-v-b16ed4af="" class="gaming-icon bg-red-100 text-red-500"><i data-v-b16ed4af="" class="fas fa-money-bill-wave"></i></div><div data-v-b16ed4af="" class="flex-1"><div data-v-b16ed4af="" class="font-medium text-sm">疑似涉赌跑分</div><div data-v-b16ed4af="" class="flex items-center mt-2"><div data-v-b16ed4af="" class="progress-container"><div data-v-b16ed4af="" class="progress-bar bg-gradient-to-r from-amber-400 to-yellow-500" style="width: 42%;"></div></div><span data-v-b16ed4af="" class="risk-level-text text-yellow-500">中等风险</span></div></div></div><div data-v-b16ed4af="" class="gaming-item border-green-500"><div data-v-b16ed4af="" class="gaming-icon bg-green-100 text-green-500"><i data-v-b16ed4af="" class="fas fa-exclamation-triangle"></i></div><div data-v-b16ed4af="" class="flex-1"><div data-v-b16ed4af="" class="font-medium text-sm">疑似欺诈</div><div data-v-b16ed4af="" class="flex items-center mt-2"><div data-v-b16ed4af="" class="progress-container"><div data-v-b16ed4af="" class="progress-bar bg-gradient-to-r from-emerald-400 to-teal-500" style="width: 0%;"></div></div><span data-v-b16ed4af="" class="risk-level-text text-green-500">无风险</span></div></div></div><div data-v-b16ed4af="" class="gaming-item border-red-500"><div data-v-b16ed4af="" class="gaming-icon bg-red-100 text-red-500"><i data-v-b16ed4af="" class="fas fa-gamepad"></i></div><div data-v-b16ed4af="" class="flex-1"><div data-v-b16ed4af="" class="font-medium text-sm">疑似赌博玩家</div><div data-v-b16ed4af="" class="flex items-center mt-2"><div data-v-b16ed4af="" class="progress-container"><div data-v-b16ed4af="" class="progress-bar bg-gradient-to-r from-orange-400 to-amber-600" style="width: 67%;"></div></div><span data-v-b16ed4af="" class="risk-level-text text-orange-500">高风险</span></div></div></div></div><div data-v-b16ed4af="" class="description mt-1">反诈反赌核验评估用户是否有涉及诈骗或赌博活动的风险</div></div></div><div class="security-tips hover-lift" data-v-b16ed4af=""><div class="flex items-center" data-v-b16ed4af=""><div class="title-icon bg-blue-100 text-blue-600 mr-2" data-v-b16ed4af=""><i class="fas fa-lightbulb" data-v-b16ed4af=""></i></div><div class="font-bold text-blue-700" data-v-b16ed4af="">安全建议</div></div><div class="tip-list" data-v-b16ed4af=""><div class="tip-item" data-v-b16ed4af=""><i class="fas fa-check-circle text-green-500 mr-1" data-v-b16ed4af=""></i><span data-v-b16ed4af="">定期更新密码，使用复杂且不易猜测的密码</span></div><div class="tip-item" data-v-b16ed4af=""><i class="fas fa-check-circle text-green-500 mr-1" data-v-b16ed4af=""></i><span data-v-b16ed4af="">开启双因素认证，提高账户安全性</span></div><div class="tip-item" data-v-b16ed4af=""><i class="fas fa-check-circle text-green-500 mr-1" data-v-b16ed4af=""></i><span data-v-b16ed4af="">不点击来源不明的链接或下载不明文件</span></div><div class="tip-item" data-v-b16ed4af=""><i class="fas fa-check-circle text-green-500 mr-1" data-v-b16ed4af=""></i><span data-v-b16ed4af="">不向陌生人透露个人敏感信息</span></div></div></div></div></div></div></div><!----></div><div data-v-622108cb="" id="van-tab-5" role="tabpanel" class="van-tab__panel" tabindex="0" aria-labelledby="van-tabs-1-3" data-allow-mismatch="attribute"><div data-v-622108cb="" id="lawsuit" class="title mb-4">违约失信</div><div data-v-292c8a1d="" data-v-622108cb="" class="card" params="[object Object]"><div data-v-292c8a1d="" class="flex flex-col"><div data-v-292c8a1d="" class="mb-5"><div data-v-292c8a1d="" class="flex items-center justify-between"><div data-v-292c8a1d="" class="text-lg font-bold text-gray-800">借贷违约失信风险查询结果</div><div data-v-292c8a1d="" class="px-3 py-1 rounded-full text-white text-sm font-semibold shadow-sm transform transition-all duration-300 hover:scale-105 bg-gradient-to-r from-red-500 to-red-600">严重风险</div></div><div data-v-292c8a1d="" class="mt-3 p-4 rounded-lg shadow-md border-l-4 transform transition-all duration-300 border-red-500 bg-gradient-to-br from-white to-red-50"><div data-v-292c8a1d="" class="flex items-center"><div data-v-292c8a1d="" class="w-12 h-12 flex-shrink-0 mr-4 flex items-center justify-center rounded-full shadow-inner transform transition-all duration-300 hover:scale-110 bg-red-100 text-red-700"><span data-v-292c8a1d="" class="text-xl font-bold">50</span></div><div data-v-292c8a1d=""><div data-v-292c8a1d="" class="text-base font-medium text-red-700"> 检测到 50 项风险 </div><p data-v-292c8a1d="" class="text-sm text-gray-600 mt-1">存在无法收回风险，请立即处理</p></div></div></div></div><div data-v-292c8a1d="" class="grid grid-cols-4 w-full border-b mb-5"><button data-v-292c8a1d="" class="px-2 py-3 text-center cursor-pointer transition-all duration-300 font-medium text-xs sm:text-sm relative border-b-2 border-blue-500 text-blue-600">汇总 <!----></button><button data-v-292c8a1d="" class="px-2 py-3 text-center cursor-pointer transition-all duration-300 font-medium text-xs sm:text-sm relative border-b-2 border-transparent text-gray-500 hover:text-gray-700">短期逾期 <span data-v-292c8a1d="" class="absolute -top-1 -right-1 inline-flex items-center justify-center w-4 h-4 text-xs font-bold leading-none text-white rounded-full shadow-sm transform transition-all duration-300 hover:scale-110 bg-blue-500">16</span></button><button data-v-292c8a1d="" class="px-2 py-3 text-center cursor-pointer transition-all duration-300 font-medium text-xs sm:text-sm relative border-b-2 border-transparent text-gray-500 hover:text-gray-700">严重逾期 <span data-v-292c8a1d="" class="absolute -top-1 -right-1 inline-flex items-center justify-center w-4 h-4 text-xs font-bold leading-none text-white rounded-full shadow-sm transform transition-all duration-300 hover:scale-110 bg-orange-500">18</span></button><button data-v-292c8a1d="" class="px-2 py-3 text-center cursor-pointer transition-all duration-300 font-medium text-xs sm:text-sm relative border-b-2 border-transparent text-gray-500 hover:text-gray-700">无法收回 <span data-v-292c8a1d="" class="absolute -top-1 -right-1 inline-flex items-center justify-center w-4 h-4 text-xs font-bold leading-none text-white rounded-full shadow-sm transform transition-all duration-300 hover:scale-110 bg-red-500">16</span></button></div><div data-v-292c8a1d="" class="mt-2"><div data-v-292c8a1d="" class="space-y-6"><div data-v-292c8a1d="" class="grid grid-cols-1 gap-4"><div data-v-292c8a1d="" class="p-4 rounded-lg shadow-md border-l-4 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1 cursor-pointer border-blue-500 bg-gradient-to-br from-white to-blue-50"><div data-v-292c8a1d="" class="flex justify-between items-start"><h3 data-v-292c8a1d="" class="text-base font-semibold text-blue-700">短期逾期</h3><span data-v-292c8a1d="" class="text-xs px-2 py-1 rounded-full font-medium shadow-sm bg-blue-100 text-blue-800">已命中</span></div><div data-v-292c8a1d="" class="mt-3 flex items-end justify-between"><div data-v-292c8a1d=""><p data-v-292c8a1d="" class="text-xs text-gray-600">命中项</p><p data-v-292c8a1d="" class="text-xl font-bold text-blue-600">16 / 16</p></div><button data-v-292c8a1d="" class="text-xs px-3 py-1.5 rounded-full focus:outline-none shadow-sm transition-all duration-300 hover:shadow-md bg-blue-100 text-blue-600 hover:bg-blue-200"> 查看详情 </button></div></div><div data-v-292c8a1d="" class="p-4 rounded-lg shadow-md border-l-4 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1 cursor-pointer border-orange-500 bg-gradient-to-br from-white to-orange-50"><div data-v-292c8a1d="" class="flex justify-between items-start"><h3 data-v-292c8a1d="" class="text-base font-semibold text-orange-700">严重逾期</h3><span data-v-292c8a1d="" class="text-xs px-2 py-1 rounded-full font-medium shadow-sm bg-orange-100 text-orange-800">已命中</span></div><div data-v-292c8a1d="" class="mt-3 flex items-end justify-between"><div data-v-292c8a1d=""><p data-v-292c8a1d="" class="text-xs text-gray-600">命中项</p><p data-v-292c8a1d="" class="text-xl font-bold text-orange-600">18 / 20</p></div><button data-v-292c8a1d="" class="text-xs px-3 py-1.5 rounded-full focus:outline-none shadow-sm transition-all duration-300 hover:shadow-md bg-orange-100 text-orange-600 hover:bg-orange-200"> 查看详情 </button></div></div><div data-v-292c8a1d="" class="p-4 rounded-lg shadow-md border-l-4 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1 cursor-pointer border-red-500 bg-gradient-to-br from-white to-red-50"><div data-v-292c8a1d="" class="flex justify-between items-start"><h3 data-v-292c8a1d="" class="text-base font-semibold text-red-700">无法收回</h3><span data-v-292c8a1d="" class="text-xs px-2 py-1 rounded-full font-medium shadow-sm bg-red-100 text-red-800">已命中</span></div><div data-v-292c8a1d="" class="mt-3 flex items-end justify-between"><div data-v-292c8a1d=""><p data-v-292c8a1d="" class="text-xs text-gray-600">命中项</p><p data-v-292c8a1d="" class="text-xl font-bold text-red-600">16 / 16</p></div><button data-v-292c8a1d="" class="text-xs px-3 py-1.5 rounded-full focus:outline-none shadow-sm transition-all duration-300 hover:shadow-md bg-red-100 text-red-600 hover:bg-red-200"> 查看详情 </button></div></div></div><div data-v-292c8a1d="" class=""><h3 data-v-292c8a1d="" class="text-base font-semibold mb-3 text-gray-700 border-l-4 border-gray-400 pl-2">风险主体分布</h3><div data-v-292c8a1d="" class="space-y-3"><div data-v-292c8a1d="" class="p-3 bg-white rounded-lg border border-gray-200 shadow-sm relative overflow-hidden transition-all duration-300 hover:shadow-md"><div data-v-292c8a1d="" class="flex justify-between items-center"><div data-v-292c8a1d="" class="text-sm font-medium text-gray-900">身份证风险</div><div data-v-292c8a1d="" class="flex items-center space-x-1"><span data-v-292c8a1d="" class="text-xs text-gray-500">命中项</span><span data-v-292c8a1d="" class="text-xs font-medium px-1.5 py-0.5 rounded-full bg-red-100 text-red-700">26</span><span data-v-292c8a1d="" class="text-xs text-gray-500">/</span><span data-v-292c8a1d="" class="text-xs text-gray-500">26</span></div></div><div data-v-292c8a1d="" class="w-full h-2 bg-gray-100 rounded-full mt-2 overflow-hidden"><div data-v-292c8a1d="" class="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-red-400 to-red-500" style="width: 100%;"></div></div></div><div data-v-292c8a1d="" class="p-3 bg-white rounded-lg border border-gray-200 shadow-sm relative overflow-hidden transition-all duration-300 hover:shadow-md"><div data-v-292c8a1d="" class="flex justify-between items-center"><div data-v-292c8a1d="" class="text-sm font-medium text-gray-900">手机号风险</div><div data-v-292c8a1d="" class="flex items-center space-x-1"><span data-v-292c8a1d="" class="text-xs text-gray-500">命中项</span><span data-v-292c8a1d="" class="text-xs font-medium px-1.5 py-0.5 rounded-full bg-red-100 text-red-700">24</span><span data-v-292c8a1d="" class="text-xs text-gray-500">/</span><span data-v-292c8a1d="" class="text-xs text-gray-500">26</span></div></div><div data-v-292c8a1d="" class="w-full h-2 bg-gray-100 rounded-full mt-2 overflow-hidden"><div data-v-292c8a1d="" class="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-red-400 to-red-500" style="width: 92.3077%;"></div></div></div></div></div><div data-v-292c8a1d="" class=""><h3 data-v-292c8a1d="" class="text-base font-semibold mb-3 text-gray-700 border-l-4 border-gray-400 pl-2">机构风险分布</h3><div data-v-292c8a1d="" class="grid grid-cols-2 gap-3"><div data-v-292c8a1d="" class="flex flex-col p-3 rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:shadow-md"><div data-v-292c8a1d="" class="flex items-center mb-2"><div data-v-292c8a1d="" class="mr-2 flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full shadow-inner bg-orange-100"><span data-v-292c8a1d="" class="text-sm font-bold text-orange-600">1</span></div><div data-v-292c8a1d="" class="flex flex-col"><h4 data-v-292c8a1d="" class="text-xs font-medium text-gray-900 truncate max-w-[100px]">法院失信人</h4><div data-v-292c8a1d="" class="flex items-center mt-1 text-xs text-gray-500"><span data-v-292c8a1d="">命中项：1/2</span></div></div></div><div data-v-292c8a1d="" class="w-full h-1.5 bg-gray-100 rounded-full"><div data-v-292c8a1d="" class="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-orange-400 to-orange-500" style="width: 50%;"></div></div></div><div data-v-292c8a1d="" class="flex flex-col p-3 rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:shadow-md"><div data-v-292c8a1d="" class="flex items-center mb-2"><div data-v-292c8a1d="" class="mr-2 flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full shadow-inner bg-orange-100"><span data-v-292c8a1d="" class="text-sm font-bold text-orange-600">1</span></div><div data-v-292c8a1d="" class="flex flex-col"><h4 data-v-292c8a1d="" class="text-xs font-medium text-gray-900 truncate max-w-[100px]">法院被执行人</h4><div data-v-292c8a1d="" class="flex items-center mt-1 text-xs text-gray-500"><span data-v-292c8a1d="">命中项：1/2</span></div></div></div><div data-v-292c8a1d="" class="w-full h-1.5 bg-gray-100 rounded-full"><div data-v-292c8a1d="" class="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-orange-400 to-orange-500" style="width: 50%;"></div></div></div><div data-v-292c8a1d="" class="flex flex-col p-3 rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:shadow-md"><div data-v-292c8a1d="" class="flex items-center mb-2"><div data-v-292c8a1d="" class="mr-2 flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full shadow-inner bg-red-100"><span data-v-292c8a1d="" class="text-sm font-bold text-red-600">6</span></div><div data-v-292c8a1d="" class="flex flex-col"><h4 data-v-292c8a1d="" class="text-xs font-medium text-gray-900 truncate max-w-[100px]">银行(含信用卡)</h4><div data-v-292c8a1d="" class="flex items-center mt-1 text-xs text-gray-500"><span data-v-292c8a1d="">命中项：6/6</span></div></div></div><div data-v-292c8a1d="" class="w-full h-1.5 bg-gray-100 rounded-full"><div data-v-292c8a1d="" class="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-red-400 to-red-500" style="width: 100%;"></div></div></div><div data-v-292c8a1d="" class="flex flex-col p-3 rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:shadow-md"><div data-v-292c8a1d="" class="flex items-center mb-2"><div data-v-292c8a1d="" class="mr-2 flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full shadow-inner bg-red-100"><span data-v-292c8a1d="" class="text-sm font-bold text-red-600">6</span></div><div data-v-292c8a1d="" class="flex flex-col"><h4 data-v-292c8a1d="" class="text-xs font-medium text-gray-900 truncate max-w-[100px]">非银机构</h4><div data-v-292c8a1d="" class="flex items-center mt-1 text-xs text-gray-500"><span data-v-292c8a1d="">命中项：6/6</span></div></div></div><div data-v-292c8a1d="" class="w-full h-1.5 bg-gray-100 rounded-full"><div data-v-292c8a1d="" class="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-red-400 to-red-500" style="width: 100%;"></div></div></div><div data-v-292c8a1d="" class="flex flex-col p-3 rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:shadow-md"><div data-v-292c8a1d="" class="flex items-center mb-2"><div data-v-292c8a1d="" class="mr-2 flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full shadow-inner bg-red-100"><span data-v-292c8a1d="" class="text-sm font-bold text-red-600">6</span></div><div data-v-292c8a1d="" class="flex flex-col"><h4 data-v-292c8a1d="" class="text-xs font-medium text-gray-900 truncate max-w-[100px]">持牌网络小贷</h4><div data-v-292c8a1d="" class="flex items-center mt-1 text-xs text-gray-500"><span data-v-292c8a1d="">命中项：6/6</span></div></div></div><div data-v-292c8a1d="" class="w-full h-1.5 bg-gray-100 rounded-full"><div data-v-292c8a1d="" class="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-red-400 to-red-500" style="width: 100%;"></div></div></div><div data-v-292c8a1d="" class="flex flex-col p-3 rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:shadow-md"><div data-v-292c8a1d="" class="flex items-center mb-2"><div data-v-292c8a1d="" class="mr-2 flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full shadow-inner bg-red-100"><span data-v-292c8a1d="" class="text-sm font-bold text-red-600">6</span></div><div data-v-292c8a1d="" class="flex flex-col"><h4 data-v-292c8a1d="" class="text-xs font-medium text-gray-900 truncate max-w-[100px]">持牌小贷</h4><div data-v-292c8a1d="" class="flex items-center mt-1 text-xs text-gray-500"><span data-v-292c8a1d="">命中项：6/6</span></div></div></div><div data-v-292c8a1d="" class="w-full h-1.5 bg-gray-100 rounded-full"><div data-v-292c8a1d="" class="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-red-400 to-red-500" style="width: 100%;"></div></div></div><div data-v-292c8a1d="" class="flex flex-col p-3 rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:shadow-md"><div data-v-292c8a1d="" class="flex items-center mb-2"><div data-v-292c8a1d="" class="mr-2 flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full shadow-inner bg-red-100"><span data-v-292c8a1d="" class="text-sm font-bold text-red-600">6</span></div><div data-v-292c8a1d="" class="flex flex-col"><h4 data-v-292c8a1d="" class="text-xs font-medium text-gray-900 truncate max-w-[100px]">持牌消费金融</h4><div data-v-292c8a1d="" class="flex items-center mt-1 text-xs text-gray-500"><span data-v-292c8a1d="">命中项：6/6</span></div></div></div><div data-v-292c8a1d="" class="w-full h-1.5 bg-gray-100 rounded-full"><div data-v-292c8a1d="" class="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-red-400 to-red-500" style="width: 100%;"></div></div></div><div data-v-292c8a1d="" class="flex flex-col p-3 rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:shadow-md"><div data-v-292c8a1d="" class="flex items-center mb-2"><div data-v-292c8a1d="" class="mr-2 flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full shadow-inner bg-red-100"><span data-v-292c8a1d="" class="text-sm font-bold text-red-600">6</span></div><div data-v-292c8a1d="" class="flex flex-col"><h4 data-v-292c8a1d="" class="text-xs font-medium text-gray-900 truncate max-w-[100px]">持牌融资租赁</h4><div data-v-292c8a1d="" class="flex items-center mt-1 text-xs text-gray-500"><span data-v-292c8a1d="">命中项：6/6</span></div></div></div><div data-v-292c8a1d="" class="w-full h-1.5 bg-gray-100 rounded-full"><div data-v-292c8a1d="" class="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-red-400 to-red-500" style="width: 100%;"></div></div></div><div data-v-292c8a1d="" class="flex flex-col p-3 rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:shadow-md"><div data-v-292c8a1d="" class="flex items-center mb-2"><div data-v-292c8a1d="" class="mr-2 flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full shadow-inner bg-red-100"><span data-v-292c8a1d="" class="text-sm font-bold text-red-600">6</span></div><div data-v-292c8a1d="" class="flex flex-col"><h4 data-v-292c8a1d="" class="text-xs font-medium text-gray-900 truncate max-w-[100px]">持牌汽车金融</h4><div data-v-292c8a1d="" class="flex items-center mt-1 text-xs text-gray-500"><span data-v-292c8a1d="">命中项：6/6</span></div></div></div><div data-v-292c8a1d="" class="w-full h-1.5 bg-gray-100 rounded-full"><div data-v-292c8a1d="" class="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-red-400 to-red-500" style="width: 100%;"></div></div></div><div data-v-292c8a1d="" class="flex flex-col p-3 rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:shadow-md"><div data-v-292c8a1d="" class="flex items-center mb-2"><div data-v-292c8a1d="" class="mr-2 flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full shadow-inner bg-red-100"><span data-v-292c8a1d="" class="text-sm font-bold text-red-600">6</span></div><div data-v-292c8a1d="" class="flex flex-col"><h4 data-v-292c8a1d="" class="text-xs font-medium text-gray-900 truncate max-w-[100px]">其他</h4><div data-v-292c8a1d="" class="flex items-center mt-1 text-xs text-gray-500"><span data-v-292c8a1d="">命中项：6/6</span></div></div></div><div data-v-292c8a1d="" class="w-full h-1.5 bg-gray-100 rounded-full"><div data-v-292c8a1d="" class="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-red-400 to-red-500" style="width: 100%;"></div></div></div></div></div></div></div></div></div><!----></div><div data-v-622108cb="" id="van-tab-6" role="tabpanel" class="van-tab__panel" tabindex="0" aria-labelledby="van-tabs-1-4" data-allow-mismatch="attribute"><div data-v-622108cb="" id="lawsuit" class="title mb-4">司法涉诉</div><div data-v-35e2c4aa="" data-v-622108cb="" class="card shadow-sm rounded-xl overflow-hidden" params="[object Object]"><div data-v-35e2c4aa="" class="relative mb-4"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 inline-block rounded-lg px-2 py-1 text-white font-bold shadow-md">涉诉风险分析</div><div class="absolute left-0 top-0 h-4 w-4 transform rounded-full bg-white shadow-md -translate-x-2 -translate-y-2"></div><div class="relative mt-1.5"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 h-[2px] w-full rounded"></div></div></div><div data-v-35e2c4aa="" class="mb-6"><div class="mb-6"><div class="flex items-center"><div class="h-8 w-1 bg-gradient-to-b from-blue-500 to-blue-700 rounded-full mr-3"></div><span class="text-lg font-medium text-gray-800">涉诉风险整体概览</span></div><div class="mt-1 h-px bg-gradient-to-r from-blue-200 via-gray-200 to-transparent"></div><div class="mt-4 p-3 bg-gradient-to-r from-blue-50 to-white rounded-lg border border-blue-100 shadow-sm"><div class="flex"><div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg></div><div><div class="text-sm text-gray-600"><span class="font-medium">7</span> 起涉诉案件中， <span class="text-red-600 font-medium">7</span> 起高风险案件 <span class="ml-1"> ，涉及 3 种案件类型 </span></div></div></div></div></div><div class="grid grid-cols-2 md:grid-cols-4 gap-5 mb-6"><div class="bg-gradient-to-br from-orange-50 to-orange-100 p-5 rounded-xl shadow-sm border-0 flex flex-col items-center justify-center hover:shadow-md transition-all duration-300 relative overflow-hidden group"><div class="absolute -right-8 -top-8 w-20 h-20 bg-orange-200 rounded-full opacity-30 group-hover:scale-110 transition-transform duration-300"></div><div class="text-center z-10"><div class="text-4xl font-bold text-orange-500 mb-1">7</div><div class="text-sm font-medium text-gray-700 mb-1">风险事项</div><div class="text-xs text-gray-500">平均1.0项/案件</div></div><div class="absolute top-3 left-3 w-6 h-6 rounded-full bg-orange-200 flex items-center justify-center text-orange-500"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg></div></div><div class="bg-gradient-to-br from-red-50 to-red-100 p-5 rounded-xl shadow-sm border-0 flex flex-col items-center justify-center hover:shadow-md transition-all duration-300 relative overflow-hidden group"><div class="absolute -right-8 -top-8 w-20 h-20 bg-red-200 rounded-full opacity-30 group-hover:scale-110 transition-transform duration-300"></div><div class="text-center z-10"><div class="text-4xl font-bold text-red-600 mb-1">7</div><div class="text-sm font-medium text-gray-700 mb-1">高风险案件</div><div class="text-xs text-gray-500">占比100.0%</div><div class="flex items-center justify-center gap-3 mt-1"><span class="text-xs text-red-600">失信 2</span><span class="text-xs text-amber-600">限高 2</span></div></div><div class="absolute top-3 left-3 w-6 h-6 rounded-full bg-red-200 flex items-center justify-center text-red-600"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4zm3 1h6v4H7V5zm8 8v2h1v1H4v-1h1v-2a1 1 0 011-1h8a1 1 0 011 1z" clip-rule="evenodd"></path></svg></div></div><div class="bg-gradient-to-br from-blue-50 to-blue-100 p-5 rounded-xl shadow-sm border-0 flex flex-col items-center justify-center hover:shadow-md transition-all duration-300 relative overflow-hidden group"><div class="absolute -right-8 -top-8 w-20 h-20 bg-blue-200 rounded-full opacity-30 group-hover:scale-110 transition-transform duration-300"></div><div class="text-center z-10"><div class="text-4xl font-bold text-blue-500 mb-1">3</div><div class="text-sm font-medium text-gray-700 mb-1">已结案件</div><div class="text-xs text-gray-500">占比42.9%</div></div><div class="absolute top-3 left-3 w-6 h-6 rounded-full bg-blue-200 flex items-center justify-center text-blue-500"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg></div></div><div class="bg-gradient-to-br from-indigo-50 to-indigo-100 p-5 rounded-xl shadow-sm border-0 flex flex-col items-center justify-center hover:shadow-md transition-all duration-300 relative overflow-hidden group"><div class="absolute -right-8 -top-8 w-20 h-20 bg-indigo-200 rounded-full opacity-30 group-hover:scale-110 transition-transform duration-300"></div><div class="text-center z-10"><div class="text-4xl font-bold text-indigo-500 mb-1">3</div><div class="text-sm font-medium text-gray-700 mb-1">案件类型</div><div class="text-xs text-gray-500">涉及多种类型</div></div><div class="absolute top-3 left-3 w-6 h-6 rounded-full bg-indigo-200 flex items-center justify-center text-indigo-500"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd"></path></svg></div></div></div><div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"><div class="bg-white rounded-xl p-4 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300"><div class="text-sm font-medium text-gray-700 mb-3 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-500" viewBox="0 0 20 20" fill="currentColor"><path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path></svg> 案件类型分布 </div><div class="h-[200px]"><x-vue-echarts class="echarts" style="user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); position: relative;" _echarts_instance_="ec_1753582256500"><div style="position: relative; width: 186px; height: 200px; padding: 0px; margin: 0px; border-width: 0px; cursor: default;"><canvas data-zr-dom-id="zr_0" width="232" height="250" style="position: absolute; left: 0px; top: 0px; width: 186px; height: 200px; user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 0px; margin: 0px; border-width: 0px;"></canvas></div><div class="" style="position: absolute; display: block; border-style: solid; white-space: nowrap; z-index: 9999999; box-shadow: rgba(0, 0, 0, 0.2) 1px 2px 10px; transition: opacity 0.2s cubic-bezier(0.23, 1, 0.32, 1), visibility 0.2s cubic-bezier(0.23, 1, 0.32, 1), transform 0.4s cubic-bezier(0.23, 1, 0.32, 1); background-color: rgb(255, 255, 255); border-width: 1px; border-radius: 4px; color: rgb(102, 102, 102); font: 14px / 21px &quot;Microsoft YaHei&quot;; padding: 10px; top: 0px; left: 0px; transform: translate3d(13px, 101px, 0px); border-color: rgb(255, 255, 255); pointer-events: none; visibility: hidden; opacity: 0;">非诉保全审查: 0件</div></x-vue-echarts></div></div><div class="bg-white rounded-xl p-4 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300"><div class="text-sm font-medium text-gray-700 mb-3 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-500" viewBox="0 0 20 20" fill="currentColor"><path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path><path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path></svg> 风险等级分布 </div><div class="space-y-3"><div class="flex items-center justify-between"><div class="text-xs text-gray-500">高风险案件</div><div class="text-xs font-medium text-red-600">7 起</div></div><div class="w-full h-2 bg-gray-100 rounded-full overflow-hidden"><div class="h-full bg-gradient-to-r from-red-400 to-red-600 rounded-full" style="width: 100%;"></div></div><div class="flex items-center justify-between"><div class="text-xs text-gray-500">中风险案件</div><div class="text-xs font-medium text-amber-600">0 起</div></div><div class="w-full h-2 bg-gray-100 rounded-full overflow-hidden"><div class="h-full bg-gradient-to-r from-amber-400 to-amber-600 rounded-full" style="width: 0%;"></div></div><div class="flex items-center justify-between"><div class="text-xs text-gray-500">低风险案件</div><div class="text-xs font-medium text-green-600">0 起</div></div><div class="w-full h-2 bg-gray-100 rounded-full overflow-hidden"><div class="h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full" style="width: 0%;"></div></div></div></div></div></div><div data-v-35e2c4aa="" class="mb-4"><div data-v-35e2c4aa="" class="van-tabs van-tabs--line"><div class="van-tabs__wrap"><div role="tablist" class="van-tabs__nav van-tabs__nav--line van-tabs__nav--complete" aria-orientation="horizontal"><!----><div id="van-tabs-10-0" role="tab" class="van-tab van-tab--line van-tab--grow van-tab--active" tabindex="0" aria-selected="true" aria-controls="van-tab-11" data-allow-mismatch="attribute"><span class="van-tab__text"><div data-v-35e2c4aa="" class="flex items-center gap-1"><span data-v-35e2c4aa="">全部风险</span><span data-v-35e2c4aa="" class="bg-red-50 text-red-600 text-xs min-w-5 h-5 rounded-full flex items-center justify-center ml-1 px-1">7</span></div></span></div><div id="van-tabs-10-1" role="tab" class="van-tab van-tab--line van-tab--grow" tabindex="-1" aria-selected="false" aria-controls="van-tab-12" data-allow-mismatch="attribute"><span class="van-tab__text"><div data-v-35e2c4aa="" class="flex items-center gap-1"><span data-v-35e2c4aa="">失信被执行</span><span data-v-35e2c4aa="" class="bg-red-50 text-red-600 text-xs min-w-5 h-5 rounded-full flex items-center justify-center ml-1 px-1">2</span></div></span></div><div id="van-tabs-10-2" role="tab" class="van-tab van-tab--line van-tab--grow" tabindex="-1" aria-selected="false" aria-controls="van-tab-13" data-allow-mismatch="attribute"><span class="van-tab__text"><div data-v-35e2c4aa="" class="flex items-center gap-1"><span data-v-35e2c4aa="">限高被执行</span><span data-v-35e2c4aa="" class="bg-orange-50 text-orange-600 text-xs min-w-5 h-5 rounded-full flex items-center justify-center ml-1 px-1">2</span></div></span></div><div id="van-tabs-10-3" role="tab" class="van-tab van-tab--line van-tab--grow" tabindex="-1" aria-selected="false" aria-controls="van-tab-14" data-allow-mismatch="attribute"><span class="van-tab__text"><div data-v-35e2c4aa="" class="flex items-center gap-1"><span data-v-35e2c4aa="">刑事案件</span><span data-v-35e2c4aa="" class="bg-red-50 text-red-600 text-xs min-w-5 h-5 rounded-full flex items-center justify-center ml-1 px-1">3</span></div></span></div><div id="van-tabs-10-4" role="tab" class="van-tab van-tab--line van-tab--grow" tabindex="-1" aria-selected="false" aria-controls="van-tab-15" data-allow-mismatch="attribute"><span class="van-tab__text"><div data-v-35e2c4aa="" class="flex items-center gap-1"><span data-v-35e2c4aa="">民事案件</span><span data-v-35e2c4aa="" class="bg-blue-50 text-blue-600 text-xs min-w-5 h-5 rounded-full flex items-center justify-center ml-1 px-1">0</span></div></span></div><div id="van-tabs-10-5" role="tab" class="van-tab van-tab--line van-tab--grow" tabindex="-1" aria-selected="false" aria-controls="van-tab-16" data-allow-mismatch="attribute"><span class="van-tab__text"><div data-v-35e2c4aa="" class="flex items-center gap-1"><span data-v-35e2c4aa="">行政案件</span><span data-v-35e2c4aa="" class="bg-purple-50 text-purple-600 text-xs min-w-5 h-5 rounded-full flex items-center justify-center ml-1 px-1">0</span></div></span></div><div id="van-tabs-10-6" role="tab" class="van-tab van-tab--line van-tab--grow" tabindex="-1" aria-selected="false" aria-controls="van-tab-17" data-allow-mismatch="attribute"><span class="van-tab__text"><div data-v-35e2c4aa="" class="flex items-center gap-1"><span data-v-35e2c4aa="">执行案件</span><span data-v-35e2c4aa="" class="bg-orange-50 text-orange-600 text-xs min-w-5 h-5 rounded-full flex items-center justify-center ml-1 px-1">0</span></div></span></div><div id="van-tabs-10-7" role="tab" class="van-tab van-tab--line van-tab--grow" tabindex="-1" aria-selected="false" aria-controls="van-tab-18" data-allow-mismatch="attribute"><span class="van-tab__text"><div data-v-35e2c4aa="" class="flex items-center gap-1"><span data-v-35e2c4aa="">强制清算与破产案件</span><span data-v-35e2c4aa="" class="bg-rose-50 text-rose-600 text-xs min-w-5 h-5 rounded-full flex items-center justify-center ml-1 px-1">0</span></div></span></div><div id="van-tabs-10-8" role="tab" class="van-tab van-tab--line van-tab--grow" tabindex="-1" aria-selected="false" aria-controls="van-tab-19" data-allow-mismatch="attribute"><span class="van-tab__text"><div data-v-35e2c4aa="" class="flex items-center gap-1"><span data-v-35e2c4aa="">非诉保全审查</span><span data-v-35e2c4aa="" class="bg-amber-50 text-amber-600 text-xs min-w-5 h-5 rounded-full flex items-center justify-center ml-1 px-1">0</span></div></span></div><div class="van-tabs__line" style="width: 30px; transform: translateX(62px) translateX(-50%); transition-duration: 0.3s;"></div><!----></div></div><!----><div class="van-tabs__content van-tabs__content--animated"><div class="van-swipe van-tabs__track"><div class="van-swipe__track" style="transition-duration: 0ms; transform: translateX(0px); width: 3648px;"><!----><div data-v-35e2c4aa="" class="van-swipe-item van-tab__panel-wrapper van-tab__panel-wrapper--inactive" id="van-tab-12" role="tabpanel" tabindex="-1" aria-hidden="true" aria-labelledby="van-tabs-10-1" data-allow-mismatch="attribute" style="width: 456px;"><div class="van-tab__panel"><!----></div></div><div data-v-35e2c4aa="" class="van-swipe-item van-tab__panel-wrapper van-tab__panel-wrapper--inactive" id="van-tab-13" role="tabpanel" tabindex="-1" aria-hidden="true" aria-labelledby="van-tabs-10-2" data-allow-mismatch="attribute" style="width: 456px;"><div class="van-tab__panel"><!----></div></div><div data-v-35e2c4aa="" class="van-swipe-item van-tab__panel-wrapper van-tab__panel-wrapper--inactive" id="van-tab-14" role="tabpanel" tabindex="-1" aria-hidden="true" aria-labelledby="van-tabs-10-3" data-allow-mismatch="attribute" style="width: 456px;"><div class="van-tab__panel"><!----></div></div><div data-v-35e2c4aa="" class="van-swipe-item van-tab__panel-wrapper van-tab__panel-wrapper--inactive" id="van-tab-15" role="tabpanel" tabindex="-1" aria-hidden="true" aria-labelledby="van-tabs-10-4" data-allow-mismatch="attribute" style="width: 456px;"><div class="van-tab__panel"><!----></div></div><div data-v-35e2c4aa="" class="van-swipe-item van-tab__panel-wrapper van-tab__panel-wrapper--inactive" id="van-tab-16" role="tabpanel" tabindex="-1" aria-hidden="true" aria-labelledby="van-tabs-10-5" data-allow-mismatch="attribute" style="width: 456px;"><div class="van-tab__panel"><!----></div></div><div data-v-35e2c4aa="" class="van-swipe-item van-tab__panel-wrapper van-tab__panel-wrapper--inactive" id="van-tab-17" role="tabpanel" tabindex="-1" aria-hidden="true" aria-labelledby="van-tabs-10-6" data-allow-mismatch="attribute" style="width: 456px;"><div class="van-tab__panel"><!----></div></div><div data-v-35e2c4aa="" class="van-swipe-item van-tab__panel-wrapper van-tab__panel-wrapper--inactive" id="van-tab-18" role="tabpanel" tabindex="-1" aria-hidden="true" aria-labelledby="van-tabs-10-7" data-allow-mismatch="attribute" style="width: 456px;"><div class="van-tab__panel"><!----></div></div><div data-v-35e2c4aa="" class="van-swipe-item van-tab__panel-wrapper van-tab__panel-wrapper--inactive" id="van-tab-19" role="tabpanel" tabindex="-1" aria-hidden="true" aria-labelledby="van-tabs-10-8" data-allow-mismatch="attribute" style="width: 456px;"><div class="van-tab__panel"><!----></div></div></div><!----></div></div></div></div><div data-v-35e2c4aa="" class="space-y-4"><div data-v-35e2c4aa="" class="case-wrapper"><div data-v-35e2c4aa="" class="bg-white rounded-lg shadow-sm border overflow-hidden border-gray-200 hover:border-red-300 hover:shadow-md transition-all duration-300 cursor-pointer relative"><div data-v-35e2c4aa="" class="bg-gradient-to-r from-red-50 to-red-100 px-4 py-2 flex items-center"><div data-v-35e2c4aa="" class="font-medium text-gray-800 truncate">失信被执行 - (2016)*******8160号</div></div><div data-v-35e2c4aa="" class="px-4 py-2 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2"><div data-v-35e2c4aa="" class="flex flex-wrap gap-1"><span data-v-35e2c4aa="" class="px-2 py-0.5 text-xs rounded-full text-red-600 bg-red-50">高风险</span><!----></div><div data-v-35e2c4aa="" class="flex items-center gap-2"><div data-v-35e2c4aa="" class="flex items-center text-xs text-gray-500"><svg data-v-35e2c4aa="" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path data-v-35e2c4aa="" fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg> 立案: 2016年08月08日</div></div></div><div data-v-35e2c4aa="" class="absolute right-4 bottom-2 flex items-center text-xs text-gray-500 hover:text-red-600 transition-colors duration-300"><span data-v-35e2c4aa="" class="mr-1">查看详情</span><div data-v-35e2c4aa="" class="w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center transition-transform duration-300"><svg data-v-35e2c4aa="" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path data-v-35e2c4aa="" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></div></div></div><div data-v-35e2c4aa="" class="overflow-hidden transition-all duration-500 ease-in-out max-h-0 opacity-0"><div data-v-35e2c4aa="" class="mt-2 bg-white rounded-xl overflow-hidden p-3 border border-gray-200 shadow-sm transform transition-all duration-300 max-h-[800px] overflow-y-auto hover:shadow-md"><div data-v-35e2c4aa="" class="bg-gray-50 rounded-xl p-3 border border-gray-100 hover:border-gray-300 transition-all duration-300 relative overflow-hidden group"><div class="absolute top-0 right-0 w-24 h-24 opacity-5 transform rotate-45 translate-x-10 -translate-y-10 group-hover:opacity-10 transition-opacity duration-300 bg-red-600 bg-red-600"></div><div class="flex flex-wrap justify-between items-center mb-3 pb-2 border-b border-dashed border-gray-200"><div class="font-medium flex items-center gap-2 mr-2 truncate"><span class="text-gray-800 truncate">(2016)*******8160号</span><!----></div><div class="flex items-center gap-3 flex-shrink-0"><div class="flex items-center text-xs text-gray-500 whitespace-nowrap"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg> 立案: 2016年08月08日</div><div class="flex items-center text-xs text-gray-500 whitespace-nowrap"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg> 发布: 2016年11月09日</div></div></div><div class="rounded-lg space-y-3"><div class="grid grid-cols-1 sm:grid-cols-2 gap-3"><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.496 2.132a1 1 0 00-.992 0l-7 4A1 1 0 003 8v7a1 1 0 100 2h14a1 1 0 100-2V8a1 1 0 00.496-1.868l-7-4zM6 9a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1zm3 1a1 1 0 012 0v3a1 1 0 11-2 0v-3zm5-1a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg> 执行法院</div><div class="text-sm font-medium">北京市*****人民法院</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path></svg> 所属地域 </div><div class="text-sm font-medium">北京</div></div></div><!----><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg> 立案时间 </div><div class="text-sm font-medium">2016年08月08日</div></div><!----><!----><!----><!----><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg> 执行依据文号 </div><div class="text-sm font-medium">2015年*****12658号</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"></path></svg> 执行依据单位 </div><div class="text-sm font-medium">北京市****人民法院</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg> 生成法律文书确定的义务 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100">判决如下： 一、被告张某于本判决生效后十日内偿还原告兵器装备集团财务有限责任公司贷款本金二万一千零三十一元四角七分及逾期利息（截止至二Ｏ一四年十二月二十三日，逾期利息为四千八百五十四元三角五分，自二Ｏ一四年十二月二十四日起至贷款全部清偿之日止的逾期利息按《汽车消费贷款合同》及其附件约定计算）； 二、原告兵器装备集团财务有限责任公司对被告张某所有的重庆长安汽车股份有限公司生产的长安牌小型轿车一辆（车架号LS5A*******02800、发动机号CC4*****967）折价或拍卖、变卖后所得的价款享有优先受偿权； 三、驳回原告兵器装备集团财务有限责任公司其他诉讼请求。 如果被告张某未按本判决指定的期间履行给付金钱义务，应当依照《中华人民共和国民事诉讼法》第二百五十三条之规定，加倍支付迟延履行期间的债务利息。 案件受理费三百七十四元（原告兵器装备集团财务有限责任公司已预交），由原告兵器装备集团财务有限责任公司负担一百一十八元，已交纳；由被告张某负担二百五十六元，于本判决书生效后七日内交纳。 如不服本判决，可在判决书送达之日起十五日内向本院递交上诉状，并按对方当事人的人数提出副本，按照不服一审判决部分的上诉请求数额交纳上诉案件受理费，上诉于北京市第一中级人民法院。如在上诉期满后七日内未交纳上诉费的，按自动撤回上诉处理。</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg> 履行情况 </div><div class="text-sm font-medium">全部未履行</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg> 行为情形 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100">其他有履行能力而拒不履行生效法律文书确定义务</div></div><!----><!----><!----></div></div></div></div></div><div data-v-35e2c4aa="" class="case-wrapper"><div data-v-35e2c4aa="" class="bg-white rounded-lg shadow-sm border overflow-hidden border-gray-200 hover:border-red-300 hover:shadow-md transition-all duration-300 cursor-pointer relative border-red-400 shadow-md"><div data-v-35e2c4aa="" class="bg-gradient-to-r from-red-50 to-red-100 px-4 py-2 flex items-center"><div data-v-35e2c4aa="" class="font-medium text-gray-800 truncate">失信被执行 - (2016)**0211执****号</div></div><div data-v-35e2c4aa="" class="px-4 py-2 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2"><div data-v-35e2c4aa="" class="flex flex-wrap gap-1"><span data-v-35e2c4aa="" class="px-2 py-0.5 text-xs rounded-full text-red-600 bg-red-50">高风险</span><!----></div><div data-v-35e2c4aa="" class="flex items-center gap-2"><div data-v-35e2c4aa="" class="flex items-center text-xs text-gray-500"><svg data-v-35e2c4aa="" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path data-v-35e2c4aa="" fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg> 立案: 2016年09月05日</div></div></div><div data-v-35e2c4aa="" class="absolute right-4 bottom-2 flex items-center text-xs text-gray-500 hover:text-red-600 transition-colors duration-300 text-red-600"><span data-v-35e2c4aa="" class="mr-1">收起详情</span><div data-v-35e2c4aa="" class="w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center transition-transform duration-300 bg-red-100 text-red-600 rotate-180"><svg data-v-35e2c4aa="" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path data-v-35e2c4aa="" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></div></div></div><div data-v-35e2c4aa="" class="overflow-hidden transition-all duration-500 ease-in-out max-h-[2000px] opacity-100"><div data-v-35e2c4aa="" class="mt-2 bg-white rounded-xl overflow-hidden p-3 border border-gray-200 shadow-sm transform transition-all duration-300 max-h-[800px] overflow-y-auto hover:shadow-md"><div data-v-35e2c4aa="" class="bg-gray-50 rounded-xl p-3 border border-gray-100 hover:border-gray-300 transition-all duration-300 relative overflow-hidden group"><div class="absolute top-0 right-0 w-24 h-24 opacity-5 transform rotate-45 translate-x-10 -translate-y-10 group-hover:opacity-10 transition-opacity duration-300 bg-red-600 bg-red-600"></div><div class="flex flex-wrap justify-between items-center mb-3 pb-2 border-b border-dashed border-gray-200"><div class="font-medium flex items-center gap-2 mr-2 truncate"><span class="text-gray-800 truncate">(2016)**0211执****号</span><!----></div><div class="flex items-center gap-3 flex-shrink-0"><div class="flex items-center text-xs text-gray-500 whitespace-nowrap"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg> 立案: 2016年09月05日</div><div class="flex items-center text-xs text-gray-500 whitespace-nowrap"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg> 发布: 2016年09月14日</div></div></div><div class="rounded-lg space-y-3"><div class="grid grid-cols-1 sm:grid-cols-2 gap-3"><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.496 2.132a1 1 0 00-.992 0l-7 4A1 1 0 003 8v7a1 1 0 100 2h14a1 1 0 100-2V8a1 1 0 00.496-1.868l-7-4zM6 9a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1zm3 1a1 1 0 012 0v3a1 1 0 11-2 0v-3zm5-1a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg> 执行法院</div><div class="text-sm font-medium">**市**区人民法院</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path></svg> 所属地域 </div><div class="text-sm font-medium">**省</div></div></div><!----><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg> 立案时间 </div><div class="text-sm font-medium">2016年09月05日</div></div><!----><!----><!----><!----><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg> 执行依据文号 </div><div class="text-sm font-medium">(2016)**0211民初****号</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"></path></svg> 执行依据单位 </div><div class="text-sm font-medium">**市**区人民法院</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg> 生成法律文书确定的义务 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100">被执行人支付欠款***元...</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg> 履行情况 </div><div class="text-sm font-medium">全部未履行</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg> 行为情形 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100">其他有履行能力而拒不履行生效法律文书确定义务</div></div><!----><!----><!----></div></div></div></div></div><div data-v-35e2c4aa="" class="case-wrapper"><div data-v-35e2c4aa="" class="bg-white rounded-lg shadow-sm border overflow-hidden border-gray-200 hover:border-red-300 hover:shadow-md transition-all duration-300 cursor-pointer relative"><div data-v-35e2c4aa="" class="bg-gradient-to-r from-red-50 to-red-100 px-4 py-2 flex items-center"><div data-v-35e2c4aa="" class="font-medium text-gray-800 truncate">限高被执行 - (20*****77号</div></div><div data-v-35e2c4aa="" class="px-4 py-2 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2"><div data-v-35e2c4aa="" class="flex flex-wrap gap-1"><span data-v-35e2c4aa="" class="px-2 py-0.5 text-xs rounded-full text-red-600 bg-red-50">高风险</span><!----></div><div data-v-35e2c4aa="" class="flex items-center gap-2"><!----></div></div><div data-v-35e2c4aa="" class="absolute right-4 bottom-2 flex items-center text-xs text-gray-500 hover:text-red-600 transition-colors duration-300"><span data-v-35e2c4aa="" class="mr-1">查看详情</span><div data-v-35e2c4aa="" class="w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center transition-transform duration-300"><svg data-v-35e2c4aa="" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path data-v-35e2c4aa="" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></div></div></div><div data-v-35e2c4aa="" class="overflow-hidden transition-all duration-500 ease-in-out max-h-0 opacity-0"><div data-v-35e2c4aa="" class="mt-2 bg-white rounded-xl overflow-hidden p-3 border border-gray-200 shadow-sm transform transition-all duration-300 max-h-[800px] overflow-y-auto hover:shadow-md"><div data-v-35e2c4aa="" class="bg-gray-50 rounded-xl p-3 border border-gray-100 hover:border-gray-300 transition-all duration-300 relative overflow-hidden group"><div class="absolute top-0 right-0 w-24 h-24 opacity-5 transform rotate-45 translate-x-10 -translate-y-10 group-hover:opacity-10 transition-opacity duration-300 bg-orange-600 bg-orange-600"></div><div class="flex flex-wrap justify-between items-center mb-3 pb-2 border-b border-dashed border-gray-200"><div class="font-medium flex items-center gap-2 mr-2 truncate"><span class="text-gray-800 truncate">(20*****77号</span><!----></div><div class="flex items-center gap-3 flex-shrink-0"><!----><div class="flex items-center text-xs text-gray-500 whitespace-nowrap"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg> 发布: 2018年08月31日</div></div></div><div class="rounded-lg space-y-3"><div class="grid grid-cols-1 sm:grid-cols-2 gap-3"><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.496 2.132a1 1 0 00-.992 0l-7 4A1 1 0 003 8v7a1 1 0 100 2h14a1 1 0 100-2V8a1 1 0 00.496-1.868l-7-4zM6 9a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1zm3 1a1 1 0 012 0v3a1 1 0 11-2 0v-3zm5-1a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg> 执行法院</div><div class="text-sm font-medium">*****人民法院</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path></svg> 所属地域 </div><div class="text-sm font-medium">—</div></div></div><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div></div></div></div></div><div data-v-35e2c4aa="" class="case-wrapper"><div data-v-35e2c4aa="" class="bg-white rounded-lg shadow-sm border overflow-hidden border-gray-200 hover:border-red-300 hover:shadow-md transition-all duration-300 cursor-pointer relative"><div data-v-35e2c4aa="" class="bg-gradient-to-r from-red-50 to-red-100 px-4 py-2 flex items-center"><div data-v-35e2c4aa="" class="font-medium text-gray-800 truncate">限高被执行 - (2016)*****574号</div></div><div data-v-35e2c4aa="" class="px-4 py-2 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2"><div data-v-35e2c4aa="" class="flex flex-wrap gap-1"><span data-v-35e2c4aa="" class="px-2 py-0.5 text-xs rounded-full text-red-600 bg-red-50">高风险</span><!----></div><div data-v-35e2c4aa="" class="flex items-center gap-2"><!----></div></div><div data-v-35e2c4aa="" class="absolute right-4 bottom-2 flex items-center text-xs text-gray-500 hover:text-red-600 transition-colors duration-300"><span data-v-35e2c4aa="" class="mr-1">查看详情</span><div data-v-35e2c4aa="" class="w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center transition-transform duration-300"><svg data-v-35e2c4aa="" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path data-v-35e2c4aa="" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></div></div></div><div data-v-35e2c4aa="" class="overflow-hidden transition-all duration-500 ease-in-out max-h-0 opacity-0"><div data-v-35e2c4aa="" class="mt-2 bg-white rounded-xl overflow-hidden p-3 border border-gray-200 shadow-sm transform transition-all duration-300 max-h-[800px] overflow-y-auto hover:shadow-md"><div data-v-35e2c4aa="" class="bg-gray-50 rounded-xl p-3 border border-gray-100 hover:border-gray-300 transition-all duration-300 relative overflow-hidden group"><div class="absolute top-0 right-0 w-24 h-24 opacity-5 transform rotate-45 translate-x-10 -translate-y-10 group-hover:opacity-10 transition-opacity duration-300 bg-orange-600 bg-orange-600"></div><div class="flex flex-wrap justify-between items-center mb-3 pb-2 border-b border-dashed border-gray-200"><div class="font-medium flex items-center gap-2 mr-2 truncate"><span class="text-gray-800 truncate">(2016)*****574号</span><!----></div><div class="flex items-center gap-3 flex-shrink-0"><!----><div class="flex items-center text-xs text-gray-500 whitespace-nowrap"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg> 发布: 2018年08月31日</div></div></div><div class="rounded-lg space-y-3"><div class="grid grid-cols-1 sm:grid-cols-2 gap-3"><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.496 2.132a1 1 0 00-.992 0l-7 4A1 1 0 003 8v7a1 1 0 100 2h14a1 1 0 100-2V8a1 1 0 00.496-1.868l-7-4zM6 9a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1zm3 1a1 1 0 012 0v3a1 1 0 11-2 0v-3zm5-1a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg> 执行法院</div><div class="text-sm font-medium">*******法院</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path></svg> 所属地域 </div><div class="text-sm font-medium">—</div></div></div><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div></div></div></div></div><div data-v-35e2c4aa="" class="case-wrapper"><div data-v-35e2c4aa="" class="bg-white rounded-lg shadow-sm border overflow-hidden border-gray-200 hover:border-red-300 hover:shadow-md transition-all duration-300 cursor-pointer relative"><div data-v-35e2c4aa="" class="bg-gradient-to-r from-red-50 to-red-100 px-4 py-2 flex items-center"><div data-v-35e2c4aa="" class="font-medium text-gray-800 truncate">刑事案件 - （2016）桂****刑初**号</div></div><div data-v-35e2c4aa="" class="px-4 py-2 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2"><div data-v-35e2c4aa="" class="flex flex-wrap gap-1"><span data-v-35e2c4aa="" class="px-2 py-0.5 text-xs rounded-full text-red-600 bg-red-50">高风险</span><span data-v-35e2c4aa="" class="px-2 py-0.5 text-xs rounded-full bg-green-50 text-green-600">已结案</span></div><div data-v-35e2c4aa="" class="flex items-center gap-2"><div data-v-35e2c4aa="" class="flex items-center text-xs text-gray-500"><svg data-v-35e2c4aa="" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path data-v-35e2c4aa="" fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg> 立案: 2016年01月04日</div></div></div><div data-v-35e2c4aa="" class="absolute right-4 bottom-2 flex items-center text-xs text-gray-500 hover:text-red-600 transition-colors duration-300"><span data-v-35e2c4aa="" class="mr-1">查看详情</span><div data-v-35e2c4aa="" class="w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center transition-transform duration-300"><svg data-v-35e2c4aa="" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path data-v-35e2c4aa="" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></div></div></div><div data-v-35e2c4aa="" class="overflow-hidden transition-all duration-500 ease-in-out max-h-0 opacity-0"><div data-v-35e2c4aa="" class="mt-2 bg-white rounded-xl overflow-hidden p-3 border border-gray-200 shadow-sm transform transition-all duration-300 max-h-[800px] overflow-y-auto hover:shadow-md"><div data-v-35e2c4aa="" class="bg-gray-50 rounded-xl p-3 border border-gray-100 hover:border-gray-300 transition-all duration-300 relative overflow-hidden group"><div class="absolute top-0 right-0 w-24 h-24 opacity-5 transform rotate-45 translate-x-10 -translate-y-10 group-hover:opacity-10 transition-opacity duration-300 bg-red-600 bg-red-600"></div><div class="flex flex-wrap justify-between items-center mb-3 pb-2 border-b border-dashed border-gray-200"><div class="font-medium flex items-center gap-2 mr-2 truncate"><span class="text-gray-800 truncate">（2016）桂****刑初**号</span><span class="ml-2 text-xs px-2 py-0.5 rounded-full font-medium flex-shrink-0 bg-green-50 text-green-600">已结案</span></div><div class="flex items-center gap-3 flex-shrink-0"><div class="flex items-center text-xs text-gray-500 whitespace-nowrap"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg> 立案: 2016年01月04日</div><div class="flex items-center text-xs text-gray-500 whitespace-nowrap"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg> 结案: 2016年04月01日</div></div></div><div class="rounded-lg space-y-3"><div class="grid grid-cols-1 sm:grid-cols-2 gap-3"><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.496 2.132a1 1 0 00-.992 0l-7 4A1 1 0 003 8v7a1 1 0 100 2h14a1 1 0 100-2V8a1 1 0 00.496-1.868l-7-4zM6 9a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1zm3 1a1 1 0 012 0v3a1 1 0 11-2 0v-3zm5-1a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg> 经办法院</div><div class="text-sm font-medium">******县人民法院</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path></svg> 所属地域 </div><div class="text-sm font-medium">广西壮族自治区</div></div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path><path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path></svg> 案件类型 </div><div class="text-sm font-medium">刑事一审</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg> 立案时间 </div><div class="text-sm font-medium">2016年01月04日</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg> 立案案由 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100">妨害社会管理秩序罪,扰乱公共秩序罪,开设赌场罪</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-1 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path></svg> 当事人信息 </div><div class="flex flex-wrap gap-1.5"><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">被告人:</span><span class="font-medium text-gray-800">何某</span></div><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">被告人:</span><span class="font-medium text-gray-800">覃某</span></div><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">被告人:</span><span class="font-medium text-gray-800">刘某</span></div><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">被告人:</span><span class="font-medium text-gray-800">陈某</span></div><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">被告人:</span><span class="font-medium text-gray-800">覃某</span></div><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">被告人:</span><span class="font-medium text-gray-800">陈某</span></div></div></div><!----><div class="space-y-3"><div class="flex items-center pt-1"><div class="flex-grow h-px bg-gradient-to-r from-transparent via-green-200 to-transparent"></div><div class="mx-2 px-3 py-1 text-xs font-medium text-green-600 bg-green-50 rounded-full flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg> 案件已结 </div><div class="flex-grow h-px bg-gradient-to-r from-green-200 via-transparent to-transparent"></div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg> 结案方式 </div><div class="text-sm font-medium">判决</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg> 结案时间 </div><div class="text-sm font-medium">2016年04月01日</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg> 结案案由 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100">妨害社会管理秩序罪,扰乱公共秩序罪,开设赌场罪</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path></svg> 相关当事人 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100">公诉机关广西******人民检察院。被告人何某,男,1979年7月13日出生于广西壮族自治区******县,汉族,小学文化,农民,住广西壮族自治区******县。因涉嫌犯开设赌场罪于2015年9月13日被刑事拘留,同年10月20日被逮捕。被告人陈某,男,1987年7月17日出生于广西壮族自治区******县,汉族,小学文化,农民,住广西壮族自治区******县。因涉嫌犯开设赌场罪于2015年9月16日被刑事拘留,同年10月20日被逮捕。被告人覃某,女,1979年4月15日出生于广西壮族自治区******县,汉族,初中文化,农民,住广西壮族自治区******县。因涉嫌犯开设赌场罪于2015年11月4日被羁押,次日被刑事拘留,同月26日被逮捕。被告人覃某有,男,1972年9月21日出生于广西壮族自治区******县,汉族,小学文化,农民,住广西壮族自治区******县。因涉嫌犯开设赌场罪于2015年9月17日被刑事拘留,同年10月20日被逮捕。被告人刘某飞,男,1991年4月3日出生于广西壮族自治区******县,汉族,初中文化,农民,住广西壮族自治区******县。因涉嫌犯开设赌场罪于2015年10月28日被刑事拘留,同年11月12日被逮捕。被告人陈某观,男,1990年12月2日出生于广西壮族自治区******县,汉族,初中文化,农民,住广西壮族自治区******县。因涉嫌犯开设赌场罪于2015年10月28日被刑事拘留,同年11月12日被逮捕。</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 1.944A11.954 11.954 0 012.166 5C2.056 5.649 2 6.319 2 7c0 5.225 3.34 9.67 8 11.317C14.66 16.67 18 12.225 18 7c0-.682-.057-1.35-.166-2.001A11.954 11.954 0 0110 1.944zm-4.707 3.05a1 1 0 00.707.293h2a1 1 0 00.707-.293l.708-.707a1 1 0 000-1.414l-.708-.707a1 1 0 00-.707-.293h-2a1 1 0 00-.707.293l-.708.707a1 1 0 000 1.414l.708.707z" clip-rule="evenodd"></path></svg> 判决结果 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100 whitespace-pre-line">一、被告人何某犯开设赌场罪,判处有期徒刑一年六个月,并处罚金人民币二万元。(刑期从判决执行之日起计算,判决执行以前先行羁押的,羁押一日折抵刑期一日,即自2015年9月13日起至2017年3月12日止。罚金在本判决生效后一个月内一次缴纳,期满不缴纳的,强制缴纳。)二、被告人陈某犯开设赌场罪,判处有期徒刑一年二个月,并处罚金人民币二万元。(刑期从判决执行之日起计算,判决执行以前先行羁押的,羁押一日折抵刑期一日,即自2015年9月16日起至2016年11月15日止。罚金在本判决生效后一个月内一次缴纳,期满不缴纳的,强制缴纳。)三、被告人覃某犯开设赌场罪,判处有期徒刑一年二个月,并处罚金人民币二万元。(刑期从判决执行之日起计算,判决执行以前先行羁押的,羁押一日折抵刑期一日,即自2015年11月4日起至2017年1月3日止。罚金在本判决生效后一个月内一次缴纳,期满不缴纳的,强制缴纳。)四、被告人覃某有犯开设赌场罪,判处有期徒刑八个月,并处罚金人民币二万元。(刑期从判决执行之日起计算,判决执行以前先行羁押的,羁押一日折抵刑期一日,即自2015年9月17日起至2016年5月16日止。罚金已缴纳。)五、被告人刘某飞犯开设赌场罪,判处有期徒刑六个月,并处罚金人民币一万五千元。(刑期从判决执行之日起计算,判决执行以前先行羁押的,羁押一日折抵刑期一日,即自2015年10月28日起至2016年4月27日止。罚金已缴纳。)六、被告人陈某犯开设赌场罪,判处有期徒刑六个月,并处罚金人民币一万五千元。(刑期从判决执行之日起计算,判决执行以前先行羁押的,羁押一日折抵刑期一日,即自2015年10月28日起至2016年4月27日止。罚金已缴纳。)如不服本判决,可在收到判决书之次日起十日内,通过本院或直接向广西壮族自治区*******法院提出上诉。书面上诉的应提交上诉状正本一份,副本十三份。</div></div></div><!----></div></div></div></div></div><div data-v-35e2c4aa="" class="case-wrapper"><div data-v-35e2c4aa="" class="bg-white rounded-lg shadow-sm border overflow-hidden border-gray-200 hover:border-red-300 hover:shadow-md transition-all duration-300 cursor-pointer relative"><div data-v-35e2c4aa="" class="bg-gradient-to-r from-red-50 to-red-100 px-4 py-2 flex items-center"><div data-v-35e2c4aa="" class="font-medium text-gray-800 truncate">刑事案件 - （2016）桂09刑终283号</div></div><div data-v-35e2c4aa="" class="px-4 py-2 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2"><div data-v-35e2c4aa="" class="flex flex-wrap gap-1"><span data-v-35e2c4aa="" class="px-2 py-0.5 text-xs rounded-full text-red-600 bg-red-50">高风险</span><span data-v-35e2c4aa="" class="px-2 py-0.5 text-xs rounded-full bg-green-50 text-green-600">已结案</span></div><div data-v-35e2c4aa="" class="flex items-center gap-2"><div data-v-35e2c4aa="" class="flex items-center text-xs text-gray-500"><svg data-v-35e2c4aa="" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path data-v-35e2c4aa="" fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg> 立案: 2016年05月16日</div></div></div><div data-v-35e2c4aa="" class="absolute right-4 bottom-2 flex items-center text-xs text-gray-500 hover:text-red-600 transition-colors duration-300"><span data-v-35e2c4aa="" class="mr-1">查看详情</span><div data-v-35e2c4aa="" class="w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center transition-transform duration-300"><svg data-v-35e2c4aa="" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path data-v-35e2c4aa="" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></div></div></div><div data-v-35e2c4aa="" class="overflow-hidden transition-all duration-500 ease-in-out max-h-0 opacity-0"><div data-v-35e2c4aa="" class="mt-2 bg-white rounded-xl overflow-hidden p-3 border border-gray-200 shadow-sm transform transition-all duration-300 max-h-[800px] overflow-y-auto hover:shadow-md"><div data-v-35e2c4aa="" class="bg-gray-50 rounded-xl p-3 border border-gray-100 hover:border-gray-300 transition-all duration-300 relative overflow-hidden group"><div class="absolute top-0 right-0 w-24 h-24 opacity-5 transform rotate-45 translate-x-10 -translate-y-10 group-hover:opacity-10 transition-opacity duration-300 bg-red-600 bg-red-600"></div><div class="flex flex-wrap justify-between items-center mb-3 pb-2 border-b border-dashed border-gray-200"><div class="font-medium flex items-center gap-2 mr-2 truncate"><span class="text-gray-800 truncate">（2016）桂09刑终283号</span><span class="ml-2 text-xs px-2 py-0.5 rounded-full font-medium flex-shrink-0 bg-green-50 text-green-600">已结案</span></div><div class="flex items-center gap-3 flex-shrink-0"><div class="flex items-center text-xs text-gray-500 whitespace-nowrap"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg> 立案: 2016年05月16日</div><div class="flex items-center text-xs text-gray-500 whitespace-nowrap"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg> 结案: 2016年07月14日</div></div></div><div class="rounded-lg space-y-3"><div class="grid grid-cols-1 sm:grid-cols-2 gap-3"><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.496 2.132a1 1 0 00-.992 0l-7 4A1 1 0 003 8v7a1 1 0 100 2h14a1 1 0 100-2V8a1 1 0 00.496-1.868l-7-4zM6 9a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1zm3 1a1 1 0 012 0v3a1 1 0 11-2 0v-3zm5-1a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg> 经办法院</div><div class="text-sm font-medium">广西壮族自治区*******法院</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path></svg> 所属地域 </div><div class="text-sm font-medium">广西壮族自治区</div></div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path><path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path></svg> 案件类型 </div><div class="text-sm font-medium">刑事二审</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg> 立案时间 </div><div class="text-sm font-medium">2016年05月16日</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg> 立案案由 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100">妨害社会管理秩序罪,扰乱公共秩序罪,开设赌场罪</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-1 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path></svg> 当事人信息 </div><div class="flex flex-wrap gap-1.5"><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">其他:</span><span class="font-medium text-gray-800">何某</span></div><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">其他:</span><span class="font-medium text-gray-800">胡某</span></div><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">其他:</span><span class="font-medium text-gray-800">张某</span></div><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">上诉人:</span><span class="font-medium text-gray-800">李某</span></div><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">上诉人:</span><span class="font-medium text-gray-800">刘某</span></div><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">上诉人:</span><span class="font-medium text-gray-800">陈某</span></div></div></div><!----><div class="space-y-3"><div class="flex items-center pt-1"><div class="flex-grow h-px bg-gradient-to-r from-transparent via-green-200 to-transparent"></div><div class="mx-2 px-3 py-1 text-xs font-medium text-green-600 bg-green-50 rounded-full flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg> 案件已结 </div><div class="flex-grow h-px bg-gradient-to-r from-green-200 via-transparent to-transparent"></div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg> 结案方式 </div><div class="text-sm font-medium">改判</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg> 结案时间 </div><div class="text-sm font-medium">2016年07月14日</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg> 结案案由 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100">妨害社会管理秩序罪,扰乱公共秩序罪,开设赌场罪</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path></svg> 相关当事人 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100">原公诉机关广西壮族自治区******县人民检察院。上诉人(原审被告人)李某,农民。因涉嫌犯开设赌场罪于2015年9月13日被刑事拘留,同年10月20日被逮捕。现羁押于******县看守所。上诉人(原审被告人)陈某(曾用名陈东东),农民。因涉嫌犯开设赌场罪于2015年9月16日被刑事拘留,同年10月20日被逮捕。现羁押于******县看守所。上诉人(原审被告人)刘某,农民。因涉嫌犯开设赌场罪于2015年11月4日被羁押,次日被刑事拘留,同月26日被逮捕。现羁押于******县看守所。原审被告人胡某,农民。因涉嫌犯开设赌场罪于2015年9月17日被刑事拘留,同年10月20日被逮捕。现羁押于******县看守所。原审被告人何某,农民。因涉嫌犯开设赌场罪于2015年10月28日被刑事拘留,同年11月12日被逮捕。现羁押于******县看守所。原审被告人张某,农民。因涉嫌犯开设赌场罪于2015年10月28日被刑事拘留,同年11月12日被逮捕。现羁押于******县看守所。</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 1.944A11.954 11.954 0 012.166 5C2.056 5.649 2 6.319 2 7c0 5.225 3.34 9.67 8 11.317C14.66 16.67 18 12.225 18 7c0-.682-.057-1.35-.166-2.001A11.954 11.954 0 0110 1.944zm-4.707 3.05a1 1 0 00.707.293h2a1 1 0 00.707-.293l.708-.707a1 1 0 000-1.414l-.708-.707a1 1 0 00-.707-.293h-2a1 1 0 00-.707.293l-.708.707a1 1 0 000 1.414l.708.707z" clip-rule="evenodd"></path></svg> 判决结果 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100 whitespace-pre-line">一、维持广西壮族自治区******县人民法院(2016)桂0923刑初90号刑事判决的第四、第五、第六项,即:被告人胡某犯开设赌场罪,判处有期徒刑八个月,并处罚金人民币二万元。被告人何某犯开设赌场罪,判处有期徒刑六个月,并处罚金人民币一万五千元。被告人张某犯开设赌场罪,判处有期徒刑六个月,并处罚金人民币一万五千元。二、撤销广西壮族自治区******县人民法院(2016)桂0923刑初90号刑事判决的第一、第二、第三项,即:被告人李某犯开设赌场罪,判处有期徒刑一年六个月,并处罚金人民币二万元。被告人陈某犯开设赌场罪,判处有期徒刑一年二个月,并处罚金人民币二万元。被告人刘某犯开设赌场罪,判处有期徒刑一年二个月,并处罚金人民币二万元。三、上诉人(原审被告人)李某犯开设赌场罪,判处有期徒刑一年,并处罚金人民币二万元。(刑期从判决执行之日起计算。判决执行前先行羁押的,羁押一日折抵刑期一日,即自2015年9月13日起至2016年9月12日止。罚金已缴纳。)四、上诉人(原审被告人)陈某犯开设赌场罪,判处有期徒刑十个月,并处罚金人民币二万元。(刑期从判决执行之日起计算。判决执行前先行羁押的,羁押一日折抵刑期一日,即自2015年9月16日起至2016年7月15日止;已缴纳罚金一万元,罚金余款自判决生效之次日起一个月内缴纳,逾期不缴纳的,强制缴纳。)五、上诉人(原审被告人)刘某犯开设赌场罪,判处有期徒刑十个月,并处罚金人民币二万元。(刑期从判决执行之日起计算。判决执行前先行羁押的,羁押一日折抵刑期一日,即自2015年11月4日起至2016年9月3日止;已缴纳罚金一万元,罚金余款自判决生效之次日起一个月内缴纳,逾期不缴纳的,强制缴纳。)本判决为终审判决。</div></div></div><!----></div></div></div></div></div><div data-v-35e2c4aa="" class="case-wrapper"><div data-v-35e2c4aa="" class="bg-white rounded-lg shadow-sm border overflow-hidden border-gray-200 hover:border-red-300 hover:shadow-md transition-all duration-300 cursor-pointer relative"><div data-v-35e2c4aa="" class="bg-gradient-to-r from-red-50 to-red-100 px-4 py-2 flex items-center"><div data-v-35e2c4aa="" class="font-medium text-gray-800 truncate">刑事案件 - （2019）桂0923刑初81号</div></div><div data-v-35e2c4aa="" class="px-4 py-2 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2"><div data-v-35e2c4aa="" class="flex flex-wrap gap-1"><span data-v-35e2c4aa="" class="px-2 py-0.5 text-xs rounded-full text-red-600 bg-red-50">高风险</span><span data-v-35e2c4aa="" class="px-2 py-0.5 text-xs rounded-full bg-green-50 text-green-600">已结案</span></div><div data-v-35e2c4aa="" class="flex items-center gap-2"><div data-v-35e2c4aa="" class="flex items-center text-xs text-gray-500"><svg data-v-35e2c4aa="" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path data-v-35e2c4aa="" fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg> 立案: 2019年02月20日</div></div></div><div data-v-35e2c4aa="" class="absolute right-4 bottom-2 flex items-center text-xs text-gray-500 hover:text-red-600 transition-colors duration-300"><span data-v-35e2c4aa="" class="mr-1">查看详情</span><div data-v-35e2c4aa="" class="w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center transition-transform duration-300"><svg data-v-35e2c4aa="" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path data-v-35e2c4aa="" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></div></div></div><div data-v-35e2c4aa="" class="overflow-hidden transition-all duration-500 ease-in-out max-h-0 opacity-0"><div data-v-35e2c4aa="" class="mt-2 bg-white rounded-xl overflow-hidden p-3 border border-gray-200 shadow-sm transform transition-all duration-300 max-h-[800px] overflow-y-auto hover:shadow-md"><div data-v-35e2c4aa="" class="bg-gray-50 rounded-xl p-3 border border-gray-100 hover:border-gray-300 transition-all duration-300 relative overflow-hidden group"><div class="absolute top-0 right-0 w-24 h-24 opacity-5 transform rotate-45 translate-x-10 -translate-y-10 group-hover:opacity-10 transition-opacity duration-300 bg-red-600 bg-red-600"></div><div class="flex flex-wrap justify-between items-center mb-3 pb-2 border-b border-dashed border-gray-200"><div class="font-medium flex items-center gap-2 mr-2 truncate"><span class="text-gray-800 truncate">（2019）桂0923刑初81号</span><span class="ml-2 text-xs px-2 py-0.5 rounded-full font-medium flex-shrink-0 bg-green-50 text-green-600">已结案</span></div><div class="flex items-center gap-3 flex-shrink-0"><div class="flex items-center text-xs text-gray-500 whitespace-nowrap"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg> 立案: 2019年02月20日</div><div class="flex items-center text-xs text-gray-500 whitespace-nowrap"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg> 结案: 2019年03月07日</div></div></div><div class="rounded-lg space-y-3"><div class="grid grid-cols-1 sm:grid-cols-2 gap-3"><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.496 2.132a1 1 0 00-.992 0l-7 4A1 1 0 003 8v7a1 1 0 100 2h14a1 1 0 100-2V8a1 1 0 00.496-1.868l-7-4zM6 9a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1zm3 1a1 1 0 012 0v3a1 1 0 11-2 0v-3zm5-1a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg> 经办法院</div><div class="text-sm font-medium">******县人民法院</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path></svg> 所属地域 </div><div class="text-sm font-medium">广西壮族自治区</div></div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path><path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path></svg> 案件类型 </div><div class="text-sm font-medium">刑事一审</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg> 立案时间 </div><div class="text-sm font-medium">2019年02月20日</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg> 立案案由 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100">妨害社会管理秩序罪,扰乱公共秩序罪,开设赌场罪</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-1 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path></svg> 当事人信息 </div><div class="flex flex-wrap gap-1.5"><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">被告人:</span><span class="font-medium text-gray-800">李某</span></div><div class="text-xs px-2 py-1 bg-gray-50 rounded-lg flex items-center border border-gray-100 hover:border-gray-300 transition-colors duration-200"><span class="text-gray-600 mr-1">被告人:</span><span class="font-medium text-gray-800">张某</span></div></div></div><!----><div class="space-y-3"><div class="flex items-center pt-1"><div class="flex-grow h-px bg-gradient-to-r from-transparent via-green-200 to-transparent"></div><div class="mx-2 px-3 py-1 text-xs font-medium text-green-600 bg-green-50 rounded-full flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg> 案件已结 </div><div class="flex-grow h-px bg-gradient-to-r from-green-200 via-transparent to-transparent"></div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg> 结案方式 </div><div class="text-sm font-medium">判决</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg> 结案时间 </div><div class="text-sm font-medium">2019年03月07日</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg> 结案案由 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100">妨害社会管理秩序罪,扰乱公共秩序罪,开设赌场罪</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path></svg> 相关当事人 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100">公诉机关广西壮族自治区******县人民检察院。被告人李某。被告人张某。</div></div><div class="bg-white rounded-lg p-2 shadow-sm"><div class="text-xs text-gray-500 mb-0.5 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 1.944A11.954 11.954 0 012.166 5C2.056 5.649 2 6.319 2 7c0 5.225 3.34 9.67 8 11.317C14.66 16.67 18 12.225 18 7c0-.682-.057-1.35-.166-2.001A11.954 11.954 0 0110 1.944zm-4.707 3.05a1 1 0 00.707.293h2a1 1 0 00.707-.293l.708-.707a1 1 0 000-1.414l-.708-.707a1 1 0 00-.707-.293h-2a1 1 0 00-.707.293l-.708.707a1 1 0 000 1.414l.708.707z" clip-rule="evenodd"></path></svg> 判决结果 </div><div class="text-sm bg-gray-50 p-1.5 rounded border border-gray-100 whitespace-pre-line">一、被告人李某犯开设赌场罪,判处有期徒刑二年,并处罚金人民币二万元。(刑期从判决执行之日起计算,判决执行以前先行羁押的,羁押一日折抵刑期一日,即自2018年10月16日起至2020年10月15日止。罚金在本判决生效后一个月内一次缴纳,期满不缴纳的,强制缴纳。)%1、被告人张某犯开设赌场罪,判处有期徒刑一年六个月,并处罚金人民币一万五千元。(刑期从判决执行之日起计算,判决执行以前先行羁押的,羁押一日折抵刑期一日,即自2018年10月30日起至2020年4月29日止。罚金在本判决生效后一个月内一次缴纳,期满不缴纳的,强制缴纳。)三、扣押在案的赌具扑克牌、龙虎珠、骨牌及人民币八百三十五元依法予以没收,其中人民币八百三十五元上缴国库。如不服本判决,可在收到判决书之次日起十日内,通过本院或直接向广西壮族自治区*******法院提出上诉。书面上诉的应当提交上诉状正本一份,副本九份。</div></div></div><!----></div></div></div></div></div></div></div><!----></div><div data-v-622108cb="" id="van-tab-7" role="tabpanel" class="van-tab__panel" tabindex="0" aria-labelledby="van-tabs-1-5" data-allow-mismatch="attribute"><div data-v-622108cb="" id="lawsuit" class="title mb-4">手机号码风险</div><div data-v-cf57e1a9="" data-v-622108cb="" class="card"><!----><!----><div data-v-cf57e1a9="" class="bg-yellow-100 text-yellow-700 p-4 rounded-lg"><h3 data-v-cf57e1a9="" class="text-xl font-semibold">中危</h3><p data-v-cf57e1a9="" class="text-sm">该手机号码存在一定风险，请留意相关信息。</p></div><!----></div><!----></div><div data-v-622108cb="" id="van-tab-8" role="tabpanel" class="van-tab__panel" tabindex="0" aria-labelledby="van-tabs-1-6" data-allow-mismatch="attribute"><div data-v-622108cb="" id="lawsuit" class="title mb-4">借贷申请记录</div><!----><!----></div><div data-v-622108cb="" id="van-tab-9" role="tabpanel" class="van-tab__panel" tabindex="0" aria-labelledby="van-tabs-1-7" data-allow-mismatch="attribute"><div data-v-622108cb="" id="lawsuit" class="title mb-4">借贷行为记录</div><div data-v-44e08373="" data-v-622108cb="" class="card" params="[object Object]"><div data-v-44e08373="" class="flex flex-col gap-y-4"><div data-v-44e08373="" class="p-6 bg-white rounded-lg shadow-sm border border-gray-100 relative overflow-hidden mb-4"><div data-v-44e08373="" class="absolute top-0 right-0 w-32 h-32 bg-blue-50 rounded-full -mr-8 -mt-8 opacity-60"></div><div data-v-44e08373="" class="absolute bottom-0 left-0 w-20 h-20 bg-green-50 rounded-full -ml-10 -mb-10 opacity-50"></div><div data-v-44e08373="" class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 relative z-10"><div data-v-44e08373="" class="space-y-2"><h2 data-v-44e08373="" class="text-xl font-semibold text-gray-800 flex items-center">借贷行为分析报告</h2><p data-v-44e08373="" class="text-sm text-gray-600 ml-6">本报告统计审批额度与应还情况，帮助评估信贷风险</p></div><div data-v-44e08373="" class="flex flex-wrap gap-6 w-full md:w-auto"><div data-v-44e08373="" class="flex-1 md:flex-none flex rounded-md shadow-sm relative"><button data-v-44e08373="" type="button" class="flex-1 py-2 px-4 text-sm font-medium rounded-l-md border transition-all duration-200 flex items-center bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-500 shadow-md"><svg data-v-44e08373="" class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path data-v-44e08373="" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> 身份证数据 </button><button data-v-44e08373="" type="button" class="flex-1 py-2 px-4 text-sm font-medium rounded-r-md border transition-all duration-200 flex items-center bg-white text-gray-700 border-gray-300 hover:bg-gray-50"><svg data-v-44e08373="" class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path data-v-44e08373="" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg> 手机号数据 </button></div></div></div><div data-v-44e08373="" class="mt-4 bg-blue-50 p-3 rounded-lg text-xs text-gray-700"><p class="font-medium text-blue-800 mb-1" data-v-44e08373="">数据类型说明：</p><div class="grid grid-cols-1 md:grid-cols-2 gap-2" data-v-44e08373=""><div class="flex items-start" data-v-44e08373=""><span class="inline-block w-2 h-2 mt-1 mr-2 rounded-full flex-shrink-0 bg-blue-500" data-v-44e08373=""></span><span data-v-44e08373=""><strong data-v-44e08373="">身份证数据：</strong>通过身份证号码匹配获取的借贷记录，反映与身份证关联的所有借贷行为</span></div><div class="flex items-start" data-v-44e08373=""><span class="inline-block w-2 h-2 mt-1 mr-2 rounded-full flex-shrink-0 bg-green-500" data-v-44e08373=""></span><span data-v-44e08373=""><strong data-v-44e08373="">手机号数据：</strong>通过手机号码匹配获取的借贷记录，反映与手机号关联的所有借贷行为</span></div></div></div></div><div data-v-44e08373="" class="relative"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 inline-block rounded-lg px-2 py-1 text-white font-bold shadow-md">近期审批额度</div><div class="absolute left-0 top-0 h-4 w-4 transform rounded-full bg-white shadow-md -translate-x-2 -translate-y-2"></div><div class="relative mt-1.5"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 h-[2px] w-full rounded"></div></div></div><div data-v-44e08373="" class="chart-container" style="user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); position: relative;" _echarts_instance_="ec_1753582256501"><div style="position: relative; width: 456px; height: 300px; padding: 0px; margin: 0px; border-width: 0px; cursor: default;"><canvas data-zr-dom-id="zr_0" width="570" height="375" style="position: absolute; left: 0px; top: 0px; width: 456px; height: 300px; user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 0px; margin: 0px; border-width: 0px;"></canvas></div><div class=""></div></div><div data-v-44e08373="" class="relative"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 inline-block rounded-lg px-2 py-1 text-white font-bold shadow-md">近期通过借贷审批情况</div><div class="absolute left-0 top-0 h-4 w-4 transform rounded-full bg-white shadow-md -translate-x-2 -translate-y-2"></div><div class="relative mt-1.5"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 h-[2px] w-full rounded"></div></div></div><div data-v-44e08373="" class="overflow-x-auto"><div data-v-e5692a64="" data-v-44e08373="" class="l-table overflow-x-auto"><table data-v-e5692a64="" class="min-w-full border-collapse table-auto text-center text-size-xs"><thead data-v-e5692a64="" class="bg-teal-200"><tr data-v-e5692a64=""><th data-v-44e08373="" class="border px-1 py-2 text-xs min-w-[25%]">时间</th><th data-v-44e08373="" class="border px-1 py-2 text-xs min-w-[15%]">借贷机构数</th><th data-v-44e08373="" class="border px-1 py-2 text-xs min-w-[15%]">借贷次数</th><th data-v-44e08373="" class="border px-1 py-2 text-xs min-w-[15%]">审批额度</th></tr></thead><tbody data-v-e5692a64=""><tr data-v-e5692a64="" class="border-t"><td data-v-44e08373="" class="border px-1 py-2 text-xs">近1年</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">3</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">6</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">168,000</td></tr><tr data-v-e5692a64="" class="bg-teal-100/40 border-t"><td data-v-44e08373="" class="border px-1 py-2 text-xs">近9月</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">3</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">6</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">168,000</td></tr><tr data-v-e5692a64="" class="border-t"><td data-v-44e08373="" class="border px-1 py-2 text-xs">近6月</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">3</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">5</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">141,000</td></tr><tr data-v-e5692a64="" class="bg-teal-100/40 border-t"><td data-v-44e08373="" class="border px-1 py-2 text-xs">近3月</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">3</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">4</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">114,000</td></tr><tr data-v-e5692a64="" class="border-t"><td data-v-44e08373="" class="border px-1 py-2 text-xs">近1月</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">2</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">3</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">93,000</td></tr></tbody></table></div></div><div data-v-44e08373="" class="relative"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 inline-block rounded-lg px-2 py-1 text-white font-bold shadow-md">近1年借贷情况</div><div class="absolute left-0 top-0 h-4 w-4 transform rounded-full bg-white shadow-md -translate-x-2 -translate-y-2"></div><div class="relative mt-1.5"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 h-[2px] w-full rounded"></div></div></div><div data-v-44e08373="" class="overflow-x-auto"><div data-v-e5692a64="" data-v-44e08373="" class="l-table overflow-x-auto"><table data-v-e5692a64="" class="min-w-full border-collapse table-auto text-center text-size-xs"><thead data-v-e5692a64="" class="bg-teal-200"><tr data-v-e5692a64=""><th data-v-44e08373="" class="border px-1 py-2 text-xs min-w-[25%]">时间</th><th data-v-44e08373="" class="border px-1 py-2 text-xs min-w-[15%]">借贷机构数</th><th data-v-44e08373="" class="border px-1 py-2 text-xs min-w-[15%]">借贷次数</th><th data-v-44e08373="" class="border px-1 py-2 text-xs min-w-[15%]">审批额度</th><th data-v-44e08373="" class="border px-1 py-2 text-xs min-w-[15%]">应还金额</th><th data-v-44e08373="" class="border px-1 py-2 text-xs min-w-[15%]">审批与应还比例</th></tr></thead><tbody data-v-e5692a64=""><tr data-v-e5692a64="" class="border-t"><td data-v-44e08373="" class="border px-1 py-2 text-xs">1年内</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">3</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">6</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">168,000</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">48,000</td><td data-v-44e08373="" class="border px-1 py-2 text-xs text-center">28.57%</td></tr></tbody></table></div></div><div data-v-44e08373="" class="relative"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 inline-block rounded-lg px-2 py-1 text-white font-bold shadow-md">借贷行为总结分析</div><div class="absolute left-0 top-0 h-4 w-4 transform rounded-full bg-white shadow-md -translate-x-2 -translate-y-2"></div><div class="relative mt-1.5"><div class="bg-gradient-to-r from-blue-400 via-green-500 to-teal-500 h-[2px] w-full rounded"></div></div></div><div data-v-44e08373="" class="summary-container bg-blue-50 p-4 rounded-md"><div data-v-44e08373="" class="text-xs text-gray-500 mb-2">数据时间范围: 近1年</div><div data-v-44e08373="" class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4"><div data-v-44e08373="" class="info-card p-3 bg-white rounded shadow-sm"><div data-v-44e08373="" class="text-sm text-gray-500">总申请次数</div><div data-v-44e08373="" class="text-lg font-semibold">6次</div></div><div data-v-44e08373="" class="info-card p-3 bg-white rounded shadow-sm"><div data-v-44e08373="" class="text-sm text-gray-500">借贷机构数</div><div data-v-44e08373="" class="text-lg font-semibold">3家</div></div><div data-v-44e08373="" class="info-card p-3 bg-white rounded shadow-sm"><div data-v-44e08373="" class="text-sm text-gray-500">总审批额度</div><div data-v-44e08373="" class="text-lg font-semibold">168,000元</div></div><div data-v-44e08373="" class="info-card p-3 bg-white rounded shadow-sm"><div data-v-44e08373="" class="text-sm text-gray-500">月均申请次数</div><div data-v-44e08373="" class="text-lg font-semibold">0.5次</div></div></div><div data-v-44e08373="" class="risk-assessment p-4 bg-white rounded-md"><div data-v-44e08373="" class="text-lg font-bold mb-2"> 风险评估: <span data-v-44e08373="" class="text-yellow-500">中风险</span></div><div data-v-44e08373="" class="text-gray-700"><p data-v-44e08373=""> · 月均审批额度: 14,000元 </p><p data-v-44e08373=""> · 月均应还金额: 4,000元 </p><p data-v-44e08373="">· 还款比例: 28.6%</p><p data-v-44e08373="">· 借贷行为健康，借贷金额合理，还款比例较低</p></div></div></div></div></div><!----></div><div data-v-f2fd578f="" data-v-622108cb="" class="mt-4 flex flex-col items-center gap-2 mb-4"><button data-v-f2fd578f="" type="button" class="van-button van-button--primary van-button--small !bg-blue-500 !border-blue-500"><div class="van-button__content"><div class="van-button__icon"><i data-v-f2fd578f="" class="van-badge__wrapper van-icon van-icon-share-o"><!----><!----><!----></i></div><span class="van-button__text"> 分享报告</span><!----></div></button><div data-v-f2fd578f="" class="text-xs text-gray-500">分享链接将在7天后过期</div></div><div data-v-622108cb="" class="card"><div data-v-622108cb=""><div data-v-622108cb="" class="text-bold text-blue-500 mb-2"> 报告说明 </div><div data-v-622108cb=""> &nbsp; &nbsp;本报告的数据由用户本人明确授权后，我们才向相关合法存有用户个人数据的机构调取本报告相关内容，本平台只做大数据的获取与分析，仅向用户个人展示参考。 </div><p data-v-622108cb=""> &nbsp; &nbsp; 报告有效期<strong data-v-622108cb="" class="text-red-500">30天</strong>，过期自动删除。 </p><p data-v-622108cb=""> &nbsp; &nbsp; 若您的数据不全面，可能是数据具有延迟性或者合作信息机构未获取到您的数据。若数据有错误请联系客服 </p><p data-v-622108cb=""> &nbsp; &nbsp;本产品所有数据均来自第三方。可能部分数据未公开、数据更新延迟或信息受到限制，贵司不对数据的准确性、真实性、完整性做任何承诺。用户需根据实际情况，结合报告内容自行判断与决策。 </p></div></div></div></div></div></div><div class="disclaimer" data-v-622108cb=""><div class="flex flex-col items-center" data-v-622108cb=""><div class="flex items-center" data-v-622108cb=""><img class="w-4 h-4 mr-2" src="./天远数据 - 婚恋评估、司法涉诉查询、婚姻状态、判决书查询工具_files/public_security_record_icon-1_QVWmWl.png" alt="公安备案" data-v-622108cb=""><text data-v-622108cb="">琼公网安备46010002000443号</text></div><div data-v-622108cb=""><a class="text-blue-500" href="https://beian.miit.gov.cn/" data-v-622108cb=""> 琼ICP备2024038584号-2 </a></div></div><div data-v-622108cb="">海南省学宇思科技有限公司版权所有</div></div><!----><!----><!----></div>
        



        
    

<div data-v-app=""></div><!----><div role="dialog" tabindex="0" class="van-popup van-popup--center van-toast van-toast--bottom van-toast--fail" style="z-index: 2002; display: none;"><i class="van-badge__wrapper van-icon van-icon-fail van-toast__icon"><!----><!----><!----></i><div class="van-toast__text">缺少订单标识</div><!----></div></body><div id="immersive-translate-browser-popup" style="all: initial"><template shadowrootmode="open"><style>@charset "UTF-8";
/*!
 * Pico.css v1.5.6 (https://picocss.com)
 * Copyright 2019-2022 - Licensed under MIT
 */
/**
 * Theme: default
 */
#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 0.25rem;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 1rem;
  --typography-spacing-vertical: 1.5rem;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 0.75rem;
  --form-element-spacing-horizontal: 1rem;
  --nav-element-spacing-vertical: 1rem;
  --nav-element-spacing-horizontal: 0.5rem;
  --nav-link-spacing-vertical: 0.5rem;
  --nav-link-spacing-horizontal: 0.5rem;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(0.25rem);
}
@media (min-width: 576px) {
  #mount {
    --font-size: 17px;
  }
}
@media (min-width: 768px) {
  #mount {
    --font-size: 18px;
  }
}
@media (min-width: 992px) {
  #mount {
    --font-size: 19px;
  }
}
@media (min-width: 1200px) {
  #mount {
    --font-size: 20px;
  }
}

@media (min-width: 576px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 2);
  }
}
@media (min-width: 768px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 2.5);
  }
}
@media (min-width: 992px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 3);
  }
}
@media (min-width: 1200px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 3.5);
  }
}

@media (min-width: 576px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.25);
  }
}
@media (min-width: 768px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.5);
  }
}
@media (min-width: 992px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.75);
  }
}
@media (min-width: 1200px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 2);
  }
}

dialog > article {
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
}
@media (min-width: 576px) {
  dialog > article {
    --block-spacing-vertical: calc(var(--spacing) * 2.5);
    --block-spacing-horizontal: calc(var(--spacing) * 1.25);
  }
}
@media (min-width: 768px) {
  dialog > article {
    --block-spacing-vertical: calc(var(--spacing) * 3);
    --block-spacing-horizontal: calc(var(--spacing) * 1.5);
  }
}

a {
  --text-decoration: none;
}
a.secondary,
a.contrast {
  --text-decoration: underline;
}

small {
  --font-size: 0.875em;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  --font-weight: 700;
}

h1 {
  --font-size: 2rem;
  --typography-spacing-vertical: 3rem;
}

h2 {
  --font-size: 1.75rem;
  --typography-spacing-vertical: 2.625rem;
}

h3 {
  --font-size: 1.5rem;
  --typography-spacing-vertical: 2.25rem;
}

h4 {
  --font-size: 1.25rem;
  --typography-spacing-vertical: 1.874rem;
}

h5 {
  --font-size: 1.125rem;
  --typography-spacing-vertical: 1.6875rem;
}

[type="checkbox"],
[type="radio"] {
  --border-width: 2px;
}

[type="checkbox"][role="switch"] {
  --border-width: 2px;
}

thead th,
thead td,
tfoot th,
tfoot td {
  --border-width: 3px;
}

:not(thead, tfoot) > * > td {
  --font-size: 0.875em;
}

pre,
code,
kbd,
samp {
  --font-family: "Menlo", "Consolas", "Roboto Mono", "Ubuntu Monospace",
    "Noto Mono", "Oxygen Mono", "Liberation Mono", monospace,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

kbd {
  --font-weight: bolder;
}

[data-theme="light"],
#mount:not([data-theme="dark"]) {
  --background-color: #fff;
  --background-light-green: #f5f7f9;
  --color: hsl(205deg, 20%, 32%);
  --h1-color: hsl(205deg, 30%, 15%);
  --h2-color: #24333e;
  --h3-color: hsl(205deg, 25%, 23%);
  --h4-color: #374956;
  --h5-color: hsl(205deg, 20%, 32%);
  --h6-color: #4d606d;
  --muted-color: hsl(205deg, 10%, 50%);
  --muted-border-color: hsl(205deg, 20%, 94%);
  --primary: hsl(195deg, 85%, 41%);
  --primary-hover: hsl(195deg, 90%, 32%);
  --primary-focus: rgba(16, 149, 193, 0.125);
  --primary-inverse: #fff;
  --secondary: hsl(205deg, 15%, 41%);
  --secondary-hover: hsl(205deg, 20%, 32%);
  --secondary-focus: rgba(89, 107, 120, 0.125);
  --secondary-inverse: #fff;
  --contrast: hsl(205deg, 30%, 15%);
  --contrast-hover: #000;
  --contrast-focus: rgba(89, 107, 120, 0.125);
  --contrast-inverse: #fff;
  --mark-background-color: #fff2ca;
  --mark-color: #543a26;
  --ins-color: #388e3c;
  --del-color: #c62828;
  --blockquote-border-color: var(--muted-border-color);
  --blockquote-footer-color: var(--muted-color);
  --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --form-element-background-color: transparent;
  --form-element-border-color: hsl(205deg, 14%, 68%);
  --form-element-color: var(--color);
  --form-element-placeholder-color: var(--muted-color);
  --form-element-active-background-color: transparent;
  --form-element-active-border-color: var(--primary);
  --form-element-focus-color: var(--primary-focus);
  --form-element-disabled-background-color: hsl(205deg, 18%, 86%);
  --form-element-disabled-border-color: hsl(205deg, 14%, 68%);
  --form-element-disabled-opacity: 0.5;
  --form-element-invalid-border-color: #c62828;
  --form-element-invalid-active-border-color: #d32f2f;
  --form-element-invalid-focus-color: rgba(211, 47, 47, 0.125);
  --form-element-valid-border-color: #388e3c;
  --form-element-valid-active-border-color: #43a047;
  --form-element-valid-focus-color: rgba(67, 160, 71, 0.125);
  --switch-background-color: hsl(205deg, 16%, 77%);
  --switch-color: var(--primary-inverse);
  --switch-checked-background-color: var(--primary);
  --range-border-color: hsl(205deg, 18%, 86%);
  --range-active-border-color: hsl(205deg, 16%, 77%);
  --range-thumb-border-color: var(--background-color);
  --range-thumb-color: var(--secondary);
  --range-thumb-hover-color: var(--secondary-hover);
  --range-thumb-active-color: var(--primary);
  --table-border-color: var(--muted-border-color);
  --table-row-stripped-background-color: #f6f8f9;
  --code-background-color: hsl(205deg, 20%, 94%);
  --code-color: var(--muted-color);
  --code-kbd-background-color: var(--contrast);
  --code-kbd-color: var(--contrast-inverse);
  --code-tag-color: hsl(330deg, 40%, 50%);
  --code-property-color: hsl(185deg, 40%, 40%);
  --code-value-color: hsl(40deg, 20%, 50%);
  --code-comment-color: hsl(205deg, 14%, 68%);
  --accordion-border-color: var(--muted-border-color);
  --accordion-close-summary-color: var(--color);
  --accordion-open-summary-color: var(--muted-color);
  --card-background-color: var(--background-color);
  --card-border-color: var(--muted-border-color);
  --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(27, 40, 50, 0.01698),
    0.0335rem 0.067rem 0.402rem rgba(27, 40, 50, 0.024),
    0.0625rem 0.125rem 0.75rem rgba(27, 40, 50, 0.03),
    0.1125rem 0.225rem 1.35rem rgba(27, 40, 50, 0.036),
    0.2085rem 0.417rem 2.502rem rgba(27, 40, 50, 0.04302),
    0.5rem 1rem 6rem rgba(27, 40, 50, 0.06),
    0 0 0 0.0625rem rgba(27, 40, 50, 0.015);
  --card-sectionning-background-color: #fbfbfc;
  --dropdown-background-color: #fbfbfc;
  --dropdown-border-color: #e1e6eb;
  --dropdown-box-shadow: var(--card-box-shadow);
  --dropdown-color: var(--color);
  --dropdown-hover-background-color: hsl(205deg, 20%, 94%);
  --modal-overlay-background-color: rgba(213, 220, 226, 0.7);
  --progress-background-color: hsl(205deg, 18%, 86%);
  --progress-color: var(--primary);
  --loading-spinner-opacity: 0.5;
  --tooltip-background-color: var(--contrast);
  --tooltip-color: var(--contrast-inverse);
  --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(198, 40, 40)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(56, 142, 60)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
  --float-ball-more-button-border-color: #f6f6f6;
  --float-ball-more-button-background-color: #ffffff;
  --float-ball-more-button-svg-color: #6c6f73;
  color-scheme: light;
  --service-bg-hover: #f7faff;
  --service-bg: #fafbfb;
}

@media only screen and (prefers-color-scheme: dark) {
  #mount:not([data-theme="light"]) {
    --background-color: #11191f;
    --float-ball-more-button-background-color: #ffffff;
    --background-light-green: #141e26;
    --color: hsl(205deg, 16%, 77%);
    --h1-color: hsl(205deg, 20%, 94%);
    --h2-color: #e1e6eb;
    --h3-color: hsl(205deg, 18%, 86%);
    --h4-color: #c8d1d8;
    --h5-color: hsl(205deg, 16%, 77%);
    --h6-color: #afbbc4;
    --muted-color: hsl(205deg, 10%, 50%);
    --muted-border-color: #1f2d38;
    --primary: hsl(195deg, 85%, 41%);
    --primary-hover: hsl(195deg, 80%, 50%);
    --primary-focus: rgba(16, 149, 193, 0.25);
    --primary-inverse: #fff;
    --secondary: hsl(205deg, 15%, 41%);
    --secondary-hover: hsl(205deg, 10%, 50%);
    --secondary-focus: rgba(115, 130, 140, 0.25);
    --secondary-inverse: #fff;
    --contrast: hsl(205deg, 20%, 94%);
    --contrast-hover: #fff;
    --contrast-focus: rgba(115, 130, 140, 0.25);
    --contrast-inverse: #000;
    --mark-background-color: #d1c284;
    --mark-color: #11191f;
    --ins-color: #388e3c;
    --del-color: #c62828;
    --blockquote-border-color: var(--muted-border-color);
    --blockquote-footer-color: var(--muted-color);
    --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    --form-element-background-color: #11191f;
    --form-element-border-color: #374956;
    --form-element-color: var(--color);
    --form-element-placeholder-color: var(--muted-color);
    --form-element-active-background-color: var(
      --form-element-background-color
    );
    --form-element-active-border-color: var(--primary);
    --form-element-focus-color: var(--primary-focus);
    --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
    --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
    --form-element-disabled-opacity: 0.5;
    --form-element-invalid-border-color: #b71c1c;
    --form-element-invalid-active-border-color: #c62828;
    --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
    --form-element-valid-border-color: #2e7d32;
    --form-element-valid-active-border-color: #388e3c;
    --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
    --switch-background-color: #374956;
    --switch-color: var(--primary-inverse);
    --switch-checked-background-color: var(--primary);
    --range-border-color: #24333e;
    --range-active-border-color: hsl(205deg, 25%, 23%);
    --range-thumb-border-color: var(--background-color);
    --range-thumb-color: var(--secondary);
    --range-thumb-hover-color: var(--secondary-hover);
    --range-thumb-active-color: var(--primary);
    --table-border-color: var(--muted-border-color);
    --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
    --code-background-color: #18232c;
    --code-color: var(--muted-color);
    --code-kbd-background-color: var(--contrast);
    --code-kbd-color: var(--contrast-inverse);
    --code-tag-color: hsl(330deg, 30%, 50%);
    --code-property-color: hsl(185deg, 30%, 50%);
    --code-value-color: hsl(40deg, 10%, 50%);
    --code-comment-color: #4d606d;
    --accordion-border-color: var(--muted-border-color);
    --accordion-active-summary-color: var(--primary);
    --accordion-close-summary-color: var(--color);
    --accordion-open-summary-color: var(--muted-color);
    --card-background-color: #141e26;
    --card-border-color: var(--card-background-color);
    --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
      0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
      0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
      0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
      0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
      0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
    --card-sectionning-background-color: #18232c;
    --dropdown-background-color: hsl(205deg, 30%, 15%);
    --dropdown-border-color: #24333e;
    --dropdown-box-shadow: var(--card-box-shadow);
    --dropdown-color: var(--color);
    --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
    --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
    --progress-background-color: #24333e;
    --progress-color: var(--primary);
    --loading-spinner-opacity: 0.5;
    --tooltip-background-color: var(--contrast);
    --tooltip-color: var(--contrast-inverse);
    --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
    --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
    --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
    --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
    --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
    --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
    color-scheme: dark;
    --service-bg-hover: #22292f;
    --service-bg: rgba(0, 0, 0, 0.1);
  }
}
[data-theme="dark"] {
  --background-color: #11191f;
  --float-ball-more-button-background-color: #ffffff;
  --background-light-green: #141e26;
  --color: hsl(205deg, 16%, 77%);
  --h1-color: hsl(205deg, 20%, 94%);
  --h2-color: #e1e6eb;
  --h3-color: hsl(205deg, 18%, 86%);
  --h4-color: #c8d1d8;
  --h5-color: hsl(205deg, 16%, 77%);
  --h6-color: #afbbc4;
  --muted-color: hsl(205deg, 10%, 50%);
  --muted-border-color: #1f2d38;
  --primary: hsl(195deg, 85%, 41%);
  --primary-hover: hsl(195deg, 80%, 50%);
  --primary-focus: rgba(16, 149, 193, 0.25);
  --primary-inverse: #fff;
  --secondary: hsl(205deg, 15%, 41%);
  --secondary-hover: hsl(205deg, 10%, 50%);
  --secondary-focus: rgba(115, 130, 140, 0.25);
  --secondary-inverse: #fff;
  --contrast: hsl(205deg, 20%, 94%);
  --contrast-hover: #fff;
  --contrast-focus: rgba(115, 130, 140, 0.25);
  --contrast-inverse: #000;
  --mark-background-color: #d1c284;
  --mark-color: #11191f;
  --ins-color: #388e3c;
  --del-color: #c62828;
  --blockquote-border-color: var(--muted-border-color);
  --blockquote-footer-color: var(--muted-color);
  --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --form-element-background-color: #11191f;
  --form-element-border-color: #374956;
  --form-element-color: var(--color);
  --form-element-placeholder-color: var(--muted-color);
  --form-element-active-background-color: var(--form-element-background-color);
  --form-element-active-border-color: var(--primary);
  --form-element-focus-color: var(--primary-focus);
  --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
  --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
  --form-element-disabled-opacity: 0.5;
  --form-element-invalid-border-color: #b71c1c;
  --form-element-invalid-active-border-color: #c62828;
  --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
  --form-element-valid-border-color: #2e7d32;
  --form-element-valid-active-border-color: #388e3c;
  --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
  --switch-background-color: #374956;
  --switch-color: var(--primary-inverse);
  --switch-checked-background-color: var(--primary);
  --range-border-color: #24333e;
  --range-active-border-color: hsl(205deg, 25%, 23%);
  --range-thumb-border-color: var(--background-color);
  --range-thumb-color: var(--secondary);
  --range-thumb-hover-color: var(--secondary-hover);
  --range-thumb-active-color: var(--primary);
  --table-border-color: var(--muted-border-color);
  --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
  --code-background-color: #18232c;
  --code-color: var(--muted-color);
  --code-kbd-background-color: var(--contrast);
  --code-kbd-color: var(--contrast-inverse);
  --code-tag-color: hsl(330deg, 30%, 50%);
  --code-property-color: hsl(185deg, 30%, 50%);
  --code-value-color: hsl(40deg, 10%, 50%);
  --code-comment-color: #4d606d;
  --accordion-border-color: var(--muted-border-color);
  --accordion-active-summary-color: var(--primary);
  --accordion-close-summary-color: var(--color);
  --accordion-open-summary-color: var(--muted-color);
  --card-background-color: #141e26;
  --card-border-color: var(--card-background-color);
  --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
    0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
    0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
    0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
    0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
    0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
  --card-sectionning-background-color: #18232c;
  --dropdown-background-color: hsl(205deg, 30%, 15%);
  --dropdown-border-color: #24333e;
  --dropdown-box-shadow: var(--card-box-shadow);
  --dropdown-color: var(--color);
  --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
  --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
  --progress-background-color: #24333e;
  --progress-color: var(--primary);
  --loading-spinner-opacity: 0.5;
  --tooltip-background-color: var(--contrast);
  --tooltip-color: var(--contrast-inverse);
  --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
  color-scheme: dark;
  --service-bg: rgba(0, 0, 0, 0.1);
}

progress,
[type="checkbox"],
[type="radio"],
[type="range"] {
  accent-color: var(--primary);
}

/**
 * Document
 * Content-box & Responsive typography
 */
*,
*::before,
*::after {
  box-sizing: border-box;
  background-repeat: no-repeat;
}

::before,
::after {
  text-decoration: inherit;
  vertical-align: inherit;
}

:where(#mount) {
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  background-color: var(--background-color);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  line-height: var(--line-height);
  font-family: var(--font-family);
  text-rendering: optimizeLegibility;
  overflow-wrap: break-word;
  cursor: default;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
}

/**
 * Sectioning
 * Container and responsive spacings for header, main, footer
 */
main {
  display: block;
}

#mount {
  width: 100%;
  margin: 0;
}
#mount > header,
#mount > main,
#mount > footer {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
}
@media (min-width: 576px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    padding: 2px !important;
  }
}
@media (min-width: 992px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    padding: 0 12px !important;
  }
}
@media (min-width: 1200px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    padding: 0 24px !important;
  }
}

/**
* Container
*/
.container,
.container-fluid {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--spacing);
  padding-left: var(--spacing);
}
/* 
@media (min-width: 576px) {
  .container {
    max-width: 510px;
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 700px;
  }
} */
@media (min-width: 992px) {
  .container {
    max-width: 920px;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1130px;
  }
}

/**
 * Section
 * Responsive spacings for section
 */
section {
  margin-bottom: var(--block-spacing-vertical);
}

/**
* Grid
* Minimal grid system with auto-layout columns
*/
.grid {
  grid-column-gap: var(--grid-spacing-horizontal);
  grid-row-gap: var(--grid-spacing-vertical);
  display: grid;
  grid-template-columns: 1fr;
  margin: 0;
}
@media (min-width: 1280px) {
  .grid {
    grid-template-columns: repeat(auto-fit, minmax(0%, 1fr));
  }
}
.grid > * {
  min-width: 0;
}

/**
 * Horizontal scroller (<figure>)
 */
figure {
  display: block;
  margin: 0;
  padding: 0;
  overflow-x: auto;
}
figure figcaption {
  padding: calc(var(--spacing) * 0.5) 0;
  color: var(--muted-color);
}

/**
 * Typography
 */
b,
strong {
  font-weight: bolder;
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

address,
blockquote,
dl,
figure,
form,
ol,
p,
pre,
table,
ul {
  margin-top: 0;
  margin-bottom: var(--typography-spacing-vertical);
  color: var(--color);
  font-style: normal;
  font-weight: var(--font-weight);
  font-size: var(--font-size);
}

a,
[role="link"] {
  --color: var(--primary);
  --background-color: transparent;
  outline: none;
  background-color: var(--background-color);
  color: var(--color);
  -webkit-text-decoration: var(--text-decoration);
  text-decoration: var(--text-decoration);
  transition: background-color var(--transition), color var(--transition),
    box-shadow var(--transition), -webkit-text-decoration var(--transition);
  transition: background-color var(--transition), color var(--transition),
    text-decoration var(--transition), box-shadow var(--transition);
  transition: background-color var(--transition), color var(--transition),
    text-decoration var(--transition), box-shadow var(--transition),
    -webkit-text-decoration var(--transition);
}
a:is([aria-current], :hover, :active, :focus),
[role="link"]:is([aria-current], :hover, :active, :focus) {
  --color: var(--primary-hover);
  --text-decoration: underline;
}
a:focus,
[role="link"]:focus {
  --background-color: var(--primary-focus);
}
a.secondary,
[role="link"].secondary {
  --color: var(--secondary);
}
a.secondary:is([aria-current], :hover, :active, :focus),
[role="link"].secondary:is([aria-current], :hover, :active, :focus) {
  --color: var(--secondary-hover);
}
a.secondary:focus,
[role="link"].secondary:focus {
  --background-color: var(--secondary-focus);
}
a.contrast,
[role="link"].contrast {
  --color: var(--contrast);
}
a.contrast:is([aria-current], :hover, :active, :focus),
[role="link"].contrast:is([aria-current], :hover, :active, :focus) {
  --color: var(--contrast-hover);
}
a.contrast:focus,
[role="link"].contrast:focus {
  --background-color: var(--contrast-focus);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: var(--typography-spacing-vertical);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  font-family: var(--font-family);
}

h1 {
  --color: var(--h1-color);
}

h2 {
  --color: var(--h2-color);
}

h3 {
  --color: var(--h3-color);
}

h4 {
  --color: var(--h4-color);
}

h5 {
  --color: var(--h5-color);
}

h6 {
  --color: var(--h6-color);
}

:where(address, blockquote, dl, figure, form, ol, p, pre, table, ul)
  ~ :is(h1, h2, h3, h4, h5, h6) {
  margin-top: var(--typography-spacing-vertical);
}

hgroup,
.headings {
  margin-bottom: var(--typography-spacing-vertical);
}
hgroup > *,
.headings > * {
  margin-bottom: 0;
}
hgroup > *:last-child,
.headings > *:last-child {
  --color: var(--muted-color);
  --font-weight: unset;
  font-size: 1rem;
  font-family: unset;
}

p {
  margin-bottom: var(--typography-spacing-vertical);
}

small {
  font-size: var(--font-size);
}

:where(dl, ol, ul) {
  padding-right: 0;
  padding-left: var(--spacing);
  -webkit-padding-start: var(--spacing);
  padding-inline-start: var(--spacing);
  -webkit-padding-end: 0;
  padding-inline-end: 0;
}
:where(dl, ol, ul) li {
  margin-bottom: calc(var(--typography-spacing-vertical) * 0.25);
}

:where(dl, ol, ul) :is(dl, ol, ul) {
  margin: 0;
  margin-top: calc(var(--typography-spacing-vertical) * 0.25);
}

ul li {
  list-style: square;
}

mark {
  padding: 0.125rem 0.25rem;
  background-color: var(--mark-background-color);
  color: var(--mark-color);
  vertical-align: baseline;
}

blockquote {
  display: block;
  margin: var(--typography-spacing-vertical) 0;
  padding: var(--spacing);
  border-right: none;
  border-left: 0.25rem solid var(--blockquote-border-color);
  -webkit-border-start: 0.25rem solid var(--blockquote-border-color);
  border-inline-start: 0.25rem solid var(--blockquote-border-color);
  -webkit-border-end: none;
  border-inline-end: none;
}
blockquote footer {
  margin-top: calc(var(--typography-spacing-vertical) * 0.5);
  color: var(--blockquote-footer-color);
}

abbr[title] {
  border-bottom: 1px dotted;
  text-decoration: none;
  cursor: help;
}

ins {
  color: var(--ins-color);
  text-decoration: none;
}

del {
  color: var(--del-color);
}

::-moz-selection {
  background-color: var(--primary-focus);
}

::selection {
  background-color: var(--primary-focus);
}

/**
 * Embedded content
 */
:where(audio, canvas, iframe, img, svg, video) {
  vertical-align: middle;
}

audio,
video {
  display: inline-block;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

:where(iframe) {
  border-style: none;
}

img {
  max-width: 100%;
  height: auto;
  border-style: none;
}

:where(svg:not([fill])) {
  fill: currentColor;
}

svg:not(#mount) {
  overflow: hidden;
}

/**
 * Button
 */
button {
  margin: 0;
  overflow: visible;
  font-family: inherit;
  text-transform: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

button {
  display: block;
  width: 100%;
  margin-bottom: var(--spacing);
}

[role="button"] {
  display: inline-block;
  text-decoration: none;
}

button,
input[type="submit"],
input[type="button"],
input[type="reset"],
[role="button"] {
  --background-color: var(--primary);
  --border-color: var(--primary);
  --color: var(--primary-inverse);
  --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
button:is([aria-current], :hover, :active, :focus),
input[type="submit"]:is([aria-current], :hover, :active, :focus),
input[type="button"]:is([aria-current], :hover, :active, :focus),
input[type="reset"]:is([aria-current], :hover, :active, :focus),
[role="button"]:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--primary-hover);
  --border-color: var(--primary-hover);
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  --color: var(--primary-inverse);
}
button:focus,
input[type="submit"]:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
[role="button"]:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--primary-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary,
input[type="reset"] {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  cursor: pointer;
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:is([aria-current], :hover, :active, :focus),
input[type="reset"]:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
  --color: var(--secondary-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:focus,
input[type="reset"]:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--secondary-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast {
  --background-color: var(--contrast);
  --border-color: var(--contrast);
  --color: var(--contrast-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--contrast-hover);
  --border-color: var(--contrast-hover);
  --color: var(--contrast-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--contrast-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline,
input[type="reset"].outline {
  --background-color: transparent;
  --color: var(--primary);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline:is([aria-current], :hover, :active, :focus),
input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
  --background-color: transparent;
  --color: var(--primary-hover);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary,
input[type="reset"].outline {
  --color: var(--secondary);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary:is([aria-current], :hover, :active, :focus),
input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
  --color: var(--secondary-hover);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast {
  --color: var(--contrast);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast:is([aria-current], :hover, :active, :focus) {
  --color: var(--contrast-hover);
}

:where(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  )[disabled],
:where(fieldset[disabled])
  :is(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  ),
a[role="button"]:not([href]) {
  opacity: 0.5;
  pointer-events: none;
}

/**
 * Form elements
 */
input,
optgroup,
select,
textarea {
  margin: 0;
  font-size: 1rem;
  line-height: var(--line-height);
  font-family: inherit;
  letter-spacing: inherit;
}

input {
  overflow: visible;
}

select {
  text-transform: none;
}

legend {
  max-width: 100%;
  padding: 0;
  color: inherit;
  white-space: normal;
}

textarea {
  overflow: auto;
}

[type="checkbox"],
[type="radio"] {
  padding: 0;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

:-moz-focusring {
  outline: none;
}

:-moz-ui-invalid {
  box-shadow: none;
}

::-ms-expand {
  display: none;
}

[type="file"],
[type="range"] {
  padding: 0;
  border-width: 0;
}

input:not([type="checkbox"], [type="radio"], [type="range"]) {
  height: calc(
    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
      var(--border-width) * 2
  );
}

fieldset {
  margin: 0;
  margin-bottom: var(--spacing);
  padding: 0;
  border: 0;
}

label,
fieldset legend {
  display: block;
  margin-bottom: calc(var(--spacing) * 0.25);
  font-weight: var(--form-label-font-weight, var(--font-weight));
}

input:not([type="checkbox"], [type="radio"]),
select,
textarea {
  width: 100%;
}

input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]),
select,
textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
}

input,
select,
textarea {
  --background-color: var(--form-element-background-color);
  --border-color: var(--form-element-border-color);
  --color: var(--form-element-color);
  --box-shadow: none;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="checkbox"],
    [type="radio"],
    [readonly]
  ):is(:active, :focus),
:where(select, textarea):is(:active, :focus) {
  --background-color: var(--form-element-active-background-color);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="switch"],
    [readonly]
  ):is(:active, :focus),
:where(select, textarea):is(:active, :focus) {
  --border-color: var(--form-element-active-border-color);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="range"],
    [type="file"],
    [readonly]
  ):focus,
select:focus,
textarea:focus {
  --box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
}

input:not([type="submit"], [type="button"], [type="reset"])[disabled],
select[disabled],
textarea[disabled],
:where(fieldset[disabled])
  :is(
    input:not([type="submit"], [type="button"], [type="reset"]),
    select,
    textarea
  ) {
  --background-color: var(--form-element-disabled-background-color);
  --border-color: var(--form-element-disabled-border-color);
  opacity: var(--form-element-disabled-opacity);
  pointer-events: none;
}

:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid] {
  padding-right: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  padding-left: var(--form-element-spacing-horizontal);
  -webkit-padding-start: var(--form-element-spacing-horizontal) !important;
  padding-inline-start: var(--form-element-spacing-horizontal) !important;
  -webkit-padding-end: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  padding-inline-end: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  background-position: center right 0.75rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}
:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="false"] {
  background-image: var(--icon-valid);
}
:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="true"] {
  background-image: var(--icon-invalid);
}
:where(input, select, textarea)[aria-invalid="false"] {
  --border-color: var(--form-element-valid-border-color);
}
:where(input, select, textarea)[aria-invalid="false"]:is(:active, :focus) {
  --border-color: var(--form-element-valid-active-border-color) !important;
  --box-shadow: 0 0 0 var(--outline-width) var(--form-element-valid-focus-color) !important;
}
:where(input, select, textarea)[aria-invalid="true"] {
  --border-color: var(--form-element-invalid-border-color);
}
:where(input, select, textarea)[aria-invalid="true"]:is(:active, :focus) {
  --border-color: var(--form-element-invalid-active-border-color) !important;
  --box-shadow: 0 0 0 var(--outline-width)
    var(--form-element-invalid-focus-color) !important;
}

[dir="rtl"]
  :where(input, select, textarea):not([type="checkbox"], [type="radio"]):is(
    [aria-invalid],
    [aria-invalid="true"],
    [aria-invalid="false"]
  ) {
  background-position: center left 0.75rem;
}

input::placeholder,
input::-webkit-input-placeholder,
textarea::placeholder,
textarea::-webkit-input-placeholder,
select:invalid {
  color: var(--form-element-placeholder-color);
  opacity: 1;
}

input:not([type="checkbox"], [type="radio"]),
select,
textarea {
  margin-bottom: var(--spacing);
}

select::-ms-expand {
  border: 0;
  background-color: transparent;
}
select:not([multiple], [size]) {
  padding-right: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  padding-left: var(--form-element-spacing-horizontal);
  -webkit-padding-start: var(--form-element-spacing-horizontal);
  padding-inline-start: var(--form-element-spacing-horizontal);
  -webkit-padding-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  padding-inline-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  background-image: var(--icon-chevron);
  background-position: center right 0.75rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}

[dir="rtl"] select:not([multiple], [size]) {
  background-position: center left 0.75rem;
}

:where(input, select, textarea) + small {
  display: block;
  width: 100%;
  margin-top: calc(var(--spacing) * -0.75);
  margin-bottom: var(--spacing);
  color: var(--muted-color);
}

label > :where(input, select, textarea) {
  margin-top: calc(var(--spacing) * 0.25);
}

/**
 * Form elements
 * Checkboxes & Radios
 */
[type="checkbox"],
[type="radio"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 1.25em;
  height: 1.25em;
  margin-top: -0.125em;
  margin-right: 0.375em;
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0.375em;
  margin-inline-end: 0.375em;
  border-width: var(--border-width);
  font-size: inherit;
  vertical-align: middle;
  cursor: pointer;
}
[type="checkbox"]::-ms-check,
[type="radio"]::-ms-check {
  display: none;
}
[type="checkbox"]:checked,
[type="checkbox"]:checked:active,
[type="checkbox"]:checked:focus,
[type="radio"]:checked,
[type="radio"]:checked:active,
[type="radio"]:checked:focus {
  --background-color: var(--primary);
  --border-color: var(--primary);
  background-image: var(--icon-checkbox);
  background-position: center;
  background-size: 0.75em auto;
  background-repeat: no-repeat;
}
[type="checkbox"] ~ label,
[type="radio"] ~ label {
  display: inline-block;
  margin-right: 0.375em;
  margin-bottom: 0;
  cursor: pointer;
}

[type="checkbox"]:indeterminate {
  --background-color: var(--primary);
  --border-color: var(--primary);
  background-image: var(--icon-minus);
  background-position: center;
  background-size: 0.75em auto;
  background-repeat: no-repeat;
}

[type="radio"] {
  border-radius: 50%;
}
[type="radio"]:checked,
[type="radio"]:checked:active,
[type="radio"]:checked:focus {
  --background-color: var(--primary-inverse);
  border-width: 0.35em;
  background-image: none;
}

[type="checkbox"][role="switch"] {
  --background-color: var(--switch-background-color);
  --border-color: var(--switch-background-color);
  --color: var(--switch-color);
  width: 2.25em;
  height: 1.25em;
  border: var(--border-width) solid var(--border-color);
  border-radius: 1.25em;
  background-color: var(--background-color);
  line-height: 1.25em;
}
[type="checkbox"][role="switch"]:focus {
  --background-color: var(--switch-background-color);
  --border-color: var(--switch-background-color);
}
[type="checkbox"][role="switch"]:checked {
  --background-color: var(--switch-checked-background-color);
  --border-color: var(--switch-checked-background-color);
}
[type="checkbox"][role="switch"]:before {
  display: block;
  width: calc(1.25em - (var(--border-width) * 2));
  height: 100%;
  border-radius: 50%;
  background-color: var(--color);
  content: "";
  transition: margin 0.1s ease-in-out;
}
[type="checkbox"][role="switch"]:checked {
  background-image: none;
}
[type="checkbox"][role="switch"]:checked::before {
  margin-left: calc(1.125em - var(--border-width));
  -webkit-margin-start: calc(1.125em - var(--border-width));
  margin-inline-start: calc(1.125em - var(--border-width));
}

[type="checkbox"][aria-invalid="false"],
[type="checkbox"]:checked[aria-invalid="false"],
[type="radio"][aria-invalid="false"],
[type="radio"]:checked[aria-invalid="false"],
[type="checkbox"][role="switch"][aria-invalid="false"],
[type="checkbox"][role="switch"]:checked[aria-invalid="false"] {
  --border-color: var(--form-element-valid-border-color);
}
[type="checkbox"][aria-invalid="true"],
[type="checkbox"]:checked[aria-invalid="true"],
[type="radio"][aria-invalid="true"],
[type="radio"]:checked[aria-invalid="true"],
[type="checkbox"][role="switch"][aria-invalid="true"],
[type="checkbox"][role="switch"]:checked[aria-invalid="true"] {
  --border-color: var(--form-element-invalid-border-color);
}

/**
 * Form elements
 * Alternatives input types (Not Checkboxes & Radios)
 */
[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}
[type="color"]::-moz-focus-inner {
  padding: 0;
}
[type="color"]::-webkit-color-swatch {
  border: 0;
  border-radius: calc(var(--border-radius) * 0.5);
}
[type="color"]::-moz-color-swatch {
  border: 0;
  border-radius: calc(var(--border-radius) * 0.5);
}

input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]):is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
  --icon-position: 0.75rem;
  --icon-width: 1rem;
  padding-right: calc(var(--icon-width) + var(--icon-position));
  background-image: var(--icon-date);
  background-position: center right var(--icon-position);
  background-size: var(--icon-width) auto;
  background-repeat: no-repeat;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="time"] {
  background-image: var(--icon-time);
}

[type="date"]::-webkit-calendar-picker-indicator,
[type="datetime-local"]::-webkit-calendar-picker-indicator,
[type="month"]::-webkit-calendar-picker-indicator,
[type="time"]::-webkit-calendar-picker-indicator,
[type="week"]::-webkit-calendar-picker-indicator {
  width: var(--icon-width);
  margin-right: calc(var(--icon-width) * -1);
  margin-left: var(--icon-position);
  opacity: 0;
}

[dir="rtl"]
  :is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
  text-align: right;
}

[type="file"] {
  --color: var(--muted-color);
  padding: calc(var(--form-element-spacing-vertical) * 0.5) 0;
  border: 0;
  border-radius: 0;
  background: none;
}
[type="file"]::file-selector-button {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) / 2);
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::file-selector-button:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}
[type="file"]::-webkit-file-upload-button {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) / 2);
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  -webkit-transition: background-color var(--transition),
    border-color var(--transition), color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::-webkit-file-upload-button:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}
[type="file"]::-ms-browse {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  margin-inline-start: 0;
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  -ms-transition: background-color var(--transition),
    border-color var(--transition), color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::-ms-browse:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}

[type="range"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  height: 1.25rem;
  background: none;
}
[type="range"]::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -webkit-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-moz-range-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -moz-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-ms-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -ms-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -webkit-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]::-moz-range-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -moz-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]::-ms-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -ms-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]:hover,
[type="range"]:focus {
  --range-border-color: var(--range-active-border-color);
  --range-thumb-color: var(--range-thumb-hover-color);
}
[type="range"]:active {
  --range-thumb-color: var(--range-thumb-active-color);
}
[type="range"]:active::-webkit-slider-thumb {
  transform: scale(1.25);
}
[type="range"]:active::-moz-range-thumb {
  transform: scale(1.25);
}
[type="range"]:active::-ms-thumb {
  transform: scale(1.25);
}

input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
  -webkit-padding-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
  padding-inline-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
  border-radius: 5rem;
  background-image: var(--icon-search);
  background-position: center left 1.125rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
  -webkit-padding-start: calc(
    var(--form-element-spacing-horizontal) + 1.75rem
  ) !important;
  padding-inline-start: calc(
    var(--form-element-spacing-horizontal) + 1.75rem
  ) !important;
  background-position: center left 1.125rem, center right 0.75rem;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="false"] {
  background-image: var(--icon-search), var(--icon-valid);
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="true"] {
  background-image: var(--icon-search), var(--icon-invalid);
}

[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
  display: none;
}

[dir="rtl"]
  :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
  background-position: center right 1.125rem;
}
[dir="rtl"]
  :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
  background-position: center right 1.125rem, center left 0.75rem;
}

/**
 * Table
 */
:where(table) {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  text-indent: 0;
}

th,
td {
  padding: calc(var(--spacing) / 2) var(--spacing);
  border-bottom: var(--border-width) solid var(--table-border-color);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  text-align: left;
  text-align: start;
}

tfoot th,
tfoot td {
  border-top: var(--border-width) solid var(--table-border-color);
  border-bottom: 0;
}

table[role="grid"] tbody tr:nth-child(odd) {
  background-color: var(--table-row-stripped-background-color);
}

/**
 * Code
 */
pre,
code,
kbd,
samp {
  font-size: 0.875em;
  font-family: var(--font-family);
}

pre {
  -ms-overflow-style: scrollbar;
  overflow: auto;
}

pre,
code,
kbd {
  border-radius: var(--border-radius);
  background: var(--code-background-color);
  color: var(--code-color);
  font-weight: var(--font-weight);
  line-height: initial;
}

code,
kbd {
  display: inline-block;
  padding: 0.375rem 0.5rem;
}

pre {
  display: block;
  margin-bottom: var(--spacing);
  overflow-x: auto;
}
pre > code {
  display: block;
  padding: var(--spacing);
  background: none;
  font-size: 14px;
  line-height: var(--line-height);
}

code b {
  color: var(--code-tag-color);
  font-weight: var(--font-weight);
}
code i {
  color: var(--code-property-color);
  font-style: normal;
}
code u {
  color: var(--code-value-color);
  text-decoration: none;
}
code em {
  color: var(--code-comment-color);
  font-style: normal;
}

kbd {
  background-color: var(--code-kbd-background-color);
  color: var(--code-kbd-color);
  vertical-align: baseline;
}

/**
 * Miscs
 */
hr {
  height: 0;
  border: 0;
  border-top: 1px solid var(--muted-border-color);
  color: inherit;
}

[hidden],
template {
  display: none !important;
}

canvas {
  display: inline-block;
}

/**
 * Accordion (<details>)
 */
details {
  display: block;
  margin-bottom: var(--spacing);
  padding-bottom: var(--spacing);
  border-bottom: var(--border-width) solid var(--accordion-border-color);
}
details summary {
  line-height: 1rem;
  list-style-type: none;
  cursor: pointer;
  transition: color var(--transition);
}
details summary:not([role]) {
  color: var(--accordion-close-summary-color);
}
details summary::-webkit-details-marker {
  display: none;
}
details summary::marker {
  display: none;
}
details summary::-moz-list-bullet {
  list-style-type: none;
}
details summary::after {
  display: block;
  width: 1rem;
  height: 1rem;
  -webkit-margin-start: calc(var(--spacing, 1rem) * 0.5);
  margin-inline-start: calc(var(--spacing, 1rem) * 0.5);
  float: right;
  transform: rotate(-90deg);
  background-image: var(--icon-chevron);
  background-position: right center;
  background-size: 1rem auto;
  background-repeat: no-repeat;
  content: "";
  transition: transform var(--transition);
}
details summary:focus {
  outline: none;
}
details summary:focus:not([role="button"]) {
  color: var(--accordion-active-summary-color);
}
details summary[role="button"] {
  width: 100%;
  text-align: left;
}
details summary[role="button"]::after {
  height: calc(1rem * var(--line-height, 1.5));
  background-image: var(--icon-chevron-button);
}
details summary[role="button"]:not(.outline).contrast::after {
  background-image: var(--icon-chevron-button-inverse);
}
details[open] > summary {
  margin-bottom: calc(var(--spacing));
}
details[open] > summary:not([role]):not(:focus) {
  color: var(--accordion-open-summary-color);
}
details[open] > summary::after {
  transform: rotate(0);
}

[dir="rtl"] details summary {
  text-align: right;
}
[dir="rtl"] details summary::after {
  float: left;
  background-position: left center;
}

/**
 * Card (<article>)
 */
article {
  margin: var(--block-spacing-vertical) 0;
  padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
  border-radius: var(--border-radius);
  background: var(--card-background-color);
  box-shadow: var(--card-box-shadow);
}
article > header,
article > footer {
  margin-right: calc(var(--block-spacing-horizontal) * -1);
  margin-left: calc(var(--block-spacing-horizontal) * -1);
  padding: calc(var(--block-spacing-vertical) * 0.66)
    var(--block-spacing-horizontal);
  background-color: var(--card-sectionning-background-color);
}
article > header {
  margin-top: calc(var(--block-spacing-vertical) * -1);
  margin-bottom: var(--block-spacing-vertical);
  border-bottom: var(--border-width) solid var(--card-border-color);
  border-top-right-radius: var(--border-radius);
  border-top-left-radius: var(--border-radius);
}
article > footer {
  margin-top: var(--block-spacing-vertical);
  margin-bottom: calc(var(--block-spacing-vertical) * -1);
  border-top: var(--border-width) solid var(--card-border-color);
  border-bottom-right-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}

/**
 * Modal (<dialog>)
 */
#mount {
  --scrollbar-width: 0px;
}

dialog {
  display: flex;
  z-index: 999;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  align-items: center;
  justify-content: center;
  width: inherit;
  min-width: 100%;
  height: inherit;
  min-height: 100%;
  padding: var(--spacing);
  border: 0;
  -webkit-backdrop-filter: var(--modal-overlay-backdrop-filter);
  backdrop-filter: var(--modal-overlay-backdrop-filter);
  background-color: var(--modal-overlay-background-color);
  color: var(--color);
}
dialog article {
  max-height: calc(100vh - var(--spacing) * 2);
  overflow: auto;
}
@media (min-width: 576px) {
  dialog article {
    max-width: 510px;
  }
}
@media (min-width: 768px) {
  dialog article {
    max-width: 700px;
  }
}
dialog article > header,
dialog article > footer {
  padding: calc(var(--block-spacing-vertical) * 0.5)
    var(--block-spacing-horizontal);
}
dialog article > header .close {
  margin: 0;
  margin-left: var(--spacing);
  float: right;
}
dialog article > footer {
  text-align: right;
}
dialog article > footer [role="button"] {
  margin-bottom: 0;
}
dialog article > footer [role="button"]:not(:first-of-type) {
  margin-left: calc(var(--spacing) * 0.5);
}
dialog article p:last-of-type {
  margin: 0;
}
dialog article .close {
  display: block;
  width: 1rem;
  height: 1rem;
  margin-top: calc(var(--block-spacing-vertical) * -0.5);
  margin-bottom: var(--typography-spacing-vertical);
  margin-left: auto;
  background-image: var(--icon-close);
  background-position: center;
  background-size: auto 1rem;
  background-repeat: no-repeat;
  opacity: 0.5;
  transition: opacity var(--transition);
}
dialog article .close:is([aria-current], :hover, :active, :focus) {
  opacity: 1;
}
dialog:not([open]),
dialog[open="false"] {
  display: none;
}

.modal-is-open {
  padding-right: var(--scrollbar-width, 0px);
  overflow: hidden;
  pointer-events: none;
}
.modal-is-open dialog {
  pointer-events: auto;
}

:where(.modal-is-opening, .modal-is-closing) dialog,
:where(.modal-is-opening, .modal-is-closing) dialog > article {
  animation-duration: 0.2s;
  animation-timing-function: ease-in-out;
  animation-fill-mode: both;
}
:where(.modal-is-opening, .modal-is-closing) dialog {
  animation-duration: 0.8s;
  animation-name: modal-overlay;
}
:where(.modal-is-opening, .modal-is-closing) dialog > article {
  animation-delay: 0.2s;
  animation-name: modal;
}

.modal-is-closing dialog,
.modal-is-closing dialog > article {
  animation-delay: 0s;
  animation-direction: reverse;
}

@keyframes modal-overlay {
  from {
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
    background-color: transparent;
  }
}
@keyframes modal {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
}
/**
 * Nav
 */
:where(nav li)::before {
  float: left;
  content: "​";
}

nav,
nav ul {
  display: flex;
}

nav {
  justify-content: space-between;
}
nav ol,
nav ul {
  align-items: center;
  margin-bottom: 0;
  padding: 0;
  list-style: none;
}
nav ol:first-of-type,
nav ul:first-of-type {
  margin-left: calc(var(--nav-element-spacing-horizontal) * -1);
}
nav ol:last-of-type,
nav ul:last-of-type {
  margin-right: calc(var(--nav-element-spacing-horizontal) * -1);
}
nav li {
  display: inline-block;
  margin: 0;
  padding: var(--nav-element-spacing-vertical)
    var(--nav-element-spacing-horizontal);
}
nav li > * {
  --spacing: 0;
}
nav :where(a, [role="link"]) {
  display: inline-block;
  margin: calc(var(--nav-link-spacing-vertical) * -1)
    calc(var(--nav-link-spacing-horizontal) * -1);
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
  border-radius: var(--border-radius);
  text-decoration: none;
}
nav :where(a, [role="link"]):is([aria-current], :hover, :active, :focus) {
  text-decoration: none;
}
nav[aria-label="breadcrumb"] {
  align-items: center;
  justify-content: start;
}
nav[aria-label="breadcrumb"] ul li:not(:first-child) {
  -webkit-margin-start: var(--nav-link-spacing-horizontal);
  margin-inline-start: var(--nav-link-spacing-horizontal);
}
nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
  position: absolute;
  width: calc(var(--nav-link-spacing-horizontal) * 2);
  -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) / 2);
  margin-inline-start: calc(var(--nav-link-spacing-horizontal) / 2);
  content: "/";
  color: var(--muted-color);
  text-align: center;
}
nav[aria-label="breadcrumb"] a[aria-current] {
  background-color: transparent;
  color: inherit;
  text-decoration: none;
  pointer-events: none;
}
nav [role="button"] {
  margin-right: inherit;
  margin-left: inherit;
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
}

aside nav,
aside ol,
aside ul,
aside li {
  display: block;
}
aside li {
  padding: calc(var(--nav-element-spacing-vertical) * 0.5)
    var(--nav-element-spacing-horizontal);
}
aside li a {
  display: block;
}
aside li [role="button"] {
  margin: inherit;
}

[dir="rtl"] nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
  content: "\\";
}

/**
 * Progress
 */
progress {
  display: inline-block;
  vertical-align: baseline;
}

progress {
  -webkit-appearance: none;
  -moz-appearance: none;
  display: inline-block;
  appearance: none;
  width: 100%;
  height: 0.5rem;
  margin-bottom: calc(var(--spacing) * 0.5);
  overflow: hidden;
  border: 0;
  border-radius: var(--border-radius);
  background-color: var(--progress-background-color);
  color: var(--progress-color);
}
progress::-webkit-progress-bar {
  border-radius: var(--border-radius);
  background: none;
}
progress[value]::-webkit-progress-value {
  background-color: var(--progress-color);
}
progress::-moz-progress-bar {
  background-color: var(--progress-color);
}
@media (prefers-reduced-motion: no-preference) {
  progress:indeterminate {
    background: var(--progress-background-color)
      linear-gradient(
        to right,
        var(--progress-color) 30%,
        var(--progress-background-color) 30%
      )
      top left/150% 150% no-repeat;
    animation: progress-indeterminate 1s linear infinite;
  }
  progress:indeterminate[value]::-webkit-progress-value {
    background-color: transparent;
  }
  progress:indeterminate::-moz-progress-bar {
    background-color: transparent;
  }
}

@media (prefers-reduced-motion: no-preference) {
  [dir="rtl"] progress:indeterminate {
    animation-direction: reverse;
  }
}

@keyframes progress-indeterminate {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
/**
 * Dropdown ([role="list"])
 */
details[role="list"],
li[role="list"] {
  position: relative;
}

details[role="list"] summary + ul,
li[role="list"] > ul {
  display: flex;
  z-index: 99;
  position: absolute;
  top: auto;
  right: 0;
  left: 0;
  flex-direction: column;
  margin: 0;
  padding: 0;
  border: var(--border-width) solid var(--dropdown-border-color);
  border-radius: var(--border-radius);
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  background-color: var(--dropdown-background-color);
  box-shadow: var(--card-box-shadow);
  color: var(--dropdown-color);
  white-space: nowrap;
}
details[role="list"] summary + ul li,
li[role="list"] > ul li {
  width: 100%;
  margin-bottom: 0;
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    var(--form-element-spacing-horizontal);
  list-style: none;
}
details[role="list"] summary + ul li:first-of-type,
li[role="list"] > ul li:first-of-type {
  margin-top: calc(var(--form-element-spacing-vertical) * 0.5);
}
details[role="list"] summary + ul li:last-of-type,
li[role="list"] > ul li:last-of-type {
  margin-bottom: calc(var(--form-element-spacing-vertical) * 0.5);
}
details[role="list"] summary + ul li a,
li[role="list"] > ul li a {
  display: block;
  margin: calc(var(--form-element-spacing-vertical) * -0.5)
    calc(var(--form-element-spacing-horizontal) * -1);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    var(--form-element-spacing-horizontal);
  overflow: hidden;
  color: var(--dropdown-color);
  text-decoration: none;
  text-overflow: ellipsis;
}
details[role="list"] summary + ul li a:hover,
li[role="list"] > ul li a:hover {
  background-color: var(--dropdown-hover-background-color);
}

details[role="list"] summary::after,
li[role="list"] > a::after {
  display: block;
  width: 1rem;
  height: calc(1rem * var(--line-height, 1.5));
  -webkit-margin-start: 0.5rem;
  margin-inline-start: 0.5rem;
  float: right;
  transform: rotate(0deg);
  background-position: right center;
  background-size: 1rem auto;
  background-repeat: no-repeat;
  content: "";
}

details[role="list"] {
  padding: 0;
  border-bottom: none;
}
details[role="list"] summary {
  margin-bottom: 0;
}
details[role="list"] summary:not([role]) {
  height: calc(
    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
      var(--border-width) * 2
  );
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--form-element-border-color);
  border-radius: var(--border-radius);
  background-color: var(--form-element-background-color);
  color: var(--form-element-placeholder-color);
  line-height: inherit;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
details[role="list"] summary:not([role]):active,
details[role="list"] summary:not([role]):focus {
  border-color: var(--form-element-active-border-color);
  background-color: var(--form-element-active-background-color);
}
details[role="list"] summary:not([role]):focus {
  box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
}
details[role="list"][open] summary {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
details[role="list"][open] summary::before {
  display: block;
  z-index: 1;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: none;
  content: "";
  cursor: default;
}

nav details[role="list"] summary,
nav li[role="list"] a {
  display: flex;
  direction: ltr;
}

nav details[role="list"] summary + ul,
nav li[role="list"] > ul {
  min-width: -moz-fit-content;
  min-width: fit-content;
  border-radius: var(--border-radius);
}
nav details[role="list"] summary + ul li a,
nav li[role="list"] > ul li a {
  border-radius: 0;
}

nav details[role="list"] summary,
nav details[role="list"] summary:not([role]) {
  height: auto;
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
}
nav details[role="list"][open] summary {
  border-radius: var(--border-radius);
}
nav details[role="list"] summary + ul {
  margin-top: var(--outline-width);
  -webkit-margin-start: 0;
  margin-inline-start: 0;
}
nav details[role="list"] summary[role="link"] {
  margin-bottom: calc(var(--nav-link-spacing-vertical) * -1);
  line-height: var(--line-height);
}
nav details[role="list"] summary[role="link"] + ul {
  margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
  -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) * -1);
  margin-inline-start: calc(var(--nav-link-spacing-horizontal) * -1);
}

li[role="list"]:hover > ul,
li[role="list"] a:active ~ ul,
li[role="list"] a:focus ~ ul {
  display: flex;
}
li[role="list"] > ul {
  display: none;
  margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
  -webkit-margin-start: calc(
    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
  );
  margin-inline-start: calc(
    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
  );
}
li[role="list"] > a::after {
  background-image: var(--icon-chevron);
}

/**
 * Loading ([aria-busy=true])
 */
[aria-busy="true"] {
  cursor: progress;
}

[aria-busy="true"]:not(input, select, textarea)::before {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 0.1875em solid currentColor;
  border-radius: 1em;
  border-right-color: transparent;
  content: "";
  vertical-align: text-bottom;
  vertical-align: -0.125em;
  animation: spinner 0.75s linear infinite;
  opacity: var(--loading-spinner-opacity);
}
[aria-busy="true"]:not(input, select, textarea):not(:empty)::before {
  margin-right: calc(var(--spacing) * 0.5);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) * 0.5);
  margin-inline-end: calc(var(--spacing) * 0.5);
}
[aria-busy="true"]:not(input, select, textarea):empty {
  text-align: center;
}

button[aria-busy="true"],
input[type="submit"][aria-busy="true"],
input[type="button"][aria-busy="true"],
input[type="reset"][aria-busy="true"],
a[aria-busy="true"] {
  pointer-events: none;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}
/**
 * Tooltip ([data-tooltip])
 */
[data-tooltip] {
  position: relative;
}
[data-tooltip]:not(a, button, input) {
  border-bottom: 1px dotted;
  text-decoration: none;
  cursor: help;
}
[data-tooltip][data-placement="top"]::before,
[data-tooltip][data-placement="top"]::after,
[data-tooltip]::before,
[data-tooltip]::after {
  display: block;
  z-index: 99;
  position: absolute;
  bottom: 100%;
  left: 50%;
  padding: 0.25rem 0.5rem;
  overflow: hidden;
  transform: translate(-50%, -0.25rem);
  border-radius: var(--border-radius);
  background: var(--tooltip-background-color);
  content: attr(data-tooltip);
  color: var(--tooltip-color);
  font-style: normal;
  font-weight: var(--font-weight);
  font-size: 0.875rem;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
}
[data-tooltip][data-placement="top"]::after,
[data-tooltip]::after {
  padding: 0;
  transform: translate(-50%, 0rem);
  border-top: 0.3rem solid;
  border-right: 0.3rem solid transparent;
  border-left: 0.3rem solid transparent;
  border-radius: 0;
  background-color: transparent;
  content: "";
  color: var(--tooltip-background-color);
}
[data-tooltip][data-placement="bottom"]::before,
[data-tooltip][data-placement="bottom"]::after {
  top: 100%;
  bottom: auto;
  transform: translate(-50%, 0.25rem);
}
[data-tooltip][data-placement="bottom"]:after {
  transform: translate(-50%, -0.3rem);
  border: 0.3rem solid transparent;
  border-bottom: 0.3rem solid;
}
[data-tooltip][data-placement="left"]::before,
[data-tooltip][data-placement="left"]::after {
  top: 50%;
  right: 100%;
  bottom: auto;
  left: auto;
  transform: translate(-0.25rem, -50%);
}
[data-tooltip][data-placement="left"]:after {
  transform: translate(0.3rem, -50%);
  border: 0.3rem solid transparent;
  border-left: 0.3rem solid;
}
[data-tooltip][data-placement="right"]::before,
[data-tooltip][data-placement="right"]::after {
  top: 50%;
  right: auto;
  bottom: auto;
  left: 100%;
  transform: translate(0.25rem, -50%);
}
[data-tooltip][data-placement="right"]:after {
  transform: translate(-0.3rem, -50%);
  border: 0.3rem solid transparent;
  border-right: 0.3rem solid;
}
[data-tooltip]:focus::before,
[data-tooltip]:focus::after,
[data-tooltip]:hover::before,
[data-tooltip]:hover::after {
  opacity: 1;
}
@media (hover: hover) and (pointer: fine) {
  [data-tooltip][data-placement="bottom"]:focus::before,
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::before,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
  [data-tooltip]:hover::before,
  [data-tooltip]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-top;
  }
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
  [data-tooltip]:hover::after {
    animation-name: tooltip-caret-slide-top;
  }
  [data-tooltip][data-placement="bottom"]:focus::before,
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover::before,
  [data-tooltip][data-placement="bottom"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-bottom;
  }
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover::after {
    animation-name: tooltip-caret-slide-bottom;
  }
  [data-tooltip][data-placement="left"]:focus::before,
  [data-tooltip][data-placement="left"]:focus::after,
  [data-tooltip][data-placement="left"]:hover::before,
  [data-tooltip][data-placement="left"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-left;
  }
  [data-tooltip][data-placement="left"]:focus::after,
  [data-tooltip][data-placement="left"]:hover::after {
    animation-name: tooltip-caret-slide-left;
  }
  [data-tooltip][data-placement="right"]:focus::before,
  [data-tooltip][data-placement="right"]:focus::after,
  [data-tooltip][data-placement="right"]:hover::before,
  [data-tooltip][data-placement="right"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-right;
  }
  [data-tooltip][data-placement="right"]:focus::after,
  [data-tooltip][data-placement="right"]:hover::after {
    animation-name: tooltip-caret-slide-right;
  }
}
@keyframes tooltip-slide-top {
  from {
    transform: translate(-50%, 0.75rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -0.25rem);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-top {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -0.25rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0rem);
    opacity: 1;
  }
}
@keyframes tooltip-slide-bottom {
  from {
    transform: translate(-50%, -0.75rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0.25rem);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-bottom {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -0.5rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -0.3rem);
    opacity: 1;
  }
}
@keyframes tooltip-slide-left {
  from {
    transform: translate(0.75rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(-0.25rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-left {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(0.05rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(0.3rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-slide-right {
  from {
    transform: translate(-0.75rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(0.25rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-right {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-0.05rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(-0.3rem, -50%);
    opacity: 1;
  }
}

/**
 * Accessibility & User interaction
 */
[aria-controls] {
  cursor: pointer;
}

[aria-disabled="true"],
[disabled] {
  cursor: not-allowed;
}

[aria-hidden="false"][hidden] {
  display: initial;
}

[aria-hidden="false"][hidden]:not(:focus) {
  clip: rect(0, 0, 0, 0);
  position: absolute;
}

a,
area,
button,
input,
label,
select,
summary,
textarea,
[tabindex] {
  -ms-touch-action: manipulation;
}

[dir="rtl"] {
  direction: rtl;
}

/**
* Reduce Motion Features
*/
@media (prefers-reduced-motion: reduce) {
  *:not([aria-busy="true"]),
  :not([aria-busy="true"])::before,
  :not([aria-busy="true"])::after {
    background-attachment: initial !important;
    animation-duration: 1ms !important;
    animation-delay: -1ms !important;
    animation-iteration-count: 1 !important;
    scroll-behavior: auto !important;
    transition-delay: 0s !important;
    transition-duration: 0s !important;
  }
}

#mount#mount {
  /* --primary: rgb(227, 59, 126); */
  --primary: #ea4c89;
  --primary-hover: #f082ac;
  --icon-xia: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9IkZyYW1lIj4KPHBhdGggaWQ9IlZlY3RvciIgZD0iTTguMDAyOTEgOS42Nzk4M0wzLjgzMzM5IDUuNTEyMjFMMy4wMjUzOSA2LjMxOTgzTDguMDAzMjkgMTEuMjk1MUwxMi45NzYyIDYuMzE5ODNMMTIuMTY3OSA1LjUxMjIxTDguMDAyOTEgOS42Nzk4M1oiIGZpbGw9IiM4MzgzODMiLz4KPC9nPgo8L3N2Zz4K");
  --switch-checked-background-color: var(--primary);
}

li.select-link.select-link:hover > ul {
  display: none;
}
li.select-link.select-link > ul {
  display: none;
}
li.select-link.select-link a:focus ~ ul {
  display: none;
}

li.select-link.select-link a:active ~ ul {
  display: none;
}
li.select-link-active.select-link-active > ul {
  display: flex;
}
li.select-link-active.select-link-active:hover > ul {
  display: flex;
}

li.select-link-active.select-link-active a:focus ~ ul {
  display: flex;
}

li.select-link-active.select-link-active a:active ~ ul {
  display: flex;
}
ul.select-link-ul.select-link-ul {
  right: 0px;
  left: auto;
}

a.select-link-selected {
  background-color: var(--primary-focus);
}
.immersive-translate-no-select {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Old versions of Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
}

/* li[role="list"].no-arrow > a::after { */
/*   background-image: none; */
/*   width: 0; */
/*   color: var(--color); */
/* } */
li[role="list"].no-arrow {
  margin-left: 8px;
  padding-right: 0;
}
li[role="list"] > a::after {
  -webkit-margin-start: 0.2rem;
  margin-inline-start: 0.2rem;
}

li[role="list"].no-arrow > a,
li[role="list"].no-arrow > a:link,
li[role="list"].no-arrow > a:visited {
  color: var(--secondary);
}

select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 4px;
  max-width: 128px;
  overflow: hidden;
  color: var(--primary);
  font-size: 13px;
  border: none;
  padding: 0;
  padding-right: 20px;
  padding-left: 8px;
  text-overflow: ellipsis;
  color: var(--color);

}
select.min-select-secondary {
  color: var(--color);
}
select.min-select:focus {
  outline: none;
  border: none;
  --box-shadow: none;
}
select.min-select-no-arrow {
  background-image: none;
  padding-right: 0;
}

select.min-select-left {
  padding-right: 0px;
  /* padding-left: 24px; */
  /* background-position: center left 0; */
  text-overflow: ellipsis;
  text-align: left;
}

.muted {
  color: var(--muted-color);
}

.select.button-select {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
  --color: var(--secondary-inverse);
  cursor: pointer;
  --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 16px;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
  -webkit-appearance: button;
  margin: 0;
  margin-bottom: 0px;
  overflow: visible;
  font-family: inherit;
  text-transform: none;
}

body {
  padding: 0;
  margin: 0 auto;
  min-width: 268px;
  border-radius: 10px;
}

.popup-container {
  font-size: 16px;
  --font-size: 16px;
  color: #666;
  background-color: var(--popup-footer-background-color);
  width: 316px;
  min-width: 316px;
}

.popup-content {
  background-color: var(--popup-content-background-color);
  border-radius: 0px 0px 12px 12px;
  padding: 16px 20px;
}

.immersive-translate-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  touch-action: none;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 10px;
  border: 1px solid var(--muted-border-color);
}

#mount#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 4px;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 16px;
  --typography-spacing-vertical: 24px;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 12px;
  --form-element-spacing-horizontal: 16px;
  --nav-element-spacing-vertical: 16px;
  --nav-element-spacing-horizontal: 8px;
  --nav-link-spacing-vertical: 8px;
  --nav-link-spacing-horizontal: 8px;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(4px);
}

[data-theme="light"],
#mount:not([data-theme="dark"]) {
  --popup-footer-background-color: #e8eaeb;
  --popup-content-background-color: #ffffff;
  --popup-item-background-color: #f3f5f6;
  --popup-item-hover-background-color: #eaeced;
  --popup-trial-pro-background-color: #f9fbfc;
  --text-black-2: #222222;
  --text-gray-2: #222222;
  --text-gray-6: #666666;
  --text-gray-9: #999999;
  --text-gray-c2: #c2c2c2;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(75, 76, 77, 0.2);
  --service-select-border-color: #fafafa;
  --service-select-selected-background-color: #f3f5f6;
}

@media only screen and (prefers-color-scheme: dark) {
  #mount:not([data-theme="light"]) {
    --popup-footer-background-color: #0d0d0d;
    --popup-content-background-color: #191919;
    --popup-item-background-color: #272727;
    --popup-item-hover-background-color: #333333;
    --popup-trial-pro-background-color: #222222;
    --text-black-2: #ffffff;
    --text-gray-2: #dbdbdb;
    --text-gray-6: #b3b3b3;
    --text-gray-9: #777777;
    --text-gray-c2: #5b5b5b;
    --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.9);
    --service-select-border-color: #2c2c2c;
    --service-select-selected-background-color: #333333;
  }
}

[data-theme="dark"] {
  --popup-footer-background-color: #0d0d0d;
  --popup-content-background-color: #191919;
  --popup-item-background-color: #272727;
  --popup-item-hover-background-color: #333333;
  --popup-trial-pro-background-color: #222222;
  --text-black-2: #ffffff;
  --text-gray-2: #dbdbdb;
  --text-gray-6: #b3b3b3;
  --text-gray-9: #777777;
  --text-gray-c2: #5b5b5b;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.9);
  --service-select-border-color: #2c2c2c;
  --service-select-selected-background-color: #333333;
}

.text-balck {
  color: var(--text-black-2);
}

.text-gray-2 {
  color: var(--text-gray-2);
}

.text-gray-6 {
  color: var(--text-gray-6);
}

.text-gray-9 {
  color: var(--text-gray-9);
}

.text-gray-c2 {
  color: var(--text-gray-c2);
}

#mount {
  min-width: 268px;
}

.main-button {
  font-size: 15px;
  vertical-align: middle;
  border-radius: 12px;
  padding: unset;
  height: 44px;
  line-height: 44px;
}

.pt-4 {
  padding-top: 16px;
}

.p-2 {
  padding: 8px;
}

.pl-5 {
  padding-left: 48px;
}

.p-0 {
  padding: 0;
}

.pl-2 {
  padding-left: 8px;
}

.pl-4 {
  padding-left: 24px;
}

.pt-2 {
  padding-top: 8px;
}

.pb-2 {
  padding-bottom: 8px;
}

.pb-4 {
  padding-bottom: 16px;
}

.pb-5 {
  padding-bottom: 20px;
}

.pr-5 {
  padding-right: 48px;
}

.text-sm {
  font-size: 13px;
}

.text-base {
  font-size: 16px;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-end {
  justify-content: flex-end;
}

.flex-grow {
  flex-grow: 1;
}

.justify-between {
  justify-content: space-between;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 12px;
}

.inline-block {
  display: inline-block;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.py-2-5 {
  padding-top: 6px;
  padding-bottom: 6px;
}

.mt-0 {
  margin-top: 0;
}

.mt-2 {
  margin-top: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.mt-4 {
  margin-top: 16px;
}

.mt-5 {
  margin-top: 20px;
}

.mt-6 {
  margin-top: 24px;
}

.mb-1 {
  margin-bottom: 4px;
}

.ml-4 {
  margin-left: 24px;
}

.ml-3 {
  margin-left: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.ml-1 {
  margin-left: 4px;
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}

.mr-3 {
  margin-right: 16px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.pl-3 {
  padding-left: 12px;
}

.pr-3 {
  padding-right: 12px;
}

.p-3 {
  padding: 12px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.pt-3 {
  padding-top: 12px;
}

.px-6 {
  padding-left: 18px;
  padding-right: 18px;
}

.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.pt-6 {
  padding-top: 20px;
}

.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.left-auto {
  left: auto !important;
}

.max-h-28 {
  max-height: 112px;
}

.max-h-30 {
  max-height: 120px;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.text-xs {
  font-size: 12px;
}

.flex-1 {
  flex: 1;
}

.flex-3 {
  flex: 3;
}

.flex-4 {
  flex: 4;
}

.flex-2 {
  flex: 2;
}

.items-center {
  align-items: center;
}

.max-content {
  width: max-content;
}

.justify-center {
  justify-content: center;
}

.items-end {
  align-items: flex-end;
}

.items-baseline {
  align-items: baseline;
}

.my-5 {
  margin-top: 48px;
  margin-bottom: 48px;
}

.my-4 {
  margin-top: 24px;
  margin-bottom: 24px;
}

.my-3 {
  margin-top: 16px;
  margin-bottom: 16px;
}

.pt-3 {
  padding-top: 12px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.pt-2 {
  padding-top: 8px;
}

.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}

.pt-1 {
  padding-top: 4px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.pb-2 {
  padding-bottom: 8px;
}

.justify-end {
  justify-content: flex-end;
}

.w-auto {
  width: auto;
}

.shrink-0 {
  flex-shrink: 0;
}

select.language-select,
select.translate-service,
select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 0px;
  max-width: unset;
  flex: 1;
  overflow: hidden;
  font-size: 13px;
  border: none;
  border-radius: 8px;
  padding-right: 30px;
  padding-left: 0px;
  background-position: center right 12px;
  background-size: 16px auto;
  background-image: var(--icon-xia);
  text-overflow: ellipsis;
  color: var(--text-gray-2);
  background-color: transparent;
  box-shadow: unset !important;
  cursor: pointer;
}

select.more {
  background-position: center right;
  padding-right: 20px;
}

select.transform-padding-left {
  padding-left: 12px;
  transform: translateX(-12px);
  background-position: center right 0px;
}

select.translate-service {
  color: var(--text-black-2);
}

/* dark use black, for windows */
@media (prefers-color-scheme: dark) {
  select.language-select option,
  select.translate-service option,
  select.min-select option {
    background-color: #666666;
  }
}

.text-overflow-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.max-w-20 {
  max-width: 180px;
  white-space: nowrap;
}

select.min-select-secondary {
  color: var(--color);
}

select.min-select:focus {
  outline: none;
  border: none;
  --box-shadow: none;
}

select.min-select-no-arrow {
  background-image: none;
  padding-right: 0;
}

select.min-select-left {
  padding-right: 0px;
  /* padding-left: 24px; */
  /* background-position: center left 0; */
  text-overflow: ellipsis;
  text-align: left;
}

.popup-footer {
  background-color: var(--popup-footer-background-color);
  height: 40px;
}

.text-right {
  text-align: right;
}

.clickable {
  cursor: pointer;
}

.close {
  cursor: pointer;
  width: 16px;
  height: 16px;
  background-image: var(--icon-close);
  background-position: center;
  background-size: auto 1rem;
  background-repeat: no-repeat;
  opacity: 0.5;
  transition: opacity var(--transition);
}

.padding-two-column {
  padding-left: 40px;
  padding-right: 40px;
}

.muted {
  color: #999;
}

.text-label {
  color: #666;
}

.display-none {
  display: none;
}

/* dark use #18232c */
@media (prefers-color-scheme: dark) {
  .text-label {
    color: #9ca3af;
  }
}

.text-decoration-none {
  text-decoration: none;
}

.text-decoration-none:is([aria-current], :hover, :active, :focus),
[role="link"]:is([aria-current], :hover, :active, :focus) {
  --text-decoration: none !important;
  background-color: transparent !important;
}

.language-select-container {
  position: relative;
  width: 100%;
  background-color: var(--popup-item-background-color);
  height: 55px;
  border-radius: 12px;
}

select.language-select {
  color: var(--text-black-2);
  font-size: 14px;
  padding: 8px 24px 24px 16px;
  position: absolute;
  border-radius: 12px;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

select.text-gray-6 {
  color: var(--text-gray-6);
}

.language-select-container label {
  position: absolute;
  bottom: 10px;
  left: 16px;
  font-size: 12px;
  color: var(--text-gray-9);
  line-height: 12px;
  margin: 0;
}

.translation-service-container {
  background-color: var(--popup-item-background-color);
  border-radius: 12px;
}

.min-select-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44px;
  background-color: var(--popup-item-background-color);
  padding-left: 16px;
}

.min-select-container:first-child {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.min-select-container:last-child {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.min-select-container:only-child {
  border-radius: 10px;
}

.translate-mode {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background-color: var(--popup-item-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
}

.translate-mode svg {
  fill: var(--text-gray-2);
}

.widgets-container {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  width: 100%;
  gap: 9px;
}

.widget-item {
  display: flex;
  max-width: 70px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--popup-item-background-color);
  font-size: 12px;
  min-height: 59px;
  height: 100%;
  border-radius: 8px;
  cursor: pointer;
  flex: 1;
  padding: 8px 4px;
  text-align: center;
}

.widget-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 100%;
  margin-bottom: 4px;
}

.widget-title-container {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  height: 24px;
  width: 100%;
  padding-bottom: 4px;
}

.widget-icon {
  margin-bottom: 4px;
  display: flex;
  justify-content: center;
}

.widget-title {
  color: var(--text-gray-6);
  font-size: 12px;
  text-align: center;
  width: 100%;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 2px 2px;
}

.widget-item svg {
  fill: var(--text-gray-2);
}

.share-button-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 2px 3px 0 8px;
}

.share-button-container svg {
  fill: var(--text-gray-9);
}

.min-select-container:hover,
.language-select-container:hover,
.widget-item:hover,
.translate-mode:hover {
  background-color: var(--popup-item-hover-background-color);
}

.main-button:hover {
  background-color: #f5508f;
}

.share-button-container:hover {
  background-color: var(--popup-item-background-color);
  border-radius: 6px;
}

.error-boundary {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  display: flex;
  padding: 12px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
  word-break: break-all;
  margin: 12px;
  border-radius: 12px;
  flex-direction: column;
}

.upgrade-pro {
  border-radius: 11px;
  background: linear-gradient(57deg, #272727 19.8%, #696969 82.2%);
  padding: 2px 8px;
  transform: scale(0.85);
}

.upgrade-pro span {
  background: linear-gradient(180deg, #ffeab4 17.65%, #f8c235 85.29%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 12px;
  margin-left: 4px;
}

.upgrade-pro svg {
  margin-top: -2px;
}

.upgrade-pro:hover {
  background: linear-gradient(57deg, #3d3d3d 19.8%, #949494 82.2%);
}

.border-bottom-radius-0 {
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.trial-pro-container {
  border-radius: 0px 0px 12px 12px;
  background: var(--popup-trial-pro-background-color);
  display: flex;
  align-items: center;
  height: 44px;
  padding-left: 16px;
  padding-right: 12px;
  font-size: 12px;
}

.trial-pro-container label {
  line-height: 13px;
  color: var(--text-black-2);
}

.trial-pro-container img {
  margin-left: 5px;
}

.cursor-pointer {
  cursor: pointer;
}

.upgrade-pro-discount-act {
  height: 25px;
  display: flex;
  padding: 0 4px;
  align-items: center;
  border-radius: 15px;
  background: linear-gradient(
    90deg,
    #cefbfa 11.33%,
    #d7f56f 63.75%,
    #fccd5e 100%
  );
  transform: scale(0.9);
  box-shadow: 0px 1.8px 3.6px 0px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.upgrade-pro-discount-act span {
  font-size: 12px;
  font-weight: 700;
  margin-left: 4px;
  color: #222222;
}

.upgrade-pro-discount-act:hover {
  text-decoration: unset;
  background: linear-gradient(
    90deg,
    #e2fffe 11.33%,
    #e6ff91 63.75%,
    #ffdf93 100%
  );
}

.custom-select-container {
  width: 200px;
  position: relative;
  flex: 1;
}

#translation-service-select {
  padding-right: 12px;
  padding-left: 6px;
}

.custom-select-content {
  border-radius: 12px;
  background: var(--popup-content-background-color);
  box-shadow: var(--service-select-content-shadow);
  border: 1px solid var(--service-select-border-color);
  padding: 4px 5px;
  position: absolute;
  left: 0;
  right: 0;
  z-index: 100;
  overflow-y: auto;
}

.custom-select-item.default {
  width: 100%;
  padding: 0;
}

.custom-select-item {
  font-size: 13px;
  padding: 5px 6px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--text-black-2);
  width: auto;
  overflow: hidden;
  height: 30px;
  line-height: 30px;
}

.custom-select-item-img {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}

@media (prefers-color-scheme: dark) {
  .custom-select-item-img {
    margin-right: 6px;
  }
}

.custom-select-content .custom-select-item.selected,
.custom-select-content .custom-select-item:hover {
  background: var(--service-select-selected-background-color);
}

.custom-select-item > span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-select-item-pro {
  font-size: 12px;
  margin-left: 6px;
}

.custom-select-item-pro img {
  margin: 0 3px;
  width: 20px;
}

.custom-select-group-header {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-gray-9);
  padding: 6px 8px 4px;
  margin-top: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.more-container {
  position: relative;
}

.new-menu-indicator {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #ef3434;
  border-radius: 50%;
  right: 18px;
  top: 4px;
}

html {
  font-size: 17px;
}

@media print {
  .imt-fb-container {
    display: none !important;
  }
}

#mount#mount {
  position: absolute;
  display: none;
  min-width: 250px;
  height: auto;
  --font-size: 17px;
  font-size: 17px;
}

/* float-ball */
.imt-fb-container {
  position: fixed;
  padding: 0;
  top: 335px;
  width: fit-content;
  display: flex;
  flex-direction: column;
  display: none;
  direction: ltr;
}

.imt-fb-container.left {
  align-items: flex-start;
  left: 0;
}

.imt-fb-container.right {
  align-items: flex-end;
  right: 0;
}

.imt-fb-btn {
  cursor: pointer;
  background: var(--float-ball-more-button-background-color);
  height: 36px;
  width: 56px;
  box-shadow: 2px 6px 10px 0px #0e121629;
}

.imt-fb-btn.left {
  border-top-right-radius: 36px;
  border-bottom-right-radius: 36px;
}

.imt-fb-btn.right {
  border-top-left-radius: 36px;
  border-bottom-left-radius: 36px;
}

.imt-fb-btn div {
  background: var(--float-ball-more-button-background-color);
  height: 36px;
  width: 54px;
  display: flex;
  align-items: center;
}

.imt-fb-btn.left div {
  border-top-right-radius: 34px;
  border-bottom-right-radius: 34px;
  justify-content: flex-end;
}

.imt-fb-btn.right div {
  border-top-left-radius: 34px;
  border-bottom-left-radius: 34px;
}

.imt-fb-logo-img {
  width: 20px;
  height: 20px;
  margin: 0 10px;
}

.imt-fb-logo-img-big-bg {
  width: 28px;
  height: 28px;
  margin: 0;
  padding: 4px;
  background-color: #ed6d8f;
  border-radius: 50%;
  margin: 0 5px;
}

.imt-float-ball-translated {
  position: absolute;
  width: 11px;
  height: 11px;
  bottom: 4px;
  right: 20px;
}

.btn-animate {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-transition: -webkit-transform ease-out 250ms;
  transition: -webkit-transform ease-out 250ms;
  transition: transform ease-out 250ms;
  transition: transform ease-out 250ms, -webkit-transform ease-out 250ms;
}

.imt-fb-setting-btn {
  margin-right: 18px;
  width: 28px;
  height: 28px;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 20px;
  box-shadow: 2px 10px 24px 0px #0e121614;
  border: none;
}

.popup-container {
  border-radius: 20px;
}

.popup-content {
  border-radius: 20px 20px 12px 12px;
}
.popup-footer {
  border-radius: 20px;
}

.imt-fb-close-button {
  pointer-events: all;
  cursor: pointer;
  position: absolute;
  margin-top: -10px;
}

.imt-fb-close-content {
  padding: 22px;
  width: 320px;
  pointer-events: all;
}

.imt-fb-close-title {
  font-weight: 500;
  color: var(--h2-color);
}

.imt-fb-close-radio-content {
  background-color: var(--background-light-green);
  padding: 8px 20px;
}

.imt-fb-radio-sel,
.imt-fb-radio-nor {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  flex-shrink: 0;
}

.imt-fb-radio-sel {
  border: 2px solid var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.imt-fb-radio-sel div {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: var(--primary);
}

.imt-fb-radio-nor {
  border: 2px solid #d3d4d6;
}

.imt-fb-primary-btn {
  background-color: var(--primary);
  width: 72px;
  height: 32px;
  color: white;
  border-radius: 8px;
  text-align: center;
  line-height: 32px;
  font-size: 16px;
  cursor: pointer;
}

.imt-fb-default-btn {
  border: 1px solid var(--primary);
  width: 72px;
  height: 32px;
  border-radius: 8px;
  color: var(--primary);
  line-height: 32px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
}

.imt-fb-guide-container {
  width: 312px;
  transform: translateY(-45%);
}

.imt-fb-guide-bg {
  position: absolute;
  left: 30px;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  height: 100%;
  width: 90%;
}

.imt-fb-guide-bg.left {
  transform: scaleX(-1);
}

.imt-fb-guide-content {
  margin: 16px -30px 80px 0px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.imt-fb-guide-content.left {
  margin: 16px 21px 60px 32px;
}

.imt-fb-guide-img {
  width: 220px;
  height: 112px;
}

.imt-fb-guide-message {
  font-size: 16px;
  line-height: 28px;
  color: #333333;
  white-space: pre-wrap;
  text-align: center;
  font-weight: 700;
  margin-bottom: 20px;
}

.imt-fb-guide-button {
  margin-top: 16px;
  line-height: 40px;
  height: 40px;
  padding: 0 20px;
  width: unset;
}

.imt-fb-more-buttons {
  box-shadow: 0px 2px 10px 0px #00000014;
  border: none;
  background: var(--float-ball-more-button-background-color);
  width: 36px;
  display: flex;
  flex-direction: column;
  border-radius: 18px;
  margin-top: 0px;
  padding: 7px 0 7px 0;
}

.imt-fb-more-buttons > div {
  margin: auto;
}

.imt-fb-side,
.imt-fb-reward {
  border-radius: 50%;
  cursor: pointer;
  pointer-events: all;
  position: relative;
}

.imt-fb-side {
  margin: 10px 0;
}

.imt-fb-new-badge {
  width: 26px;
  height: 14px;
  padding: 3px;
  background-color: #f53f3f;
  border-radius: 4px;
  position: absolute;
  top: -5px;
  right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.imt-fb-side *,
.imt-fb-reward * {
  pointer-events: all;
}

.imt-fb-more-button {
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
/* Sheet.css */
.immersive-translate-sheet {
  position: fixed;
  transform: translateY(100%);
  /* Start off screen */
  left: 0;
  right: 0;
  background-color: white;
  transition: transform 0.3s ease-out;
  /* Smooth slide transition */
  box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.1);
  /* Ensure it's above other content */
  bottom: 0;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  overflow: hidden;
}

.immersive-translate-sheet.visible {
  transform: translateY(0);
}

.immersive-translate-sheet-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.immersive-translate-sheet-backdrop.visible {
  opacity: 1;
}

.popup-container-sheet {
  max-width: 100vw;
  width: 100vw;
}

.imt-no-events svg * {
  pointer-events: none !important;
}

.imt-manga-button {
  width: 36px;
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: all;
  margin: 0 0 10px 0;
  background-color: var(--float-ball-more-button-background-color);
  border-radius: 18px;
  filter: drop-shadow(0px 2px 10px rgba(0, 0, 0, 0.08));
  opacity: 0.5;
  right: 8px;
  padding: 10px 0 4px 0;
}

.imt-manga-feedback {
  cursor: pointer;
  margin-bottom: 10px;
}

.imt-fb-feedback {
  cursor: pointer;
  margin-top: 10px;
}

.imt-fb-upgrade-button {
  cursor: pointer;
  margin-top: 10px;
}

.imt-manga-button:hover {
  opacity: 1;
}

.imt-manga-translated {
  position: absolute;
  left: 24px;
  top: 20px;
}

.imt-float-ball-loading {
  animation: imt-loading-animation 0.6s infinite linear !important;
}

.imt-manga-guide-bg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  width: 372px;
  transform: translateY(-50%);
}
.imt-manga-guide-content {
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  margin: 0 40px 0;
}

.img-manga-guide-button {
  width: fit-content;
  margin: 16px auto;
}

.img-manga-close {
  position: absolute;
  bottom: -200px;
  width: 32px;
  height: 32px;
  left: 0;
  right: 0;
  margin: auto;
  cursor: pointer;
}

.imt-fb-container.dragging .imt-fb-more-buttons,
.imt-fb-container.dragging .imt-manga-button,
.imt-fb-container.dragging .btn-animate:not(.imt-fb-btn) {
  display: none !important;
}

.imt-fb-container.dragging .imt-fb-btn {
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: move !important;
}

.imt-fb-container.dragging .imt-fb-btn div {
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
}

.imt-fb-container.dragging .imt-fb-btn.left,
.imt-fb-container.dragging .imt-fb-btn.right {
  border-radius: 50% !important;
}

.imt-fb-container.dragging .imt-fb-btn.left div,
.imt-fb-container.dragging .imt-fb-btn.right div {
  border-radius: 50% !important;
}

.imt-fb-container.dragging .imt-fb-logo-img {
  margin: 0 !important;
  padding: 4px !important;
}

.imt-fb-container.dragging .imt-float-ball-translated {
  right: 2px !important;
  bottom: 2px !important;
}

@-webkit-keyframes imt-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes imt-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}
</style><div id="mount" style="display: block;"></div></template></div></html>