# 司法涉诉查询 H5 应用

基于 Vue 3 + Vant UI 开发的移动端司法涉诉查询应用，复用原有代码架构并适配新的接口字段。

## 功能特性

- ✅ 司法涉诉查询
- ✅ 基本信息查询
- ✅ 风险等级评估
- ✅ 案件详情展示
- ✅ 移动端响应式设计
- ✅ 数据可视化展示

## 技术栈

- **前端框架**: Vue 3
- **UI 组件库**: Vant UI 4.x
- **样式框架**: TailwindCSS
- **构建工具**: Vite
- **HTTP 客户端**: Axios
- **工具库**: @vueuse/core

## 项目结构

```
new/
├── src/
│   ├── api/                 # API 接口
│   │   └── index.js
│   ├── components/          # 组件
│   │   ├── CreditReportCard.vue    # 司法涉诉报告卡片
│   │   ├── CaseDetailCard.vue      # 案件详情卡片
│   │   ├── LTitle.vue              # 标题组件
│   │   └── LRemark.vue             # 提示组件
│   ├── views/               # 页面
│   │   └── CreditReport.vue        # 司法涉诉查询页面
│   ├── utils/               # 工具函数
│   │   └── index.js
│   ├── App.vue              # 根组件
│   ├── main.js              # 入口文件
│   └── style.css            # 全局样式
├── package.json
├── vite.config.js
├── tailwind.config.js
└── README.md
```

## 接口字段映射

### CreditReportResponse
```javascript
{
  casesList: Array<CaseDTO>,  // 案件集合
  idNumber: string,           // 身份证号
  name: string               // 姓名
}
```

### CaseDTO 字段说明
| 字段 | 类型 | 说明 |
|------|------|------|
| c_ah | string | 案号 |
| c_gkws_dsr | string | 当事人 |
| c_gkws_pjjg | string | 判决结果 |
| c_slfsxx | string | 审理方式信息 |
| c_ssdy | string | 所属地域 |
| d_jarq | string | 结案时间 |
| d_larq | string | 立案时间 (YYYY-MM-DD) |
| n_ajjzjd | string | 案件进展阶段 |
| n_ajlx | string | 案件类型 |
| n_jaay | string | 结案案由 |
| n_jaay_tag | string | 结案案由标签 |
| n_jaay_tree | string | 结案案由详细 |
| n_jabdje | number | 结案标的金额 |
| n_jafs | string | 结案方式 |
| n_jbfy | string | 经办法院 |
| n_jbfy_cj | string | 法院所属层级 |
| n_laay | string | 立案案由 |
| n_laay_tag | string | 立案案由标签 |
| n_laay_tree | string | 立案案由详细 |
| n_pj_victory | string | 胜诉估计 |
| n_slcx | string | 审理程序 |
| n_ssdw | string | 诉讼地位 |
| n_ssdw_ys | string | 一审诉讼地位 |

## 安装和运行

### 1. 安装依赖
```bash
cd new
npm install
```

### 2. 配置 API 地址
编辑 `vite.config.js` 中的代理配置：
```javascript
server: {
  proxy: {
    '/api': {
      target: 'http://your-api-domain.com', // 替换为您的API地址
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '')
    }
  }
}
```

### 3. 启动开发服务器
```bash
npm run dev
```

### 4. 构建生产版本
```bash
npm run build
```

## API 接口配置

### 司法涉诉查询接口
```javascript
// src/api/index.js
export const queryCreditReport = (params) => {
  return api.get('/super/admin/carer/credit/report', { params })
}
```

**请求参数:**
```javascript
{
  carerId: "110101199001011234"  // 身份证号
}
```

**响应格式:**
```javascript
{
  "data": {
    "name": "赵*东",
    "idNumber": "23108*****2428",
    "casesList": [
      {
        "c_ah": "(2015)海商初字第00515号",
        "n_ajlx": "民事一审",
        "n_ssdw": "被告",
        "n_laay_tree": "合同、准合同纠纷,合同纠纷,借款合同纠纷",
        "n_jabdje": 112842.0,
        // ... 其他字段
      }
    ]
  },
  "code": "0000",
  "message": "成功!",
  "hasErrors": false
}
```

## 风险等级评估规则

系统会根据案件信息自动评估风险等级：

- **高风险**: 包含刑事案件或被执行人记录
- **中风险**: 包含被告身份或债务纠纷
- **低风险**: 一般民事案件
- **无风险**: 无相关司法涉诉记录

## 自定义配置

### 1. 修改主题色彩
编辑 `tailwind.config.js`:
```javascript
theme: {
  extend: {
    colors: {
      primary: '#1989fa',    // 主色调
      success: '#07c160',    // 成功色
      warning: '#ff976a',    // 警告色
      danger: '#ee0a24',     // 危险色
    }
  }
}
```

### 2. 添加新的案件类型判断
编辑 `src/utils/index.js` 中的 `getCaseTypeColor` 函数。

### 3. 自定义风险评估规则
编辑 `src/utils/index.js` 中的 `getRiskLevel` 函数。

## 部署说明

1. 构建项目：`npm run build`
2. 将 `dist` 目录部署到 Web 服务器
3. 配置 Nginx 反向代理（如需要）
4. 确保 API 接口可正常访问

## 注意事项

1. 请确保后端 API 接口返回的数据格式与 `CreditReportResponse` 一致
2. 身份证号码会进行格式验证
3. 所有敏感信息请在生产环境中妥善处理
4. 建议在生产环境中启用 HTTPS

## 开发调试

开发环境下可以使用模拟数据进行调试，在 `src/views/CreditReport.vue` 中取消注释 `mockData()` 调用即可。
