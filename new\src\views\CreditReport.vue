<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 头部 -->
    <van-nav-bar 
      title="司法涉诉查询" 
      left-text="返回"
      left-arrow
      @click-left="onClickLeft"
      class="bg-white shadow-sm"
    />
    
    <!-- 查询表单 -->
    <div class="p-4">
      <div class="card">
        <h2 class="text-lg font-semibold mb-4 text-gray-800">查询信息</h2>
        
        <van-form @submit="onSubmit">
          <van-cell-group inset>
            <van-field
              v-model="queryForm.name"
              name="name"
              label="姓名"
              placeholder="请输入姓名"
              :rules="[{ required: true, message: '请输入姓名' }]"
            />
            <van-field
              v-model="queryForm.idNumber"
              name="idNumber"
              label="身份证号"
              placeholder="请输入身份证号"
              :rules="[
                { required: true, message: '请输入身份证号' },
              ]"
            />
          </van-cell-group>
          
          <div class="mt-4">
            <van-button 
              round 
              block 
              type="primary" 
              native-type="submit"
              :loading="loading"
              loading-text="查询中..."
            >
              开始查询
            </van-button>
          </div>
        </van-form>
      </div>
    </div>
    
    <!-- 查询结果 -->
    <div v-if="showResult" class="p-4">
      <CreditReportCard :report-data="reportData" />
    </div>
    
    <!-- 空状态 -->
    <van-empty 
      v-if="showEmpty"
      image="search" 
      description="暂无查询结果"
      class="mt-8"
    />
    
    <!-- 错误提示 -->
    <van-dialog
      v-model:show="showErrorDialog"
      title="查询失败"
      :message="errorMessage"
      show-cancel-button
      @confirm="showErrorDialog = false"
      @cancel="showErrorDialog = false"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { showToast, showLoadingToast, closeToast } from 'vant'
import CreditReportCard from '@/components/CreditReportCard.vue'
import { queryCreditReport } from '@/api'

// 响应式数据
const loading = ref(false)
const showResult = ref(false)
const showEmpty = ref(false)
const showErrorDialog = ref(false)
const errorMessage = ref('')
const reportData = ref({})

// 查询表单
const queryForm = reactive({
  name: '',
  idNumber: ''
})

// 计算属性
const hasData = computed(() => {
  return reportData.value.casesList && reportData.value.casesList.length > 0
})

// 方法
const onClickLeft = () => {
  // 返回上一页或首页
  if (window.history.length > 1) {
    window.history.back()
  } else {
    window.location.href = '/'
  }
}

const onSubmit = async (values) => {
  loading.value = true
  showResult.value = false
  showEmpty.value = false

  const loadingToast = showLoadingToast({
    message: '查询中...',
    forbidClick: true,
    duration: 0
  })

  try {
    const response = await queryCreditReport({
      carerId: values.idNumber
    })

    closeToast()

    // 根据实际API响应结构处理数据
    if (response && response.code === '0000' && response.data) {
      const data = response.data
      reportData.value = {
        name: data.name,
        idNumber: data.idNumber,
        casesList: data.casesList || []
      }
      showResult.value = true

      if (!data.casesList || data.casesList.length === 0) {
        showEmpty.value = true
        showToast({
          message: '未查询到相关司法涉诉记录',
          type: 'success'
        })
      } else {
        showToast({
          message: `查询成功，共找到 ${data.casesList.length} 条记录`,
          type: 'success'
        })
      }
    } else {
      // 处理业务错误
      const errorMsg = response?.message || '查询失败'
      showEmpty.value = true
      showToast({
        message: errorMsg,
        type: 'fail'
      })
    }
  } catch (error) {
    closeToast()
    console.error('查询失败:', error)

    let message = '查询失败，请稍后重试'
    if (error.response) {
      if (error.response.status === 400) {
        message = '请求参数错误，请检查输入信息'
      } else if (error.response.status === 401) {
        message = '身份验证失败，请重新登录'
      } else if (error.response.status === 403) {
        message = '没有查询权限'
      } else if (error.response.status === 404) {
        message = '查询服务不可用'
      } else if (error.response.status >= 500) {
        message = '服务器错误，请稍后重试'
      }
    } else if (error.code === 'NETWORK_ERROR') {
      message = '网络连接失败，请检查网络设置'
    }

    errorMessage.value = message
    showErrorDialog.value = true
  } finally {
    loading.value = false
  }
}

// 模拟数据（开发测试用）- 基于实际API响应格式
const mockData = () => {
  reportData.value = {
    name: '赵*东',
    idNumber: '23108*****2428',
    casesList: [
      {
        n_laay_tree: '合同、准合同纠纷,合同纠纷,借款合同纠纷',
        n_jaay: '合同、准合同纠纷',
        n_ssdw: '被告',
        n_jaay_tree: '合同、准合同纠纷,合同纠纷,借款合同纠纷',
        n_pj_victory: '未知',
        n_ssdw_ys: '被告',
        c_gkws_dsr: '原告中国农业银行股份有限公司海林市支行,住所地黑龙江省海林市。负责人牟景珩,该支行行长。委托代理人刘志毅,男,1961年10月18日出生,汉族,中国农业银行股份有限公司海林市支行职员,住所地黑龙江省海林市。被告唐桂茹,女,1952年6月1日出生,汉族,农民,住所地黑龙江省海林市。被告赵艳东,女,1977年7月17日出生,汉族,农民,住所地黑龙江省海林市。被告李富春,男,1962年1月30日出生,汉族,农民,住所地黑龙江省海林市。',
        n_jafs: '判决',
        n_jbfy_cj: '基层法院',
        n_slcx: '一审',
        n_ajjzjd: '已结案',
        n_jbfy: '海林市人民法院',
        d_jarq: '2016-02-29',
        c_slfsxx: '1,2016-02-14 09:00:00,第二审判庭,1',
        c_ah: '(2015)海商初字第00515号',
        c_gkws_pjjg: '被告唐桂茹在本判决生效之日起10日内给付原告中国农业银行股份有限公司海林市支行借款本金50000元及利息6421.02元,(从2013年11月13日起至2015年8月17日,年利率9%)2015年8月17日以后的利息按年利率9%标准计算至实际给付之日止;被告赵艳东在本判决生效之日起10日内给付原告中国农业银行股份有限公司海林市支行借款本金50000元及利息6421.02元,(从2013年11月13日起至2015年8月17日,年利率9%)2015年8月17日以后的利息按年利率9%标准计算至实际给付之日止;被告唐桂茹、赵艳东、李富春承担连带清偿责任;被告唐桂茹、赵艳东、李富春承担连带偿还责任后,有权向债务人追偿。案件受理费2556.84元,公告费600元,合计3156.84元,由被告负担。如果未按本判决指定的期间履行给付金钱义务,应当依照《中华人民共和国民事诉讼法》第二百五十三条之规定,加倍支付迟延履行期间的债务利息。如不服本判决,可在判决书送达之日起十五日内,向本院递交上诉状,并按对方当事人的人数提出副本,上诉于黑龙江省牡丹江市中级人民法院。',
        c_ssdy: '黑龙江省',
        n_laay_tag: '合同纠纷',
        n_jabdje: 112842.0,
        n_jaay_tag: '合同纠纷',
        n_ajlx: '民事一审',
        n_laay: '合同、准合同纠纷',
        d_larq: '2015-08-17'
      }
    ]
  }
  showResult.value = true
}

// 开发环境下可以使用模拟数据
if (import.meta.env.DEV) {
  // mockData()
}
</script>

<style scoped>
.van-nav-bar {
  --van-nav-bar-background: #fff;
  --van-nav-bar-title-text-color: #323233;
}
</style>
