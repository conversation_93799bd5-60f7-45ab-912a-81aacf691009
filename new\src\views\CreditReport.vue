<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 头部 -->
    <van-nav-bar 
      title="司法涉诉查询" 
      left-text="返回"
      left-arrow
      @click-left="onClickLeft"
      class="bg-white shadow-sm"
    />
    
    <!-- 查询表单 -->
    <div class="p-4">
      <div class="card">
        <h2 class="text-lg font-semibold mb-4 text-gray-800">查询信息</h2>
        
        <van-form @submit="onSubmit">
          <van-cell-group inset>
            <van-field
              v-model="queryForm.name"
              name="name"
              label="姓名"
              placeholder="请输入姓名"
              :rules="[{ required: true, message: '请输入姓名' }]"
            />
            <van-field
              v-model="queryForm.idNumber"
              name="idNumber"
              label="身份证号"
              placeholder="请输入身份证号"
              :rules="[
                { required: true, message: '请输入身份证号' },
              ]"
            />
          </van-cell-group>
          
          <div class="mt-4">
            <van-button 
              round 
              block 
              type="primary" 
              native-type="submit"
              :loading="loading"
              loading-text="查询中..."
            >
              开始查询
            </van-button>
          </div>
        </van-form>
      </div>
    </div>
    
    <!-- 查询结果 -->
    <div v-if="showResult" class="p-4">
      <CreditReportCard :report-data="reportData" />
    </div>
    
    <!-- 空状态 -->
    <van-empty 
      v-if="showEmpty"
      image="search" 
      description="暂无查询结果"
      class="mt-8"
    />
    
    <!-- 错误提示 -->
    <van-dialog
      v-model:show="showErrorDialog"
      title="查询失败"
      :message="errorMessage"
      show-cancel-button
      @confirm="showErrorDialog = false"
      @cancel="showErrorDialog = false"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { showToast, showLoadingToast, closeToast } from 'vant'
import CreditReportCard from '@/components/CreditReportCard.vue'
import { queryCreditReport } from '@/api'

// 响应式数据
const loading = ref(false)
const showResult = ref(false)
const showEmpty = ref(false)
const showErrorDialog = ref(false)
const errorMessage = ref('')
const reportData = ref({})

// 查询表单
const queryForm = reactive({
  name: '',
  idNumber: ''
})

// 计算属性
const hasData = computed(() => {
  return reportData.value.casesList && reportData.value.casesList.length > 0
})

// 方法
const onClickLeft = () => {
  // 返回上一页或首页
  if (window.history.length > 1) {
    window.history.back()
  } else {
    window.location.href = '/'
  }
}

const onSubmit = async (values) => {
  loading.value = true
  showResult.value = false
  showEmpty.value = false
  
  const loadingToast = showLoadingToast({
    message: '查询中...',
    forbidClick: true,
    duration: 0
  })
  
  try {
    const response = await queryCreditReport({
      name: values.name,
      carerId: values.idNumber
    })
    
    closeToast()
    
    if (response && response.casesList) {
      reportData.value = response
      showResult.value = true
      
      if (response.casesList.length === 0) {
        showEmpty.value = true
        showToast({
          message: '未查询到相关司法涉诉记录',
          type: 'success'
        })
      } else {
        showToast({
          message: `查询成功，共找到 ${response.casesList.length} 条记录`,
          type: 'success'
        })
      }
    } else {
      showEmpty.value = true
      showToast({
        message: '未查询到相关记录',
        type: 'success'
      })
    }
  } catch (error) {
    closeToast()
    console.error('查询失败:', error)
    
    let message = '查询失败，请稍后重试'
    if (error.response) {
      if (error.response.status === 400) {
        message = '请求参数错误，请检查输入信息'
      } else if (error.response.status === 401) {
        message = '身份验证失败，请重新登录'
      } else if (error.response.status === 403) {
        message = '没有查询权限'
      } else if (error.response.status === 404) {
        message = '查询服务不可用'
      } else if (error.response.status >= 500) {
        message = '服务器错误，请稍后重试'
      }
    } else if (error.code === 'NETWORK_ERROR') {
      message = '网络连接失败，请检查网络设置'
    }
    
    errorMessage.value = message
    showErrorDialog.value = true
  } finally {
    loading.value = false
  }
}

// 模拟数据（开发测试用）
const mockData = () => {
  reportData.value = {
    name: '张三',
    idNumber: '110101199001011234',
    casesList: [
      {
        c_ah: '(2023)京0101民初12345号',
        c_gkws_dsr: '张三、李四',
        c_gkws_pjjg: '判决被告张三于本判决生效之日起十日内向原告李四支付借款本金50000元及利息',
        c_slfsxx: '公开审理',
        c_ssdy: '北京市东城区',
        d_jarq: '2023-06-15',
        d_larq: '2023-03-10',
        n_ajjzjd: '一审已结案',
        n_ajlx: '民事案件',
        n_jaay: '民间借贷纠纷',
        n_jaay_tag: '合同纠纷',
        n_jaay_tree: '合同、无因管理、不当得利纠纷/合同纠纷/借款合同纠纷/民间借贷纠纷',
        n_jabdje: 50000,
        n_jafs: '判决',
        n_jbfy: '北京市东城区人民法院',
        n_jbfy_cj: '基层法院',
        n_laay: '民间借贷纠纷',
        n_laay_tag: '合同纠纷',
        n_laay_tree: '合同、无因管理、不当得利纠纷/合同纠纷/借款合同纠纷/民间借贷纠纷',
        n_pj_victory: '败诉',
        n_slcx: '普通程序',
        n_ssdw: '被告',
        n_ssdw_ys: '被告'
      }
    ]
  }
  showResult.value = true
}

// 开发环境下可以使用模拟数据
if (import.meta.env.DEV) {
  // mockData()
}
</script>

<style scoped>
.van-nav-bar {
  --van-nav-bar-background: #fff;
  --van-nav-bar-title-text-color: #323233;
}
</style>
