import{u as Z}from"./use-id-BHlr6Txk.js";import{B as W,C as H,g as t,bo as q,E as J,G as Q,bp as k,ao as I,L as X,e as O,aR as tt,N as G,aL as U,f as et,o as nt,bq as it,O as at,p as lt,Y as rt}from"./index-B4d74vWj.js";import{t as st,i as ot}from"./BaseReport-ClT0Rj5U.js";const[ct,z]=W("empty"),dt={image:J("default"),imageSize:[Number,String,Array],description:String};var ut=H({name:ct,props:dt,setup(e,{slots:l}){const i=()=>{const o=l.description?l.description():e.description;if(o)return t("p",{class:z("description")},[o])},n=()=>{if(l.default)return t("div",{class:z("bottom")},[l.default()])},h=Z(),a=o=>`${h}-${o}`,r=o=>`url(#${a(o)})`,d=(o,f,b)=>t("stop",{"stop-color":o,offset:`${f}%`,"stop-opacity":b},null),u=(o,f)=>[d(o,0),d(f,100)],w=o=>[t("defs",null,[t("radialGradient",{id:a(o),cx:"50%",cy:"54%",fx:"50%",fy:"54%",r:"297%",gradientTransform:"matrix(-.16 0 0 -.33 .58 .72)","data-allow-mismatch":"attribute"},[d("#EBEDF0",0),d("#F2F3F5",100,.3)])]),t("ellipse",{fill:r(o),opacity:".8",cx:"80",cy:"140",rx:"46",ry:"8","data-allow-mismatch":"attribute"},null)],m=()=>[t("defs",null,[t("linearGradient",{id:a("a"),x1:"64%",y1:"100%",x2:"64%","data-allow-mismatch":"attribute"},[d("#FFF",0,.5),d("#F2F3F5",100)])]),t("g",{opacity:".8","data-allow-mismatch":"children"},[t("path",{d:"M36 131V53H16v20H2v58h34z",fill:r("a")},null),t("path",{d:"M123 15h22v14h9v77h-31V15z",fill:r("a")},null)])],v=()=>[t("defs",null,[t("linearGradient",{id:a("b"),x1:"64%",y1:"97%",x2:"64%",y2:"0%","data-allow-mismatch":"attribute"},[d("#F2F3F5",0,.3),d("#F2F3F5",100)])]),t("g",{opacity:".8","data-allow-mismatch":"children"},[t("path",{d:"M87 6c3 0 7 3 8 6a8 8 0 1 1-1 16H80a7 7 0 0 1-8-6c0-4 3-7 6-7 0-5 4-9 9-9Z",fill:r("b")},null),t("path",{d:"M19 23c2 0 3 1 4 3 2 0 4 2 4 4a4 4 0 0 1-4 3v1h-7v-1l-1 1c-2 0-3-2-3-4 0-1 1-3 3-3 0-2 2-4 4-4Z",fill:r("b")},null)])],y=()=>t("svg",{viewBox:"0 0 160 160"},[t("defs",{"data-allow-mismatch":"children"},[t("linearGradient",{id:a(1),x1:"64%",y1:"100%",x2:"64%"},[d("#FFF",0,.5),d("#F2F3F5",100)]),t("linearGradient",{id:a(2),x1:"50%",x2:"50%",y2:"84%"},[d("#EBEDF0",0),d("#DCDEE0",100,0)]),t("linearGradient",{id:a(3),x1:"100%",x2:"100%",y2:"100%"},[u("#EAEDF0","#DCDEE0")]),t("radialGradient",{id:a(4),cx:"50%",cy:"0%",fx:"50%",fy:"0%",r:"100%",gradientTransform:"matrix(0 1 -.54 0 .5 -.5)"},[d("#EBEDF0",0),d("#FFF",100,0)])]),t("g",{fill:"none"},[m(),t("path",{fill:r(4),d:"M0 139h160v21H0z","data-allow-mismatch":"attribute"},null),t("path",{d:"M80 54a7 7 0 0 1 3 13v27l-2 2h-2a2 2 0 0 1-2-2V67a7 7 0 0 1 3-13z",fill:r(2),"data-allow-mismatch":"attribute"},null),t("g",{opacity:".6","stroke-linecap":"round","stroke-width":"7","data-allow-mismatch":"children"},[t("path",{d:"M64 47a19 19 0 0 0-5 13c0 5 2 10 5 13",stroke:r(3)},null),t("path",{d:"M53 36a34 34 0 0 0 0 48",stroke:r(3)},null),t("path",{d:"M95 73a19 19 0 0 0 6-13c0-5-2-9-6-13",stroke:r(3)},null),t("path",{d:"M106 84a34 34 0 0 0 0-48",stroke:r(3)},null)]),t("g",{transform:"translate(31 105)"},[t("rect",{fill:"#EBEDF0",width:"98",height:"34",rx:"2"},null),t("rect",{fill:"#FFF",x:"9",y:"8",width:"80",height:"18",rx:"1.1"},null),t("rect",{fill:"#EBEDF0",x:"15",y:"12",width:"18",height:"6",rx:"1.1"},null)])])]),p=()=>t("svg",{viewBox:"0 0 160 160"},[t("defs",{"data-allow-mismatch":"children"},[t("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:a(5)},[u("#F2F3F5","#DCDEE0")]),t("linearGradient",{x1:"95%",y1:"48%",x2:"5.5%",y2:"51%",id:a(6)},[u("#EAEDF1","#DCDEE0")]),t("linearGradient",{y1:"45%",x2:"100%",y2:"54%",id:a(7)},[u("#EAEDF1","#DCDEE0")])]),m(),v(),t("g",{transform:"translate(36 50)",fill:"none"},[t("g",{transform:"translate(8)"},[t("rect",{fill:"#EBEDF0",opacity:".6",x:"38",y:"13",width:"36",height:"53",rx:"2"},null),t("rect",{fill:r(5),width:"64",height:"66",rx:"2","data-allow-mismatch":"attribute"},null),t("rect",{fill:"#FFF",x:"6",y:"6",width:"52",height:"55",rx:"1"},null),t("g",{transform:"translate(15 17)",fill:r(6),"data-allow-mismatch":"attribute"},[t("rect",{width:"34",height:"6",rx:"1"},null),t("path",{d:"M0 14h34v6H0z"},null),t("rect",{y:"28",width:"34",height:"6",rx:"1"},null)])]),t("rect",{fill:r(7),y:"61",width:"88",height:"28",rx:"1","data-allow-mismatch":"attribute"},null),t("rect",{fill:"#F7F8FA",x:"29",y:"72",width:"30",height:"6",rx:"1"},null)])]),x=()=>t("svg",{viewBox:"0 0 160 160"},[t("defs",null,[t("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:a(8),"data-allow-mismatch":"attribute"},[u("#EAEDF1","#DCDEE0")])]),m(),v(),w("c"),t("path",{d:"m59 60 21 21 21-21h3l9 9v3L92 93l21 21v3l-9 9h-3l-21-21-21 21h-3l-9-9v-3l21-21-21-21v-3l9-9h3Z",fill:r(8),"data-allow-mismatch":"attribute"},null)]),F=()=>t("svg",{viewBox:"0 0 160 160"},[t("defs",{"data-allow-mismatch":"children"},[t("linearGradient",{x1:"50%",y1:"100%",x2:"50%",id:a(9)},[u("#EEE","#D8D8D8")]),t("linearGradient",{x1:"100%",y1:"50%",y2:"50%",id:a(10)},[u("#F2F3F5","#DCDEE0")]),t("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:a(11)},[u("#F2F3F5","#DCDEE0")]),t("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:a(12)},[u("#FFF","#F7F8FA")])]),m(),v(),w("d"),t("g",{transform:"rotate(-45 113 -4)",fill:"none","data-allow-mismatch":"children"},[t("rect",{fill:r(9),x:"24",y:"52.8",width:"5.8",height:"19",rx:"1"},null),t("rect",{fill:r(10),x:"22.1",y:"67.3",width:"9.9",height:"28",rx:"1"},null),t("circle",{stroke:r(11),"stroke-width":"8",cx:"27",cy:"27",r:"27"},null),t("circle",{fill:r(12),cx:"27",cy:"27",r:"16"},null),t("path",{d:"M37 7c-8 0-15 5-16 12",stroke:r(11),"stroke-width":"3",opacity:".5","stroke-linecap":"round",transform:"rotate(45 29 13)"},null)])]),D=()=>{var o;if(l.image)return l.image();const f={error:x,search:F,network:y,default:p};return((o=f[e.image])==null?void 0:o.call(f))||t("img",{src:e.image},null)};return()=>t("div",{class:z()},[t("div",{class:z("image"),style:q(e.imageSize)},[D()]),i(),n()])}});const Mt=Q(ut),ht=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function ft(e){function l(n){return(...h)=>{if(!e.value)throw new Error("ECharts is not initialized yet.");return e.value[n].apply(e.value,h)}}function i(){const n=Object.create(null);return ht.forEach(h=>{n[h]=l(h)}),n}return i()}function mt(e,l,i){G([i,e,l],([n,h,a],r,d)=>{let u=null;if(n&&h&&a){const{offsetWidth:w,offsetHeight:m}=n,v=a===!0?{}:a,{throttle:y=100,onResize:p}=v;let x=!1;const F=()=>{h.resize(),p==null||p()},D=y?st(F,y):F;u=new ResizeObserver(()=>{!x&&(x=!0,n.offsetWidth===w&&n.offsetHeight===m)||D()}),u.observe(n)}d(()=>{u&&(u.disconnect(),u=null)})})}const pt={autoresize:[Boolean,Object]},gt=/^on[^a-z]/,j=e=>gt.test(e);function Et(e){const l={};for(const i in e)j(i)||(l[i]=e[i]);return l}function L(e,l){const i=at(e)?lt(e):e;return i&&typeof i=="object"&&"value"in i?i.value||l:i||l}const vt="ecLoadingOptions";function xt(e,l,i){const n=I(vt,{}),h=O(()=>({...L(n,{}),...i==null?void 0:i.value}));U(()=>{const a=e.value;a&&(l.value?a.showLoading(h.value):a.hideLoading())})}const Ft={loading:Boolean,loadingOptions:Object};let C=null;const R="x-vue-echarts";function wt(){if(C!=null)return C;if(typeof HTMLElement>"u"||typeof customElements>"u")return C=!1;try{new Function("tag","class EChartsElement extends HTMLElement{__dispose=null;disconnectedCallback(){this.__dispose&&(this.__dispose(),this.__dispose=null)}}customElements.get(tag)==null&&customElements.define(tag,EChartsElement);")(R)}catch{return C=!1}return C=!0}document.head.appendChild(document.createElement("style")).textContent=`x-vue-echarts{display:block;width:100%;height:100%;min-width:0}
`;const yt=wt(),bt="ecTheme",Ot="ecInitOptions",Dt="ecUpdateOptions",N=/(^&?~?!?)native:/;var Tt=H({name:"echarts",props:{option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean,...pt,...Ft},emits:{},inheritAttrs:!1,setup(e,{attrs:l}){const i=k(),n=k(),h=k(),a=I(bt,null),r=I(Ot,null),d=I(Dt,null),{autoresize:u,manualUpdate:w,loading:m,loadingOptions:v}=X(e),y=O(()=>h.value||e.option||null),p=O(()=>e.theme||L(a,{})),x=O(()=>e.initOptions||L(r,{})),F=O(()=>e.updateOptions||L(d,{})),D=O(()=>Et(l)),o={},f=tt().proxy.$listeners,b={};f?Object.keys(f).forEach(c=>{N.test(c)?o[c.replace(N,"$1")]=f[c]:b[c]=f[c]}):Object.keys(l).filter(c=>j(c)).forEach(c=>{let s=c.charAt(2).toLowerCase()+c.slice(3);if(s.indexOf("native:")===0){const _=`on${s.charAt(7).toUpperCase()}${s.slice(8)}`;o[_]=l[c];return}s.substring(s.length-4)==="Once"&&(s=`~${s.substring(0,s.length-4)}`),b[s]=l[c]});function M(c){if(!i.value)return;const s=n.value=ot(i.value,p.value,x.value);e.group&&(s.group=e.group),Object.keys(b).forEach(A=>{let g=b[A];if(!g)return;let E=A.toLowerCase();E.charAt(0)==="~"&&(E=E.substring(1),g.__once__=!0);let B=s;if(E.indexOf("zr:")===0&&(B=s.getZr(),E=E.substring(3)),g.__once__){delete g.__once__;const V=g;g=(...Y)=>{V(...Y),B.off(E,g)}}B.on(E,g)});function _(){s&&!s.isDisposed()&&s.resize()}function P(){const A=c||y.value;A&&s.setOption(A,F.value)}u.value?rt(()=>{_(),P()}):P()}function $(c,s){e.manualUpdate&&(h.value=c),n.value?n.value.setOption(c,s||{}):M(c)}function S(){n.value&&(n.value.dispose(),n.value=void 0)}let T=null;G(w,c=>{typeof T=="function"&&(T(),T=null),c||(T=G(()=>e.option,(s,_)=>{s&&(n.value?n.value.setOption(s,{notMerge:s!==_,...F.value}):M())},{deep:!0}))},{immediate:!0}),G([p,x],()=>{S(),M()},{deep:!0}),U(()=>{e.group&&n.value&&(n.value.group=e.group)});const K=ft(n);return xt(n,m,v),mt(n,u,i),et(()=>{M()}),nt(()=>{yt&&i.value?i.value.__dispose=S:S()}),{chart:n,root:i,setOption:$,nonEventAttrs:D,nativeListeners:o,...K}},render(){const e={...this.nonEventAttrs,...this.nativeListeners};return e.ref="root",e.class=e.class?["echarts"].concat(e.class):"echarts",it(R,e)}});export{Tt as E,Mt as a};
